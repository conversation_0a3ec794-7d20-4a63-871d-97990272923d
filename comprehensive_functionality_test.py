#!/usr/bin/env python3
"""
Comprehensive functionality test for all pages and features
Tests every endpoint, form, and functionality
"""

import requests
import json
import sys
import time
from datetime import datetime, date

# Configuration
BASE_URL = "http://localhost:8000/api"
FRONTEND_URL = "http://localhost:5173"
LOGIN_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "Shivam@109"
}

class ComprehensiveTester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.test_results = []
        self.created_items = {}  # Track created items for cleanup
        
    def log_test(self, test_name, success, message="", details=None):
        """Log test results with details"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if message:
            print(f"   {message}")
        if details and not success:
            print(f"   Details: {details}")
        
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message,
            'details': details
        })
    
    def authenticate(self):
        """Authenticate with the system"""
        print("🔐 Authenticating with system...")
        try:
            response = self.session.post(f"{BASE_URL}/auth/login", json=LOGIN_CREDENTIALS)
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('access_token')
                if self.token:
                    self.session.headers.update({'Authorization': f'Bearer {self.token}'})
                    self.log_test("Authentication", True, f"Logged in as {data['user']['first_name']} {data['user']['last_name']}")
                    return True
            
            self.log_test("Authentication", False, f"Status: {response.status_code}", response.text)
            return False
        except Exception as e:
            self.log_test("Authentication", False, f"Error: {str(e)}")
            return False
    
    def test_frontend_accessibility(self):
        """Test if frontend is accessible"""
        print("\n🌐 Testing Frontend Accessibility")
        print("-" * 40)
        
        try:
            response = requests.get(FRONTEND_URL, timeout=10)
            self.log_test("Frontend Server", response.status_code == 200, 
                         f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Frontend Server", False, f"Error: {str(e)}")
    
    def test_diamonds_crud(self):
        """Test complete diamonds CRUD operations"""
        print("\n💎 Testing Diamonds CRUD Operations")
        print("-" * 40)
        
        # Test GET diamonds
        try:
            response = self.session.get(f"{BASE_URL}/diamonds")
            self.log_test("Get Diamonds", response.status_code == 200, 
                         f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                diamonds = data.get('data', [])
                self.log_test("Diamonds Data Structure", isinstance(diamonds, list),
                             f"Found {len(diamonds)} diamonds")
                
                # Test enhanced fields
                if diamonds:
                    diamond = diamonds[0]
                    enhanced_fields = ['cost_price', 'retail_price', 'profit_margin', 'cut_grade']
                    found_fields = [f for f in enhanced_fields if f in diamond]
                    self.log_test("Enhanced Diamond Fields", len(found_fields) > 0,
                                 f"Found: {found_fields}")
        except Exception as e:
            self.log_test("Get Diamonds", False, f"Error: {str(e)}")
        
        # Test POST diamond (create)
        try:
            new_diamond = {
                "shape_id": 1,
                "carat": 1.5,
                "color": "D",
                "clarity": "VVS1",
                "cut": "Excellent",
                "vendor_id": 1,
                "certificate_no": f"TEST{int(time.time())}",
                "cost_price": 50000,
                "retail_price": 75000,
                "cut_grade": "Excellent",
                "polish": "Excellent",
                "symmetry": "Excellent",
                "fluorescence": "None",
                "certification_lab": "GIA"
            }
            
            response = self.session.post(f"{BASE_URL}/diamonds", json=new_diamond)
            if response.status_code in [200, 201]:
                created_diamond = response.json()
                self.created_items['diamond'] = created_diamond.get('id')
                self.log_test("Create Diamond", True, "Diamond created successfully")
                
                # Test profit margin calculation
                if 'profit_margin' in created_diamond:
                    expected_margin = ((75000 - 50000) / 75000) * 100
                    actual_margin = created_diamond['profit_margin']
                    margin_correct = abs(expected_margin - actual_margin) < 0.01
                    self.log_test("Profit Margin Calculation", margin_correct,
                                 f"Expected: {expected_margin:.2f}%, Actual: {actual_margin:.2f}%")
            else:
                self.log_test("Create Diamond", False, f"Status: {response.status_code}", response.text)
                
        except Exception as e:
            self.log_test("Create Diamond", False, f"Error: {str(e)}")
    
    def test_vendors_crud(self):
        """Test complete vendors CRUD operations"""
        print("\n🏢 Testing Vendors CRUD Operations")
        print("-" * 40)
        
        # Test GET vendors
        try:
            response = self.session.get(f"{BASE_URL}/vendors")
            self.log_test("Get Vendors", response.status_code == 200,
                         f"Status: {response.status_code}")
            
            if response.status_code == 200:
                vendors = response.json()
                self.log_test("Vendors Data Structure", isinstance(vendors, list),
                             f"Found {len(vendors)} vendors")
        except Exception as e:
            self.log_test("Get Vendors", False, f"Error: {str(e)}")
        
        # Test POST vendor (create)
        try:
            new_vendor = {
                "name": f"Test Vendor {int(time.time())}",
                "company_name": "Test Company Ltd",
                "gst_number": "22AAAAA0000A1Z5",
                "contact_number": "+91-9876543210",
                "email": "<EMAIL>",
                "address": "123 Test Street, Test City",
                "city": "Mumbai",
                "state": "Maharashtra",
                "country": "India",
                "postal_code": "400001",
                "business_type": "Manufacturer",
                "credit_limit": 100000,
                "payment_terms": "30 days"
            }
            
            response = self.session.post(f"{BASE_URL}/vendors", json=new_vendor)
            if response.status_code in [200, 201]:
                created_vendor = response.json()
                self.created_items['vendor'] = created_vendor.get('id')
                self.log_test("Create Vendor", True, "Vendor created successfully")
            else:
                self.log_test("Create Vendor", False, f"Status: {response.status_code}", response.text)
                
        except Exception as e:
            self.log_test("Create Vendor", False, f"Error: {str(e)}")
    
    def test_manufacturing_crud(self):
        """Test complete manufacturing CRUD operations"""
        print("\n🔧 Testing Manufacturing CRUD Operations")
        print("-" * 40)
        
        # Test GET manufacturing
        try:
            response = self.session.get(f"{BASE_URL}/manufacturing")
            self.log_test("Get Manufacturing", response.status_code == 200,
                         f"Status: {response.status_code}")
            
            if response.status_code == 200:
                manufacturing = response.json()
                self.log_test("Manufacturing Data Structure", isinstance(manufacturing, list),
                             f"Found {len(manufacturing)} manufacturing requests")
        except Exception as e:
            self.log_test("Get Manufacturing", False, f"Error: {str(e)}")
        
        # Test POST manufacturing (create) - only if we have vendor and diamond
        if 'vendor' in self.created_items and 'diamond' in self.created_items:
            try:
                new_manufacturing = {
                    "vendor_id": self.created_items['vendor'],
                    "order_type": "Diamond Cutting",
                    "priority": "normal",
                    "description": "Test manufacturing order",
                    "sent_date": date.today().isoformat(),
                    "expected_return_date": "2024-02-01",
                    "estimated_cost": 5000,
                    "diamonds": [
                        {
                            "diamond_id": self.created_items['diamond'],
                            "quantity": 1,
                            "original_weight": 1.5
                        }
                    ]
                }
                
                response = self.session.post(f"{BASE_URL}/manufacturing", json=new_manufacturing)
                if response.status_code in [200, 201]:
                    created_manufacturing = response.json()
                    self.created_items['manufacturing'] = created_manufacturing.get('id')
                    self.log_test("Create Manufacturing", True, "Manufacturing request created")
                else:
                    self.log_test("Create Manufacturing", False, f"Status: {response.status_code}", response.text)
                    
            except Exception as e:
                self.log_test("Create Manufacturing", False, f"Error: {str(e)}")
    
    def test_jewelry_crud(self):
        """Test complete jewelry CRUD operations"""
        print("\n💍 Testing Jewelry CRUD Operations")
        print("-" * 40)
        
        # Test GET jewelry
        try:
            response = self.session.get(f"{BASE_URL}/jewelry")
            self.log_test("Get Jewelry", response.status_code == 200,
                         f"Status: {response.status_code}")
            
            if response.status_code == 200:
                jewelry = response.json()
                self.log_test("Jewelry Data Structure", isinstance(jewelry, list),
                             f"Found {len(jewelry)} jewelry items")
        except Exception as e:
            self.log_test("Get Jewelry", False, f"Error: {str(e)}")
        
        # Test POST jewelry (create) - only if we have vendor
        if 'vendor' in self.created_items:
            try:
                new_jewelry = {
                    "name": f"Test Ring {int(time.time())}",
                    "design_code": f"TR{int(time.time())}",
                    "vendor_id": self.created_items['vendor'],
                    "category": "Ring",
                    "subcategory": "Engagement",
                    "gross_weight": 5.5,
                    "net_weight": 4.8,
                    "metal_type": "Gold",
                    "metal_purity": "18K",
                    "metal_color": "Yellow",
                    "cost_price": 25000,
                    "retail_price": 40000,
                    "making_charges": 5000,
                    "size": "7",
                    "received_date": date.today().isoformat()
                }
                
                response = self.session.post(f"{BASE_URL}/jewelry", json=new_jewelry)
                if response.status_code in [200, 201]:
                    created_jewelry = response.json()
                    self.created_items['jewelry'] = created_jewelry.get('id')
                    self.log_test("Create Jewelry", True, "Jewelry item created")
                    
                    # Test profit margin calculation
                    if 'profit_margin' in created_jewelry:
                        total_cost = 25000 + 5000  # cost_price + making_charges
                        expected_margin = ((40000 - total_cost) / 40000) * 100
                        actual_margin = created_jewelry['profit_margin']
                        margin_correct = abs(expected_margin - actual_margin) < 0.01
                        self.log_test("Jewelry Profit Calculation", margin_correct,
                                     f"Expected: {expected_margin:.2f}%, Actual: {actual_margin:.2f}%")
                else:
                    self.log_test("Create Jewelry", False, f"Status: {response.status_code}", response.text)
                    
            except Exception as e:
                self.log_test("Create Jewelry", False, f"Error: {str(e)}")
    
    def test_sales_crud(self):
        """Test complete sales CRUD operations"""
        print("\n💰 Testing Sales CRUD Operations")
        print("-" * 40)
        
        # Test GET sales
        try:
            response = self.session.get(f"{BASE_URL}/sales")
            self.log_test("Get Sales", response.status_code == 200,
                         f"Status: {response.status_code}")
            
            if response.status_code == 200:
                sales = response.json()
                self.log_test("Sales Data Structure", isinstance(sales, list),
                             f"Found {len(sales)} sales records")
        except Exception as e:
            self.log_test("Get Sales", False, f"Error: {str(e)}")
        
        # Test POST sale (create) - only if we have jewelry
        if 'jewelry' in self.created_items:
            try:
                new_sale = {
                    "customer_name": "Test Customer",
                    "customer_email": "<EMAIL>",
                    "customer_phone": "+91-9876543210",
                    "customer_address": "123 Customer Street",
                    "customer_city": "Mumbai",
                    "customer_state": "Maharashtra",
                    "sale_date": date.today().isoformat(),
                    "sale_type": "retail",
                    "sales_channel": "store",
                    "subtotal": 40000,
                    "tax_percentage": 3,
                    "tax_amount": 1200,
                    "total_amount": 41200,
                    "payment_status": "paid",
                    "payment_method": "card",
                    "jewelry_id": self.created_items['jewelry']
                }
                
                response = self.session.post(f"{BASE_URL}/sales", json=new_sale)
                if response.status_code in [200, 201]:
                    created_sale = response.json()
                    self.created_items['sale'] = created_sale.get('id')
                    self.log_test("Create Sale", True, "Sale record created")
                else:
                    self.log_test("Create Sale", False, f"Status: {response.status_code}", response.text)
                    
            except Exception as e:
                self.log_test("Create Sale", False, f"Error: {str(e)}")
    
    def test_dashboard_analytics(self):
        """Test dashboard and analytics"""
        print("\n📊 Testing Dashboard & Analytics")
        print("-" * 40)
        
        try:
            response = self.session.get(f"{BASE_URL}/dashboard")
            if response.status_code == 200:
                dashboard = response.json()
                self.log_test("Dashboard API", True, "Dashboard data retrieved")
                
                # Check for key metrics
                expected_metrics = ['diamonds_in_stock', 'jewelry_in_stock', 'total_sales']
                found_metrics = [m for m in expected_metrics if m in dashboard]
                self.log_test("Dashboard Metrics", len(found_metrics) > 0,
                             f"Found metrics: {found_metrics}")
            else:
                self.log_test("Dashboard API", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("Dashboard API", False, f"Error: {str(e)}")
    
    def test_special_endpoints(self):
        """Test special endpoints like deduct-diamonds"""
        print("\n🔧 Testing Special Endpoints")
        print("-" * 40)
        
        # Test deduct-diamonds endpoint if we have jewelry and diamond
        if 'jewelry' in self.created_items and 'diamond' in self.created_items:
            try:
                deduct_data = {
                    "diamonds": [
                        {
                            "diamond_id": self.created_items['diamond'],
                            "quantity": 1
                        }
                    ]
                }
                
                response = self.session.post(
                    f"{BASE_URL}/jewelry/{self.created_items['jewelry']}/deduct-diamonds",
                    json=deduct_data
                )
                
                self.log_test("Deduct Diamonds Endpoint", 
                             response.status_code in [200, 201, 400],  # 400 might be expected if already deducted
                             f"Status: {response.status_code}")
                             
            except Exception as e:
                self.log_test("Deduct Diamonds Endpoint", False, f"Error: {str(e)}")
    
    def test_validation_rules(self):
        """Test validation rules and business logic"""
        print("\n✅ Testing Validation Rules")
        print("-" * 40)
        
        # Test invalid GST number
        try:
            invalid_vendor = {
                "name": "Invalid Vendor",
                "gst_number": "INVALID_GST",
                "contact_number": "invalid_phone",
                "address": "Test Address"
            }
            
            response = self.session.post(f"{BASE_URL}/vendors", json=invalid_vendor)
            validation_working = response.status_code in [400, 422]
            self.log_test("GST Validation", validation_working,
                         f"Correctly rejected invalid GST: {response.status_code}")
                         
        except Exception as e:
            self.log_test("GST Validation", False, f"Error: {str(e)}")
        
        # Test negative price validation
        try:
            invalid_diamond = {
                "shape_id": 1,
                "carat": 1.0,
                "color": "D",
                "clarity": "VVS1",
                "cut": "Excellent",
                "vendor_id": 1,
                "certificate_no": "INVALID_TEST",
                "cost_price": -1000,  # Negative price
                "retail_price": 5000
            }
            
            response = self.session.post(f"{BASE_URL}/diamonds", json=invalid_diamond)
            validation_working = response.status_code in [400, 422]
            self.log_test("Price Validation", validation_working,
                         f"Correctly rejected negative price: {response.status_code}")
                         
        except Exception as e:
            self.log_test("Price Validation", False, f"Error: {str(e)}")
    
    def cleanup_created_items(self):
        """Clean up created test items"""
        print("\n🧹 Cleaning up test data...")
        
        # Delete in reverse order to handle dependencies
        cleanup_order = ['sale', 'manufacturing', 'jewelry', 'diamond', 'vendor']
        
        for item_type in cleanup_order:
            if item_type in self.created_items:
                try:
                    item_id = self.created_items[item_type]
                    endpoint_map = {
                        'sale': 'sales',
                        'manufacturing': 'manufacturing',
                        'jewelry': 'jewelry',
                        'diamond': 'diamonds',
                        'vendor': 'vendors'
                    }
                    
                    endpoint = endpoint_map[item_type]
                    response = self.session.delete(f"{BASE_URL}/{endpoint}/{item_id}")
                    
                    if response.status_code in [200, 204, 404]:  # 404 is OK if already deleted
                        print(f"   ✅ Cleaned up {item_type} {item_id}")
                    else:
                        print(f"   ⚠️  Could not clean up {item_type} {item_id}: {response.status_code}")
                        
                except Exception as e:
                    print(f"   ❌ Error cleaning up {item_type}: {str(e)}")
    
    def generate_comprehensive_report(self):
        """Generate comprehensive test report"""
        print(f"\n" + "=" * 70)
        print(f"📊 COMPREHENSIVE FUNCTIONALITY TEST REPORT")
        print(f"=" * 70)
        
        # Calculate statistics
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"📈 Test Results Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {failed_tests}")
        print(f"   Success Rate: {success_rate:.1f}%")
        
        # Categorize results
        categories = {
            'Authentication': [r for r in self.test_results if 'Auth' in r['test']],
            'Frontend': [r for r in self.test_results if 'Frontend' in r['test']],
            'Diamonds': [r for r in self.test_results if 'Diamond' in r['test']],
            'Vendors': [r for r in self.test_results if 'Vendor' in r['test']],
            'Manufacturing': [r for r in self.test_results if 'Manufacturing' in r['test']],
            'Jewelry': [r for r in self.test_results if 'Jewelry' in r['test']],
            'Sales': [r for r in self.test_results if 'Sale' in r['test']],
            'Dashboard': [r for r in self.test_results if 'Dashboard' in r['test']],
            'Validation': [r for r in self.test_results if 'Validation' in r['test']],
            'Special': [r for r in self.test_results if 'Endpoint' in r['test'] or 'Deduct' in r['test']]
        }
        
        print(f"\n📊 Results by Category:")
        for category, tests in categories.items():
            if tests:
                category_passed = sum(1 for t in tests if t['success'])
                category_total = len(tests)
                category_rate = (category_passed / category_total) * 100
                status = "✅" if category_rate >= 80 else "⚠️" if category_rate >= 60 else "❌"
                print(f"   {status} {category}: {category_passed}/{category_total} ({category_rate:.1f}%)")
        
        # Show failed tests
        failed_tests_list = [r for r in self.test_results if not r['success']]
        if failed_tests_list:
            print(f"\n❌ Failed Tests ({len(failed_tests_list)}):")
            for test in failed_tests_list:
                print(f"   - {test['test']}: {test['message']}")
                if test['details']:
                    print(f"     Details: {test['details'][:200]}...")
        
        # Production readiness assessment
        print(f"\n🎯 Production Readiness Assessment:")
        if success_rate >= 90:
            print(f"   🎉 EXCELLENT - All functionality working perfectly!")
            print(f"   ✅ System is fully production-ready")
        elif success_rate >= 80:
            print(f"   ✅ VERY GOOD - Core functionality working well")
            print(f"   ⚠️  Minor issues to address")
        elif success_rate >= 70:
            print(f"   ⚠️  GOOD - Most functionality working")
            print(f"   ❌ Some issues need fixing")
        else:
            print(f"   ❌ NEEDS WORK - Multiple issues to resolve")
        
        return success_rate >= 80
    
    def run_comprehensive_tests(self):
        """Run all comprehensive functionality tests"""
        print("🧪 COMPREHENSIVE FUNCTIONALITY TESTING")
        print("=" * 70)
        print("Testing all pages, endpoints, and functionality...")
        
        # Test frontend accessibility
        self.test_frontend_accessibility()
        
        # Authenticate
        if not self.authenticate():
            print("❌ Cannot proceed without authentication")
            return False
        
        # Test all CRUD operations
        self.test_diamonds_crud()
        self.test_vendors_crud()
        self.test_manufacturing_crud()
        self.test_jewelry_crud()
        self.test_sales_crud()
        
        # Test analytics and special features
        self.test_dashboard_analytics()
        self.test_special_endpoints()
        self.test_validation_rules()
        
        # Clean up test data
        self.cleanup_created_items()
        
        # Generate comprehensive report
        return self.generate_comprehensive_report()

if __name__ == "__main__":
    print("🧪 Starting Comprehensive Functionality Testing")
    print("=" * 70)
    
    tester = ComprehensiveTester()
    success = tester.run_comprehensive_tests()
    
    if success:
        print(f"\n🎉 COMPREHENSIVE TESTING SUCCESSFUL!")
        print(f"All major functionality is working correctly.")
    else:
        print(f"\n⚠️  Some functionality needs attention.")
        print(f"Review the failed tests above and fix the issues.")
    
    sys.exit(0 if success else 1)
