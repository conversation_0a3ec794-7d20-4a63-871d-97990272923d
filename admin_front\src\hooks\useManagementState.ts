import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { api } from '../lib/api';

interface UseManagementStateProps {
  queryKey: string;
  fetchUrl: string;
  createUrl: string;
  deleteUrl: string;
}

export const useManagementState = ({ queryKey, fetchUrl, createUrl, deleteUrl }: UseManagementStateProps) => {
  const [newItem, setNewItem] = useState('');
  const queryClient = useQueryClient();

  const { data: items, isLoading, error } = useQuery({
    queryKey: [queryKey],
    queryFn: async () => {
      const response = await api.get(fetchUrl);
      if (!response) {
        throw new Error(`Failed to fetch ${queryKey}`);
      }
      // Handle both paginated and direct array responses
      if (response.data && Array.isArray(response.data)) {
        return response.data;
      } else if (Array.isArray(response)) {
        return response;
      } else {
        return [];
      }
    },
  });

  const addItemMutation = useMutation({
    mutationFn: async (itemName: string) => {
      await api.post(createUrl, { name: itemName });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKey] });
      toast.success(`${queryKey} added successfully`);
      setNewItem('');
    },
    onError: () => {
      toast.error(`Failed to add ${queryKey}`);
    },
  });

  const deleteItemMutation = useMutation({
    mutationFn: async (itemId: number) => {
      await api.delete(`${deleteUrl}/${itemId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKey] });
      toast.success(`${queryKey} deleted successfully`);
    },
    onError: () => {
      toast.error(`Failed to delete ${queryKey}`);
    },
  });

  return {
    items,
    isLoading,
    error,
    newItem,
    setNewItem,
    addItemMutation,
    deleteItemMutation,
  };
};
