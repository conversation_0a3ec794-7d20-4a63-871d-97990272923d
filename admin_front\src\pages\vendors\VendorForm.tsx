import React from 'react';
import { useForm } from 'react-hook-form';
import { useMutation } from '@tanstack/react-query';
import { api } from '../../lib/api';
import { Vendor } from '../../types';
import Input from '../../components/ui/Input';
import Select from '../../components/ui/Select';
import Button from '../../components/ui/Button';
import toast from 'react-hot-toast';
import {
  VENDOR_STATUSES,
  BUSINESS_TYPES,
  PAYMENT_TERMS,
  INDIAN_STATES,
  COUNTRIES,
  validateGSTNumber,
  validatePANNumber,
  validatePhoneNumber,
  validateEmail,
  validateIFSCCode,
  validatePostalCode
} from '../../constants/vendor';

interface VendorFormProps {
  vendor?: Vendor;
  onSuccess: () => void;
}

interface VendorFormData {
  // Basic Information
  name: string;
  company_name?: string;

  // Contact Information
  contact_person?: string;
  contact_number: string;
  alternate_contact?: string;
  email?: string;
  website?: string;

  // Address Information
  address: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;

  // Business Information
  gst_number: string;
  pan_number?: string;
  business_type?: string;
  specialization?: string;

  // Financial Information
  credit_limit?: number;
  payment_terms?: string;
  bank_name?: string;
  bank_account?: string;
  ifsc_code?: string;

  // Status and Verification
  status?: 'active' | 'inactive' | 'blacklisted' | 'pending_verification';
  is_verified?: boolean;
  verification_date?: string;

  // Additional Information
  notes?: string;
  last_order_date?: string;
}

const VendorForm: React.FC<VendorFormProps> = ({ vendor, onSuccess }) => {
  const isEditing = !!vendor;

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<VendorFormData>({
    defaultValues: vendor ? {
      name: vendor.name,
      company_name: vendor.company_name || '',
      contact_person: vendor.contact_person || '',
      contact_number: vendor.contact_number,
      alternate_contact: vendor.alternate_contact || '',
      email: vendor.email || '',
      website: vendor.website || '',
      address: vendor.address,
      city: vendor.city || '',
      state: vendor.state || '',
      country: vendor.country || 'India',
      postal_code: vendor.postal_code || '',
      gst_number: vendor.gst_number,
      pan_number: vendor.pan_number || '',
      business_type: vendor.business_type || '',
      specialization: vendor.specialization || '',
      credit_limit: vendor.credit_limit,
      payment_terms: vendor.payment_terms || '',
      bank_name: vendor.bank_name || '',
      bank_account: vendor.bank_account || '',
      ifsc_code: vendor.ifsc_code || '',
      status: vendor.status || 'active',
      is_verified: vendor.is_verified || false,
      verification_date: vendor.verification_date
        ? (vendor.verification_date.includes('T') ? vendor.verification_date.split('T')[0] : vendor.verification_date)
        : '',
      notes: vendor.notes || '',
      last_order_date: vendor.last_order_date
        ? (vendor.last_order_date.includes('T') ? vendor.last_order_date.split('T')[0] : vendor.last_order_date)
        : ''
    } : {
      country: 'India',
      status: 'active' as const,
      is_verified: false
    }
  });

  // Enhanced validation function
  const validateVendorData = (data: VendorFormData) => {
    const errors: string[] = [];

    // GST validation
    if (data.gst_number && !validateGSTNumber(data.gst_number)) {
      errors.push('Invalid GST number format');
    }

    // PAN validation
    if (data.pan_number && !validatePANNumber(data.pan_number)) {
      errors.push('Invalid PAN number format');
    }

    // Phone validation
    if (data.contact_number && !validatePhoneNumber(data.contact_number)) {
      errors.push('Invalid contact number format');
    }

    if (data.alternate_contact && !validatePhoneNumber(data.alternate_contact)) {
      errors.push('Invalid alternate contact number format');
    }

    // Email validation
    if (data.email && !validateEmail(data.email)) {
      errors.push('Invalid email format');
    }

    // IFSC validation
    if (data.ifsc_code && !validateIFSCCode(data.ifsc_code)) {
      errors.push('Invalid IFSC code format');
    }

    // Postal code validation (for India)
    if (data.postal_code && data.country === 'India' && !validatePostalCode(data.postal_code)) {
      errors.push('Invalid postal code format');
    }

    // Credit limit validation
    if (data.credit_limit && data.credit_limit < 0) {
      errors.push('Credit limit cannot be negative');
    }

    return errors;
  };

  const mutation = useMutation({
    mutationFn: async (data: VendorFormData) => {
      // Client-side validation
      const validationErrors = validateVendorData(data);
      if (validationErrors.length > 0) {
        throw new Error(validationErrors.join(', '));
      }

      // Prepare payload with proper data types
      const payload = {
        ...data,
        credit_limit: data.credit_limit ? Number(data.credit_limit) : undefined,
        is_verified: Boolean(data.is_verified)
      };

      if (isEditing) {
        await api.vendors.update(vendor.id, payload);
      } else {
        await api.vendors.create(payload);
      }
    },
    onSuccess: () => {
      toast.success(`Vendor ${isEditing ? 'updated' : 'created'} successfully`);
      onSuccess();
    },
    onError: (error: any) => {
      const message = error.message || error.response?.data?.message || `Failed to ${isEditing ? 'update' : 'create'} vendor`;
      toast.error(message);
    }
  });

  const onSubmit = (data: VendorFormData) => {
    mutation.mutate(data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      {/* Basic Information Section */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="Vendor Name"
            type="text"
            required
            {...register('name', {
              required: 'Vendor name is required',
              minLength: { value: 2, message: 'Name must be at least 2 characters' }
            })}
            error={errors.name?.message}
            helperText="Display name for the vendor"
          />

          <Input
            label="Company Name"
            type="text"
            {...register('company_name')}
            error={errors.company_name?.message}
            helperText="Legal company name (if different)"
          />
        </div>
      </div>

      {/* Contact Information Section */}
      <div className="bg-blue-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="Contact Person"
            type="text"
            {...register('contact_person')}
            error={errors.contact_person?.message}
            helperText="Primary contact person name"
          />

          <Input
            label="Contact Number"
            type="tel"
            required
            {...register('contact_number', {
              required: 'Contact number is required',
              pattern: {
                value: /^[+]?[1-9]?[0-9]{7,15}$/,
                message: 'Invalid contact number format'
              }
            })}
            error={errors.contact_number?.message}
            helperText="Primary contact number"
          />

          <Input
            label="Alternate Contact"
            type="tel"
            {...register('alternate_contact', {
              pattern: {
                value: /^[+]?[1-9]?[0-9]{7,15}$/,
                message: 'Invalid contact number format'
              }
            })}
            error={errors.alternate_contact?.message}
            helperText="Secondary contact number"
          />

          <Input
            label="Email Address"
            type="email"
            {...register('email', {
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: 'Invalid email format'
              }
            })}
            error={errors.email?.message}
            helperText="Business email address"
          />

          <div className="md:col-span-2">
            <Input
              label="Website"
              type="url"
              {...register('website')}
              error={errors.website?.message}
              placeholder="https://www.example.com"
              helperText="Company website URL"
            />
          </div>
        </div>
      </div>

      {/* Address Information Section */}
      <div className="bg-green-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Address Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Street Address <span className="text-red-500">*</span>
            </label>
            <textarea
              {...register('address', {
                required: 'Address is required',
              minLength: { value: 10, message: 'Address must be at least 10 characters' }
            })}
            rows={3}
            className={`
              w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
              disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
              ${errors.address ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : ''}
            `}
            placeholder="Enter complete street address..."
          />
          {errors.address && (
            <p className="mt-1 text-sm text-red-600">{errors.address.message}</p>
          )}
          </div>

          <Input
            label="City"
            type="text"
            {...register('city')}
            error={errors.city?.message}
          />

          <Input
            label="Postal Code"
            type="text"
            {...register('postal_code', {
              pattern: {
                value: /^[1-9][0-9]{5}$/,
                message: 'Invalid postal code format (6 digits)'
              }
            })}
            error={errors.postal_code?.message}
            helperText="6-digit postal code"
          />

          <Select
            label="State"
            options={INDIAN_STATES}
            {...register('state')}
            error={errors.state?.message}
            placeholder="Select state"
          />

          <Select
            label="Country"
            options={COUNTRIES}
            {...register('country')}
            error={errors.country?.message}
          />
        </div>
      </div>

      {/* Business Information Section */}
      <div className="bg-purple-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Business Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="GST Number"
            type="text"
            required
            {...register('gst_number', {
              required: 'GST number is required',
              pattern: {
                value: /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
                message: 'Invalid GST number format'
              }
            })}
            error={errors.gst_number?.message}
            helperText="Format: 22**********1Z5"
          />

          <Input
            label="PAN Number"
            type="text"
            {...register('pan_number', {
              pattern: {
                value: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
                message: 'Invalid PAN number format'
              }
            })}
            error={errors.pan_number?.message}
            helperText="Format: **********"
          />

          <Select
            label="Business Type"
            options={BUSINESS_TYPES}
            {...register('business_type')}
            error={errors.business_type?.message}
            placeholder="Select business type"
          />

          <Input
            label="Specialization"
            type="text"
            {...register('specialization')}
            error={errors.specialization?.message}
            placeholder="e.g., Diamond cutting, Gold jewelry"
            helperText="What they specialize in"
          />
        </div>
      </div>

      {/* Financial Information Section */}
      <div className="bg-red-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="Credit Limit"
            type="number"
            step="0.01"
            {...register('credit_limit', {
              min: { value: 0, message: 'Credit limit cannot be negative' }
            })}
            error={errors.credit_limit?.message}
            helperText="Maximum credit amount"
          />

          <Select
            label="Payment Terms"
            options={PAYMENT_TERMS}
            {...register('payment_terms')}
            error={errors.payment_terms?.message}
            placeholder="Select payment terms"
          />

          <Input
            label="Bank Name"
            type="text"
            {...register('bank_name')}
            error={errors.bank_name?.message}
          />

          <Input
            label="Bank Account Number"
            type="text"
            {...register('bank_account')}
            error={errors.bank_account?.message}
          />

          <Input
            label="IFSC Code"
            type="text"
            {...register('ifsc_code', {
              pattern: {
                value: /^[A-Z]{4}0[A-Z0-9]{6}$/,
                message: 'Invalid IFSC code format'
              }
            })}
            error={errors.ifsc_code?.message}
            helperText="Format: ABCD0123456"
          />
        </div>
      </div>

      {/* Status and Additional Information Section */}
      <div className="bg-indigo-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Status & Additional Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Select
            label="Status"
            options={VENDOR_STATUSES}
            {...register('status')}
            error={errors.status?.message}
          />

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              {...register('is_verified')}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label className="text-sm text-gray-700">Verified Vendor</label>
          </div>

          <Input
            label="Verification Date"
            type="date"
            {...register('verification_date')}
            error={errors.verification_date?.message}
          />

          <Input
            label="Last Order Date"
            type="date"
            {...register('last_order_date')}
            error={errors.last_order_date?.message}
          />

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              {...register('notes')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Additional notes about this vendor..."
            />
            {errors.notes && (
              <p className="mt-1 text-sm text-red-600">{errors.notes.message}</p>
            )}
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          type="submit"
          isLoading={mutation.isPending}
        >
          {isEditing ? 'Update Vendor' : 'Create Vendor'}
        </Button>
      </div>
    </form>
  );
};

export default VendorForm;