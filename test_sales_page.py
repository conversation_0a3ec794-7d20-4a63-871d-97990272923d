#!/usr/bin/env python3
"""
Test sales page functionality
"""

import requests
import time
from datetime import date

BASE_URL = "http://localhost:8000/api"
LOGIN_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

def test_sales_functionality():
    print("💰 SALES PAGE FUNCTIONALITY TEST")
    print("=" * 40)
    
    # Authenticate
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json=LOGIN_CREDENTIALS)
    if response.status_code != 200:
        print("❌ Authentication failed")
        return False
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Test GET sales
    print("\n1. Testing GET sales...")
    response = session.get(f"{BASE_URL}/sales")
    if response.status_code == 200:
        sales = response.json()
        print(f"✅ GET sales: Found {len(sales)} sales")
    else:
        print(f"❌ GET sales failed: {response.status_code}")
        return False
    
    # Get jewelry item for testing
    print("\n2. Setting up test jewelry...")
    response = session.get(f"{BASE_URL}/jewelry")
    if response.status_code == 200:
        jewelry_items = response.json()
        if jewelry_items:
            jewelry_id = jewelry_items[0]['id']
            print(f"✅ Using existing jewelry: ID {jewelry_id}")
        else:
            print("❌ No jewelry items available for testing")
            return False
    else:
        print(f"❌ Failed to get jewelry: {response.status_code}")
        return False
    
    # Test CREATE sale
    print("\n3. Testing CREATE sale...")
    unique_id = int(time.time())
    sale_data = {
        "customer_name": f"Test Customer {unique_id}",
        "customer_email": f"customer{unique_id}@test.com",
        "customer_phone": "+91-9876543210",
        "customer_address": "123 Customer Street, Test City",
        "customer_city": "Mumbai",
        "customer_state": "Maharashtra",
        "customer_country": "India",
        "customer_postal_code": "400001",
        "sale_date": date.today().isoformat(),
        "sale_type": "retail",
        "sales_channel": "store",
        "subtotal": 40000,
        "tax_percentage": 3,
        "tax_amount": 1200,
        "discount_amount": 0,
        "total_amount": 41200,
        "payment_status": "paid",
        "payment_method": "card",
        "jewelry_id": jewelry_id,
        "quantity": 1,
        "unit_price": 40000,
        "notes": "Test sale transaction"
    }
    
    response = session.post(f"{BASE_URL}/sales", json=sale_data)
    if response.status_code in [200, 201]:
        sale = response.json()
        sale_id = sale.get('id')
        print(f"✅ CREATE sale: ID {sale_id}")
        
        # Check enhanced fields
        enhanced_fields = ['customer_email', 'sales_channel', 'payment_method', 'tax_amount']
        found_fields = [f for f in enhanced_fields if sale.get(f) is not None]
        print(f"✅ Enhanced fields: {found_fields}")
        
        # Check total calculation
        expected_total = 40000 + 1200  # subtotal + tax
        actual_total = sale.get('total_amount')
        if actual_total == expected_total:
            print(f"✅ Total calculation: {actual_total}")
        else:
            print(f"⚠️  Total calculation: expected {expected_total}, got {actual_total}")
        
    else:
        print(f"❌ CREATE sale failed: {response.status_code}")
        print(f"Error: {response.text[:200]}")
        return False
    
    # Test READ sale
    print("\n4. Testing READ sale...")
    response = session.get(f"{BASE_URL}/sales/{sale_id}")
    if response.status_code == 200:
        print(f"✅ READ sale: Retrieved successfully")
    else:
        print(f"❌ READ sale failed: {response.status_code}")
    
    # Test UPDATE sale
    print("\n5. Testing UPDATE sale...")
    update_data = {
        "payment_status": "partial",
        "notes": "Updated test sale - partial payment received",
        "discount_amount": 2000,
        "total_amount": 39200  # 40000 + 1200 - 2000
    }
    
    response = session.put(f"{BASE_URL}/sales/{sale_id}", json=update_data)
    if response.status_code == 200:
        updated_sale = response.json()
        print(f"✅ UPDATE sale: Updated successfully")
        print(f"   Payment status: {updated_sale.get('payment_status')}")
        print(f"   Total amount: {updated_sale.get('total_amount')}")
    else:
        print(f"❌ UPDATE sale failed: {response.status_code}")
    
    # Test sales filtering
    print("\n6. Testing sales filtering...")
    filters = [
        {"payment_status": "partial", "name": "Payment status filter"},
        {"sales_channel": "store", "name": "Sales channel filter"},
        {"customer_name": f"Test Customer {unique_id}", "name": "Customer filter"}
    ]
    
    for filter_data in filters:
        filter_name = filter_data.pop('name')
        response = session.get(f"{BASE_URL}/sales", params=filter_data)
        if response.status_code == 200:
            filtered_sales = response.json()
            print(f"   ✅ {filter_name}: Found {len(filtered_sales)} sales")
        else:
            print(f"   ❌ {filter_name} failed: {response.status_code}")
    
    # Test sales analytics
    print("\n7. Testing sales analytics...")
    response = session.get(f"{BASE_URL}/sales/analytics")
    if response.status_code == 200:
        analytics = response.json()
        print(f"✅ Sales analytics: Retrieved successfully")
        print(f"   Keys: {list(analytics.keys())[:5]}")
    else:
        print(f"❌ Sales analytics failed: {response.status_code}")
    
    # Test sales reports
    print("\n8. Testing sales reports...")
    report_params = {
        "start_date": "2024-01-01",
        "end_date": date.today().isoformat()
    }
    response = session.get(f"{BASE_URL}/sales/reports", params=report_params)
    if response.status_code == 200:
        reports = response.json()
        print(f"✅ Sales reports: Retrieved successfully")
    else:
        print(f"❌ Sales reports failed: {response.status_code}")
    
    # Test DELETE sale
    print("\n9. Testing DELETE sale...")
    response = session.delete(f"{BASE_URL}/sales/{sale_id}")
    if response.status_code in [200, 204]:
        print(f"✅ DELETE sale: Deleted successfully")
    else:
        print(f"❌ DELETE sale failed: {response.status_code}")
    
    print("\n🎯 Sales functionality test complete!")
    return True

if __name__ == "__main__":
    success = test_sales_functionality()
    if success:
        print("✅ All sales functionality is working correctly!")
    else:
        print("❌ Some sales functionality needs attention.")
