#!/usr/bin/env python3
"""
Test authentication on all endpoints
"""

import requests

BASE_URL = "http://localhost:8000/api"

def test_auth_all():
    print("🔍 Testing Authentication on All Endpoints...")
    
    endpoints = [
        'diamonds',
        'vendors', 
        'manufacturing',
        'jewelry',
        'sales',
        'dashboard'
    ]
    
    for endpoint in endpoints:
        print(f"\nTesting {endpoint}...")
        response = requests.get(f"{BASE_URL}/{endpoint}")
        print(f"  Status: {response.status_code}")
        if response.status_code == 200:
            print(f"  ❌ No authentication required")
        elif response.status_code == 401:
            print(f"  ✅ Authentication required")
        else:
            print(f"  ⚠️  Unexpected status: {response.status_code}")

if __name__ == "__main__":
    test_auth_all()
