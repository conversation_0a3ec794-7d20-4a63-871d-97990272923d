#!/usr/bin/env python3
"""
Test the uploads route fix
"""

import requests
import time

BASE_URL = "http://localhost:8000"

def test_uploads_route_fix():
    print("🔧 Testing Uploads Route Fix")
    print("=" * 50)
    
    # Step 1: Login
    print("\n1. 🔐 Authentication")
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        "email": "<EMAIL>",
        "password": "Shivam@109"
    })
    
    if login_response.status_code != 200:
        print(f"   ❌ Login failed: {login_response.status_code}")
        return False
    
    token = login_response.json().get('access_token')
    headers = {'Authorization': f'Bearer {token}'}
    print("   ✅ Authentication successful")
    
    # Step 2: Get diamond images
    print("\n2. 🖼️ Getting diamond images")
    images_response = requests.get(f"{BASE_URL}/api/diamond-images/diamonds/9/images", headers=headers)
    
    if images_response.status_code != 200:
        print(f"   ❌ Failed to get images: {images_response.status_code}")
        return False
    
    images = images_response.json()
    print(f"   ✅ Found {len(images)} images")
    
    if not images:
        print("   ⚠️  No images to test - need to upload an image first")
        return True
    
    # Step 3: Test the uploads route
    print("\n3. 🔗 Testing uploads route")
    for i, image in enumerate(images):
        image_url = image.get('image_url')
        print(f"\n   Image {i+1}:")
        print(f"      Relative URL: {image_url}")
        
        # Test the new uploads route
        full_url = f"{BASE_URL}{image_url}"
        print(f"      Full URL: {full_url}")
        
        try:
            # Test if the image is accessible
            image_response = requests.head(full_url, timeout=10)
            print(f"      Status: {image_response.status_code}", end="")
            
            if image_response.status_code == 200:
                print(" ✅ (Image accessible!)")
                content_type = image_response.headers.get('content-type', 'unknown')
                content_length = image_response.headers.get('content-length', 'unknown')
                print(f"      Content-Type: {content_type}")
                print(f"      Content-Length: {content_length} bytes")
                
                # Test actual image download
                print("      Testing image download...", end="")
                download_response = requests.get(full_url, timeout=10)
                if download_response.status_code == 200:
                    print(" ✅")
                    print(f"      Downloaded {len(download_response.content)} bytes")
                else:
                    print(f" ❌ (Download failed: {download_response.status_code})")
                    
            elif image_response.status_code == 404:
                print(" ❌ (File not found)")
            elif image_response.status_code == 500:
                print(" ❌ (Server error)")
            else:
                print(f" ❌ (Unexpected status: {image_response.status_code})")
                
        except Exception as e:
            print(f"      ❌ Error accessing image: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 UPLOADS ROUTE FIX TEST COMPLETE!")
    print("=" * 50)
    print("\n📝 What was fixed:")
    print("   ✅ Added /uploads/<path:filename> route to Flask app")
    print("   ✅ Route serves files from uploads directory")
    print("   ✅ Frontend can now access uploaded images")
    print("\n🎯 Frontend should now:")
    print("   ✅ Display diamond images correctly")
    print("   ✅ Show primary image without broken icon")
    print("   ✅ Show thumbnails in grid")
    print("   ✅ Display full-size images in modal")
    print("\n💡 Next steps:")
    print("   1. Refresh your browser page")
    print("   2. Navigate to diamond edit page")
    print("   3. Check if images display correctly")
    print("   4. Try uploading a new image")
    
    return True

if __name__ == "__main__":
    print("⚠️  IMPORTANT: Make sure to restart the Flask server first!")
    print("   The new uploads route needs a server restart to take effect.")
    print("   Press Ctrl+C in the Flask terminal and run 'flask run --port=8000' again.")
    print("")
    
    input("Press Enter when you've restarted the Flask server...")
    
    success = test_uploads_route_fix()
    if success:
        print("\n🚀 Uploads route fix applied successfully!")
        print("\n🎉 Diamond image display should now work correctly!")
    else:
        print("\n❌ Uploads route fix test failed")
