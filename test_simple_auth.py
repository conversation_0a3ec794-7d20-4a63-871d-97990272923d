#!/usr/bin/env python3
"""
Simple authentication test
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000/api"
LOGIN_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

def test_login():
    """Test login functionality"""
    print("🔐 Testing Login...")
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=LOGIN_CREDENTIALS)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('access_token'):
                print("✅ Login successful!")
                return data['access_token']
            else:
                print("❌ Login failed - no token received")
                return None
        else:
            print(f"❌ Login failed with status {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error during login: {str(e)}")
        return None

def test_protected_endpoint(token):
    """Test a protected endpoint"""
    print("\n🔒 Testing Protected Endpoint...")
    
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        response = requests.get(f"{BASE_URL}/diamonds", headers=headers)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Protected endpoint accessible! Found {len(data.get('data', []))} diamonds")
            return True
        else:
            print(f"❌ Protected endpoint failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error accessing protected endpoint: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Simple Authentication Test")
    print("=" * 40)
    
    # Test login
    token = test_login()
    
    if token:
        # Test protected endpoint
        success = test_protected_endpoint(token)
        
        if success:
            print("\n🎉 Authentication system working correctly!")
        else:
            print("\n⚠️ Authentication works but protected endpoints have issues")
    else:
        print("\n❌ Authentication system needs fixing")
