#!/usr/bin/env python3
"""
Debug validation issues
"""

import requests

BASE_URL = "http://localhost:8000/api"

def test_validation():
    # Login
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Test invalid color
    invalid_data = {
        'shape_id': 1,
        'carat': 1.0,
        'color': 'XX',  # Invalid color
        'clarity': 'VS1',
        'certificate_no': 'TEST_INVALID_COLOR'
    }
    
    print("\nTesting invalid color...")
    response = session.post(f"{BASE_URL}/diamonds", json=invalid_data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")

if __name__ == "__main__":
    test_validation()
