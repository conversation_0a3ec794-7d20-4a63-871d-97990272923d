# Diamond Management System - Login Credentials

## 🔐 Admin Login Credentials

The system has two pre-configured admin accounts:

### Admin User 1
- **Email:** `<EMAIL>`
- **Password:** `<PERSON><PERSON>@109`
- **Name:** <PERSON><PERSON>

### Admin User 2
- **Email:** `<EMAIL>`
- **Password:** `<PERSON><PERSON>@9049`
- **Name:** <PERSON><PERSON>

## 🚀 How to Login

1. Navigate to the login page: `http://localhost:5173/login`
2. Enter one of the email addresses above
3. Enter the corresponding password
4. Click "Sign In"

## ⚠️ Important Notes

- **Admin Access Only**: Only users with admin role can access the system
- **Case Sensitive**: Passwords are case-sensitive
- **Auto-Creation**: These admin users are automatically created when the backend starts
- **Password Updates**: If you change passwords, they will be updated in the database

## 🔧 Troubleshooting Login Issues

### Common Issues:

1. **"Invalid credentials" error**
   - Double-check the email and password (case-sensitive)
   - Ensure you're using the exact credentials listed above

2. **"Access denied. Admin privileges required."**
   - The user exists but doesn't have admin role
   - Contact system administrator

3. **Network/Connection errors**
   - Ensure backend is running on `http://127.0.0.1:8000`
   - Check browser console for API errors

4. **Page not loading**
   - Ensure frontend is running on `http://localhost:5173`
   - Clear browser cache and cookies

### Testing Login via API:

You can test login directly via API:

```bash
curl -X POST http://127.0.0.1:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Shivam@109"
  }'
```

Expected response:
```json
{
  "access_token": "eyJ...",
  "refresh_token": "eyJ...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "first_name": "Shivam",
    "last_name": "Vegad",
    "role": "admin"
  }
}
```

## 🔄 Recent Fixes Applied

1. **Fixed API Response Handling**: Updated AuthContext to work with the corrected API client
2. **Corrected Data Extraction**: Removed redundant `.data` property access
3. **Enhanced Error Handling**: Better error messages for login failures

The login system should now work correctly with the credentials listed above!
