#!/usr/bin/env python3
"""
Fix jewelry status to make some available for testing
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'admin_backend'))

from app import create_app, db
from app.models.jewelry import JewelryItem

def fix_jewelry_status():
    print("🔧 Fixing Jewelry Status")
    print("=" * 25)
    
    app = create_app()
    with app.app_context():
        # Get all jewelry items
        jewelry_items = JewelryItem.query.all()
        print(f"Found {len(jewelry_items)} jewelry items")
        
        for item in jewelry_items:
            print(f"  - {item.name}: {item.status}")
            
        # Mark some jewelry as available
        if jewelry_items:
            for item in jewelry_items[:2]:  # Mark first 2 as available
                item.status = 'in_stock'
                print(f"  ✅ Marked {item.name} as in_stock")
            
            db.session.commit()
            print("✅ Jewelry status updated")
            
            # Verify
            available = JewelryItem.query.filter_by(status='in_stock').count()
            print(f"Available jewelry items: {available}")

if __name__ == "__main__":
    fix_jewelry_status()
