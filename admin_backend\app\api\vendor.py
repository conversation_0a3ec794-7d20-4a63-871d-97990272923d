from flask_restx import Namespace, Resource, fields
from flask import request
from app.models.vendor import Vendor
from app import db
from app.utils.decorators import token_required
from sqlalchemy.exc import IntegrityError
from app.models.jewelry import JewelryItem  # Corrected import path

vendor_ns = Namespace('vendors', description='Vendor Management', path='/vendors')

vendor_model = vendor_ns.model('Vendor', {
    'id': fields.Integer(readOnly=True),
    'name': fields.String(required=True, description='Vendor name'),
    'company_name': fields.String(description='Legal company name'),
    'vendor_code': fields.String(readOnly=True, description='Unique vendor code'),

    # Contact Information
    'contact_person': fields.String(description='Primary contact person'),
    'contact_number': fields.String(required=True, description='Primary contact number'),
    'alternate_contact': fields.String(description='Secondary contact number'),
    'email': fields.String(description='Email address'),
    'website': fields.String(description='Company website'),

    # Address Information
    'address': fields.String(required=True, description='Street address'),
    'city': fields.String(description='City'),
    'state': fields.String(description='State'),
    'country': fields.String(description='Country'),
    'postal_code': fields.String(description='Postal/ZIP code'),

    # Business Information
    'gst_number': fields.String(required=True, description='GST registration number'),
    'pan_number': fields.String(description='PAN number'),
    'business_type': fields.String(description='Type of business'),
    'specialization': fields.String(description='Business specialization'),

    # Financial Information
    'credit_limit': fields.Float(description='Credit limit'),
    'payment_terms': fields.String(description='Payment terms'),
    'bank_name': fields.String(description='Bank name'),
    'bank_account': fields.String(description='Bank account number'),
    'ifsc_code': fields.String(description='IFSC code'),

    # Performance Metrics
    'rating': fields.Float(readOnly=True, description='Vendor rating (1-5)'),
    'total_orders': fields.Integer(readOnly=True, description='Total orders placed'),
    'total_value': fields.Float(readOnly=True, description='Total business value'),

    # Status and Verification
    'status': fields.String(description='Vendor status'),
    'is_verified': fields.Boolean(description='Verification status'),
    'verification_date': fields.String(description='Verification date'),

    # Additional Information
    'notes': fields.String(description='Additional notes'),
    'documents_path': fields.String(description='Path to vendor documents'),

    # Timestamps
    'created_at': fields.String(readOnly=True, description='Creation timestamp'),
    'updated_at': fields.String(readOnly=True, description='Last update timestamp'),
    'last_order_date': fields.String(description='Last order date'),

    # Calculated Fields
    'outstanding_amount': fields.Float(readOnly=True, description='Outstanding amount')
})

vendor_update_model = vendor_ns.model('VendorUpdate', {
    'name': fields.String(required=False),
    'company_name': fields.String(required=False),
    'contact_person': fields.String(required=False),
    'contact_number': fields.String(required=False),
    'alternate_contact': fields.String(required=False),
    'email': fields.String(required=False),
    'website': fields.String(required=False),
    'address': fields.String(required=False),
    'city': fields.String(required=False),
    'state': fields.String(required=False),
    'country': fields.String(required=False),
    'postal_code': fields.String(required=False),
    'gst_number': fields.String(required=False),
    'pan_number': fields.String(required=False),
    'business_type': fields.String(required=False),
    'specialization': fields.String(required=False),
    'credit_limit': fields.Float(required=False),
    'payment_terms': fields.String(required=False),
    'bank_name': fields.String(required=False),
    'bank_account': fields.String(required=False),
    'ifsc_code': fields.String(required=False),
    'status': fields.String(required=False),
    'is_verified': fields.Boolean(required=False),
    'verification_date': fields.String(required=False),
    'notes': fields.String(required=False),
    'last_order_date': fields.String(required=False)
})

error_model = vendor_ns.model('VendorError', {
    'status': fields.String(description='Error status'),
    'message': fields.String(description='Error message'),
    'status_code': fields.Integer(description='HTTP status code')
})

def vendor_to_dict(v):
    """Convert vendor model to dictionary using the model's to_dict method."""
    return v.to_dict()

@vendor_ns.route('/')
class VendorList(Resource):
    @vendor_ns.doc('list_vendors')
    @vendor_ns.marshal_list_with(vendor_model)
    @vendor_ns.response(200, 'List of vendors', [vendor_model])
    @vendor_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        vendors = Vendor.query.all()
        return [vendor_to_dict(v) for v in vendors], 200

    @vendor_ns.doc('create_vendor')
    @vendor_ns.expect(vendor_model, validate=True)
    @vendor_ns.marshal_with(vendor_model, code=201)
    @vendor_ns.response(400, 'Validation error', error_model)
    @vendor_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def post(self):
        data = request.json
        try:
            # Validate required fields
            required_fields = ['name', 'contact_number', 'address', 'gst_number']
            for field in required_fields:
                if not data.get(field):
                    vendor_ns.abort(400, f'Missing required field: {field}')

            # Create vendor with all supported fields
            v = Vendor(
                name=data.get('name'),
                company_name=data.get('company_name'),
                vendor_code=data.get('vendor_code'),
                contact_person=data.get('contact_person'),
                contact_number=data.get('contact_number'),
                alternate_contact=data.get('alternate_contact'),
                email=data.get('email'),
                website=data.get('website'),
                address=data.get('address'),
                city=data.get('city'),
                state=data.get('state'),
                country=data.get('country', 'India'),
                postal_code=data.get('postal_code'),
                gst_number=data.get('gst_number'),
                pan_number=data.get('pan_number'),
                business_type=data.get('business_type'),
                specialization=data.get('specialization'),
                credit_limit=data.get('credit_limit', 0.0),
                payment_terms=data.get('payment_terms'),
                bank_name=data.get('bank_name'),
                bank_account=data.get('bank_account'),
                ifsc_code=data.get('ifsc_code'),
                rating=data.get('rating', 0.0),
                status=data.get('status', 'active'),
                is_verified=data.get('is_verified', False)
            )
            db.session.add(v)
            db.session.commit()
            return vendor_to_dict(v), 201
        except IntegrityError as e:
            db.session.rollback()
            vendor_ns.abort(409, "Vendor with this GST number already exists")
        except Exception as e:
            db.session.rollback()
            vendor_ns.abort(500, f'Failed to create vendor: {str(e)}')

@vendor_ns.route('/<int:vendor_id>')
@vendor_ns.response(404, 'Vendor not found', error_model)
class VendorResource(Resource):
    @vendor_ns.doc('get_vendor')
    @vendor_ns.marshal_with(vendor_model)
    @vendor_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self, vendor_id):
        v = Vendor.query.get_or_404(vendor_id)
        return vendor_to_dict(v), 200

    @vendor_ns.doc('update_vendor')
    @vendor_ns.expect(vendor_update_model, validate=True)
    @vendor_ns.marshal_with(vendor_model)
    @vendor_ns.response(400, 'Validation error', error_model)
    @vendor_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def put(self, vendor_id):
        v = Vendor.query.get_or_404(vendor_id)
        data = request.json
        try:
            # Update all supported fields
            updatable_fields = [
                'name', 'company_name', 'vendor_code', 'contact_person',
                'contact_number', 'alternate_contact', 'email', 'website',
                'address', 'city', 'state', 'country', 'postal_code',
                'gst_number', 'pan_number', 'business_type', 'specialization',
                'credit_limit', 'payment_terms', 'bank_name', 'bank_account',
                'ifsc_code', 'rating', 'status', 'is_verified'
            ]
            for field in updatable_fields:
                if field in data:
                    setattr(v, field, data[field])
            db.session.commit()
            return vendor_to_dict(v), 200
        except Exception as e:
            db.session.rollback()
            vendor_ns.abort(500, f'Failed to update vendor: {str(e)}')

    @vendor_ns.doc('delete_vendor')
    @vendor_ns.response(200, 'Vendor deleted')
    @vendor_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def delete(self, vendor_id):
        v = Vendor.query.get_or_404(vendor_id)
        # Check for associated records
        associated_jewelry = JewelryItem.query.filter_by(vendor_id=vendor_id).count()
        from app.models.manufacturing import ManufacturingRequest
        associated_manufacturing = ManufacturingRequest.query.filter_by(vendor_id=vendor_id).count()
        if associated_jewelry > 0 or associated_manufacturing > 0:
            vendor_ns.abort(400, 'Cannot delete vendor with associated jewelry items or manufacturing requests.')
        try:
            db.session.delete(v)
            db.session.commit()
            return {'message': 'Vendor deleted'}, 200
        except Exception as e:
            db.session.rollback()
            vendor_ns.abort(400, f'Failed to delete vendor: {str(e)}')
