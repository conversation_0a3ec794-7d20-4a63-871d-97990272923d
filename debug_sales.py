#!/usr/bin/env python3
"""
Debug sales operations
"""

import requests
import time

BASE_URL = "http://localhost:8000/api"

def debug_sales():
    print("🔍 Debugging Sales Operations")
    print("=" * 30)
    
    # Login
    response = requests.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    token = response.json().get('access_token')
    headers = {'Authorization': f'Bearer {token}'}
    print("✅ Authenticated")
    
    # Check existing sales
    response = requests.get(f"{BASE_URL}/sales", headers=headers)
    print(f"\nExisting sales: {response.status_code}")
    if response.status_code == 200:
        sales_data = response.json()
        if isinstance(sales_data, dict) and 'data' in sales_data:
            sales = sales_data['data']
        else:
            sales = sales_data
        print(f"Found {len(sales)} sales")
        
        if sales:
            sale_id = sales[0]['id']
            print(f"First sale ID: {sale_id}")
            
            # Test READ specific sale
            response = requests.get(f"{BASE_URL}/sales/{sale_id}", headers=headers)
            print(f"READ sale {sale_id}: {response.status_code}")
            
            # Test UPDATE sale
            update_data = {"notes": "Updated test sale"}
            response = requests.put(f"{BASE_URL}/sales/{sale_id}", json=update_data, headers=headers)
            print(f"UPDATE sale {sale_id}: {response.status_code}")
            if response.status_code != 200:
                print(f"Update error: {response.text[:200]}")
    
    # Check available jewelry
    response = requests.get(f"{BASE_URL}/jewelry", headers=headers)
    if response.status_code == 200:
        jewelry_items = response.json()
        available_jewelry = [j for j in jewelry_items if j.get('status') != 'sold']
        print(f"\nAvailable jewelry: {len(available_jewelry)} items")
        
        if available_jewelry:
            jewelry_id = available_jewelry[0]['id']
            print(f"Using jewelry ID: {jewelry_id}")
            
            # Try creating minimal sale
            unique_id = int(time.time())
            minimal_sale = {
                "customer_name": f"Test Customer {unique_id}",
                "total_amount": 40000,
                "jewelry_id": jewelry_id
            }
            
            response = requests.post(f"{BASE_URL}/sales", json=minimal_sale, headers=headers)
            print(f"CREATE sale (minimal): {response.status_code}")
            if response.status_code not in [200, 201]:
                print(f"Create error: {response.text[:300]}")
            else:
                sale = response.json()
                new_sale_id = sale.get('id')
                print(f"Created sale ID: {new_sale_id}")
                
                # Test DELETE
                response = requests.delete(f"{BASE_URL}/sales/{new_sale_id}", headers=headers)
                print(f"DELETE sale {new_sale_id}: {response.status_code}")
        else:
            print("No available jewelry for testing")

if __name__ == "__main__":
    debug_sales()
