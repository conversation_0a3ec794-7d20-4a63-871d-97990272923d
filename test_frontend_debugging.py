#!/usr/bin/env python3
"""
Test to verify the frontend debugging is working
"""

import requests
import time

BASE_URL = "http://localhost:8000/api"

def test_frontend_debugging():
    print("🔧 Testing Frontend Debugging Setup")
    print("=" * 50)
    
    # Step 1: Login
    print("\n1. 🔐 Authentication")
    login_response = requests.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "Shivam@109"
    })
    
    if login_response.status_code != 200:
        print(f"   ❌ Login failed: {login_response.status_code}")
        return False
    
    token = login_response.json().get('access_token')
    headers = {'Authorization': f'Bearer {token}'}
    print("   ✅ Authentication successful")
    
    # Step 2: Get diamonds to find one to test with
    print("\n2. 💎 Getting diamonds")
    diamonds_response = requests.get(f"{BASE_URL}/diamonds", headers=headers)
    if diamonds_response.status_code != 200:
        print(f"   ❌ Failed to get diamonds: {diamonds_response.status_code}")
        return False
    
    diamonds_data = diamonds_response.json()
    diamonds = diamonds_data.get('data', [])
    if not diamonds:
        print("   ❌ No diamonds found")
        return False
    
    diamond = diamonds[0]
    diamond_id = diamond['id']
    print(f"   ✅ Found diamond: ID {diamond_id}, Certificate: {diamond.get('certificate_no', 'N/A')}")
    
    # Step 3: Test diamond images endpoint
    print(f"\n3. 🖼️ Testing diamond images for ID {diamond_id}")
    images_response = requests.get(f"{BASE_URL}/diamond-images/diamonds/{diamond_id}/images", headers=headers)
    print(f"   Status: {images_response.status_code}")
    
    if images_response.status_code == 200:
        images = images_response.json()
        print(f"   ✅ Found {len(images)} existing images")
        
        # Show image details if any exist
        if images:
            for i, img in enumerate(images[:3]):  # Show first 3
                print(f"      Image {i+1}: ID {img.get('id')}, Type: {img.get('image_type')}, Primary: {img.get('is_primary')}")
        else:
            print("      No images uploaded yet (perfect for testing upload)")
    else:
        print(f"   ❌ Failed to get images: {images_response.text}")
        return False
    
    print("\n" + "=" * 50)
    print("🎯 DEBUGGING SETUP COMPLETE!")
    print("=" * 50)
    print("\n📝 Next Steps:")
    print("   1. Open your browser to: http://localhost:5173/diamonds")
    print(f"   2. Edit diamond with ID: {diamond_id}")
    print("   3. Scroll down to the 'Diamond Images' section")
    print("   4. Open browser console (F12)")
    print("   5. Try clicking 'Upload Image' or 'Upload First Image'")
    print("\n🔍 Look for these console messages:")
    print("   - '🔧 DiamondImageGallery props:' (component loaded)")
    print("   - '🔧 Upload Image button clicked' (button works)")
    print("   - '🔧 Modal closing' (modal opens/closes)")
    print("   - '🔧 handleUpload called:' (upload process starts)")
    print("   - '🔧 handleImageUpload called:' (API call starts)")
    print("\n💡 If you don't see these messages:")
    print("   - Check if the diamond has an ID")
    print("   - Check if the component is rendering")
    print("   - Check for JavaScript errors in console")
    print("   - Verify the handlers are being passed correctly")
    
    return True

if __name__ == "__main__":
    success = test_frontend_debugging()
    if success:
        print("\n🚀 Ready for frontend debugging!")
    else:
        print("\n❌ Setup failed - check the logs above")
