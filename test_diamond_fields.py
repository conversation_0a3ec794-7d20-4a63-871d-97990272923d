#!/usr/bin/env python3
"""
Test diamond update with various fields
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_diamond_fields():
    print("🔍 Testing Diamond Update with Various Fields...")
    
    # Login
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    
    diamond_id = 1
    
    # Test with size_mm field (doesn't exist in backend)
    print("\n🔧 Testing with size_mm field...")
    update_with_size_mm = {
        "carat": 1.5,
        "color": "D",
        "clarity": "VVS1",
        "certificate_no": "TEST1753041088",
        "size_mm": "7.5x7.5x4.5"  # This field doesn't exist in backend
    }
    
    response = session.put(f"{BASE_URL}/diamonds/{diamond_id}", json=update_with_size_mm)
    print(f"Status with size_mm: {response.status_code}")
    if response.status_code != 200:
        try:
            error_data = response.json()
            print(f"Error: {json.dumps(error_data, indent=2)}")
        except:
            print(f"Raw error: {response.text}")
    
    # Test with vendor_id as string (frontend sends string)
    print("\n🔧 Testing with vendor_id as string...")
    update_with_vendor_string = {
        "carat": 1.5,
        "color": "D",
        "clarity": "VVS1",
        "certificate_no": "TEST1753041088",
        "vendor_id": "1"  # String instead of int
    }
    
    response = session.put(f"{BASE_URL}/diamonds/{diamond_id}", json=update_with_vendor_string)
    print(f"Status with vendor_id string: {response.status_code}")
    if response.status_code != 200:
        try:
            error_data = response.json()
            print(f"Error: {json.dumps(error_data, indent=2)}")
        except:
            print(f"Raw error: {response.text}")
    
    # Test with shape_id (required field)
    print("\n🔧 Testing with shape_id...")
    update_with_shape = {
        "shape_id": 1,
        "carat": 1.5,
        "color": "D",
        "clarity": "VVS1",
        "certificate_no": "TEST1753041088"
    }
    
    response = session.put(f"{BASE_URL}/diamonds/{diamond_id}", json=update_with_shape)
    print(f"Status with shape_id: {response.status_code}")
    if response.status_code != 200:
        try:
            error_data = response.json()
            print(f"Error: {json.dumps(error_data, indent=2)}")
        except:
            print(f"Raw error: {response.text}")

if __name__ == "__main__":
    test_diamond_fields()
