// Sales Industry Standards and Constants

export const SALE_STATUSES = [
  { value: 'draft', label: 'Draft', color: 'gray' },
  { value: 'confirmed', label: 'Confirmed', color: 'blue' },
  { value: 'processing', label: 'Processing', color: 'yellow' },
  { value: 'completed', label: 'Completed', color: 'green' },
  { value: 'cancelled', label: 'Cancelled', color: 'red' },
  { value: 'returned', label: 'Returned', color: 'orange' },
  { value: 'exchanged', label: 'Exchanged', color: 'purple' },
  { value: 'refunded', label: 'Refunded', color: 'pink' }
];

export const PAYMENT_STATUSES = [
  { value: 'pending', label: 'Pending', color: 'gray' },
  { value: 'partial', label: 'Partial', color: 'yellow' },
  { value: 'paid', label: 'Paid', color: 'green' },
  { value: 'refunded', label: 'Refunded', color: 'blue' },
  { value: 'cancelled', label: 'Cancelled', color: 'red' },
  { value: 'overdue', label: 'Overdue', color: 'red' }
];

export const PAYMENT_METHODS = [
  { value: 'cash', label: 'Cash' },
  { value: 'card', label: 'Credit/Debit Card' },
  { value: 'upi', label: 'UPI' },
  { value: 'bank_transfer', label: 'Bank Transfer' },
  { value: 'cheque', label: 'Cheque' },
  { value: 'emi', label: 'EMI' },
  { value: 'crypto', label: 'Cryptocurrency' }
];

export const SALE_TYPES = [
  { value: 'retail', label: 'Retail' },
  { value: 'wholesale', label: 'Wholesale' },
  { value: 'online', label: 'Online' },
  { value: 'custom', label: 'Custom Order' },
  { value: 'exhibition', label: 'Exhibition' },
  { value: 'b2b', label: 'B2B' }
];

export const SALES_CHANNELS = [
  { value: 'store', label: 'Physical Store' },
  { value: 'online', label: 'Online Store' },
  { value: 'phone', label: 'Phone Order' },
  { value: 'exhibition', label: 'Exhibition/Fair' },
  { value: 'home_visit', label: 'Home Visit' },
  { value: 'referral', label: 'Referral' }
];

export const DELIVERY_METHODS = [
  { value: 'pickup', label: 'Store Pickup' },
  { value: 'home_delivery', label: 'Home Delivery' },
  { value: 'courier', label: 'Courier Service' },
  { value: 'express', label: 'Express Delivery' },
  { value: 'international', label: 'International Shipping' }
];

export const DELIVERY_STATUSES = [
  { value: 'pending', label: 'Pending', color: 'gray' },
  { value: 'packed', label: 'Packed', color: 'blue' },
  { value: 'shipped', label: 'Shipped', color: 'yellow' },
  { value: 'in_transit', label: 'In Transit', color: 'orange' },
  { value: 'delivered', label: 'Delivered', color: 'green' },
  { value: 'returned', label: 'Returned', color: 'red' }
];

export const PRIORITIES = [
  { value: 'urgent', label: 'Urgent', color: 'red' },
  { value: 'high', label: 'High', color: 'orange' },
  { value: 'normal', label: 'Normal', color: 'blue' },
  { value: 'low', label: 'Low', color: 'gray' }
];

export const PAYMENT_TERMS = [
  { value: 'immediate', label: 'Immediate' },
  { value: '7_days', label: '7 Days' },
  { value: '15_days', label: '15 Days' },
  { value: '30_days', label: '30 Days' },
  { value: '45_days', label: '45 Days' },
  { value: '60_days', label: '60 Days' },
  { value: '90_days', label: '90 Days' }
];

export const CUSTOMER_SOURCES = [
  { value: 'walk_in', label: 'Walk-in' },
  { value: 'referral', label: 'Referral' },
  { value: 'online_search', label: 'Online Search' },
  { value: 'social_media', label: 'Social Media' },
  { value: 'advertisement', label: 'Advertisement' },
  { value: 'exhibition', label: 'Exhibition' },
  { value: 'repeat_customer', label: 'Repeat Customer' },
  { value: 'word_of_mouth', label: 'Word of Mouth' }
];

export const INDIAN_STATES = [
  { value: 'Andhra Pradesh', label: 'Andhra Pradesh' },
  { value: 'Arunachal Pradesh', label: 'Arunachal Pradesh' },
  { value: 'Assam', label: 'Assam' },
  { value: 'Bihar', label: 'Bihar' },
  { value: 'Chhattisgarh', label: 'Chhattisgarh' },
  { value: 'Goa', label: 'Goa' },
  { value: 'Gujarat', label: 'Gujarat' },
  { value: 'Haryana', label: 'Haryana' },
  { value: 'Himachal Pradesh', label: 'Himachal Pradesh' },
  { value: 'Jharkhand', label: 'Jharkhand' },
  { value: 'Karnataka', label: 'Karnataka' },
  { value: 'Kerala', label: 'Kerala' },
  { value: 'Madhya Pradesh', label: 'Madhya Pradesh' },
  { value: 'Maharashtra', label: 'Maharashtra' },
  { value: 'Manipur', label: 'Manipur' },
  { value: 'Meghalaya', label: 'Meghalaya' },
  { value: 'Mizoram', label: 'Mizoram' },
  { value: 'Nagaland', label: 'Nagaland' },
  { value: 'Odisha', label: 'Odisha' },
  { value: 'Punjab', label: 'Punjab' },
  { value: 'Rajasthan', label: 'Rajasthan' },
  { value: 'Sikkim', label: 'Sikkim' },
  { value: 'Tamil Nadu', label: 'Tamil Nadu' },
  { value: 'Telangana', label: 'Telangana' },
  { value: 'Tripura', label: 'Tripura' },
  { value: 'Uttar Pradesh', label: 'Uttar Pradesh' },
  { value: 'Uttarakhand', label: 'Uttarakhand' },
  { value: 'West Bengal', label: 'West Bengal' },
  { value: 'Delhi', label: 'Delhi' }
];

// Validation patterns
export const VALIDATION_PATTERNS = {
  phone: /^[+]?[1-9]?[0-9]{7,15}$/,
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  postalCode: /^[1-9][0-9]{5}$/,
  gst: /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/
};

// Validation ranges
export const VALIDATION_RANGES = {
  total_amount: { min: 0, max: 10000000 },
  discount_percentage: { min: 0, max: 100 },
  tax_percentage: { min: 0, max: 50 },
  commission_percentage: { min: 0, max: 50 },
  return_period: { min: 0, max: 365 },
  warranty_period: { min: 0, max: 120 }
};

// Helper functions
export const getSaleStatusLabel = (status: string) => {
  const statusOption = SALE_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.label : status;
};

export const getSaleStatusColor = (status: string) => {
  const statusOption = SALE_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.color : 'gray';
};

export const getPaymentStatusLabel = (status: string) => {
  const statusOption = PAYMENT_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.label : status;
};

export const getPaymentStatusColor = (status: string) => {
  const statusOption = PAYMENT_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.color : 'gray';
};

export const getDeliveryStatusLabel = (status: string) => {
  const statusOption = DELIVERY_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.label : status;
};

export const getDeliveryStatusColor = (status: string) => {
  const statusOption = DELIVERY_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.color : 'gray';
};

export const getPriorityLabel = (priority: string) => {
  const priorityOption = PRIORITIES.find(p => p.value === priority);
  return priorityOption ? priorityOption.label : priority;
};

export const getPriorityColor = (priority: string) => {
  const priorityOption = PRIORITIES.find(p => p.value === priority);
  return priorityOption ? priorityOption.color : 'gray';
};

export const calculateSubtotal = (items: any[]): number => {
  return items.reduce((sum, item) => sum + (item.unit_price * item.quantity), 0);
};

export const calculateDiscountAmount = (subtotal: number, discountPercentage: number): number => {
  return (subtotal * discountPercentage) / 100;
};

export const calculateTaxAmount = (subtotal: number, discountAmount: number, taxPercentage: number): number => {
  const taxableAmount = subtotal - discountAmount;
  return (taxableAmount * taxPercentage) / 100;
};

export const calculateTotal = (
  subtotal: number,
  discountAmount: number,
  taxAmount: number,
  shippingCharges: number = 0,
  otherCharges: number = 0
): number => {
  return subtotal - discountAmount + taxAmount + shippingCharges + otherCharges;
};

export const calculateBalance = (totalAmount: number, amountPaid: number): number => {
  return Math.max(0, totalAmount - amountPaid);
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2
  }).format(amount);
};

export const formatPercentage = (percentage: number): string => {
  return `${percentage.toFixed(2)}%`;
};

export const generateInvoiceNumber = (): string => {
  const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 12);
  return `INV${timestamp}`;
};

export const generateOrderNumber = (): string => {
  const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 12);
  return `ORD${timestamp}`;
};

export const isOverdue = (dueDate: string, paymentStatus: string): boolean => {
  if (!dueDate || ['paid', 'refunded'].includes(paymentStatus)) return false;
  const today = new Date();
  const due = new Date(dueDate);
  return today > due;
};

export const getDaysOverdue = (dueDate: string): number => {
  if (!dueDate) return 0;
  const today = new Date();
  const due = new Date(dueDate);
  const diffTime = today.getTime() - due.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
};

export const canBeReturned = (saleDate: string, isReturnable: boolean, returnPeriod: number): boolean => {
  if (!isReturnable) return false;
  if (!saleDate || !returnPeriod) return false;
  
  const today = new Date();
  const sale = new Date(saleDate);
  const diffTime = today.getTime() - sale.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays <= returnPeriod;
};

export const validateSaleData = (data: any): string[] => {
  const errors: string[] = [];
  
  // Required fields
  if (!data.customer_name) {
    errors.push('Customer name is required');
  }
  
  if (!data.total_amount || data.total_amount <= 0) {
    errors.push('Total amount must be positive');
  }
  
  // Email validation
  if (data.customer_email && !VALIDATION_PATTERNS.email.test(data.customer_email)) {
    errors.push('Invalid email format');
  }
  
  // Phone validation
  if (data.customer_phone && !VALIDATION_PATTERNS.phone.test(data.customer_phone)) {
    errors.push('Invalid phone number format');
  }
  
  // Postal code validation (for India)
  if (data.customer_postal_code && data.customer_country === 'India' && 
      !VALIDATION_PATTERNS.postalCode.test(data.customer_postal_code)) {
    errors.push('Invalid postal code format');
  }
  
  // Amount validations
  if (data.discount_percentage && (data.discount_percentage < 0 || data.discount_percentage > 100)) {
    errors.push('Discount percentage must be between 0 and 100');
  }
  
  if (data.tax_percentage && (data.tax_percentage < 0 || data.tax_percentage > 50)) {
    errors.push('Tax percentage must be between 0 and 50');
  }
  
  if (data.amount_paid && data.amount_paid < 0) {
    errors.push('Amount paid cannot be negative');
  }
  
  if (data.amount_paid && data.total_amount && data.amount_paid > data.total_amount) {
    errors.push('Amount paid cannot exceed total amount');
  }
  
  // Date validations
  if (data.due_date && data.sale_date) {
    const saleDate = new Date(data.sale_date);
    const dueDate = new Date(data.due_date);
    if (dueDate < saleDate) {
      errors.push('Due date cannot be before sale date');
    }
  }
  
  if (data.delivery_date && data.sale_date) {
    const saleDate = new Date(data.sale_date);
    const deliveryDate = new Date(data.delivery_date);
    if (deliveryDate < saleDate) {
      errors.push('Delivery date cannot be before sale date');
    }
  }
  
  return errors;
};
