import requests
from tests.conftest import get_tokens
from datetime import datetime

def test_sale_crud(base_url, test_user):
    access_token, _ = get_tokens(base_url, test_user)
    headers = {"Authorization": f"Bearer {access_token}"}
    # Create a vendor and jewelry
    vendor = {
        "name": "Sale Vendor",
        "gst_number": f"SALE{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
        "contact_number": "2223334444",
        "address": "101 Sale Ave"
    }
    r = requests.post(f"{base_url}/vendors/", json=vendor, headers=headers, timeout=10)
    if r.status_code != 201:
        print("Create vendor failed:", r.status_code, r.text)
    assert r.status_code == 201
    vendor_id = r.json()["id"]
    jewelry = {
        "name": "Sale Ring",
        "design_code": "SALE001",
        "vendor_id": vendor_id,
        "gross_weight": 3.0,
        "metal_type": "Platinum",
        "received_date": "2025-06-21",
        "status": "in_stock",
        "diamonds": []
    }
    r = requests.post(f"{base_url}/jewelry/", json=jewelry, headers=headers, timeout=10)
    if r.status_code != 201:
        print("Create jewelry failed:", r.status_code, r.text)
    assert r.status_code == 201
    jewelry_id = r.json()["id"]
    # Create sale
    sale = {
        "invoice_no": f"INV{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
        "customer_name": "Customer X",
        "sale_date": "2025-06-21",
        "total_amount": 5000.0,
        "payment_status": "unpaid",
        "jewelry_id": jewelry_id
    }
    r = requests.post(f"{base_url}/sales/", json=sale, headers=headers, timeout=10)
    assert r.status_code == 201
    sale_id = r.json()["id"]
    # Get
    r = requests.get(f"{base_url}/sales/{sale_id}", headers=headers, timeout=10)
    assert r.status_code == 200
    # Cleanup
    requests.delete(f"{base_url}/jewelry/{jewelry_id}", headers=headers, timeout=10)
    requests.delete(f"{base_url}/vendors/{vendor_id}", headers=headers, timeout=10)
