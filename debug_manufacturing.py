#!/usr/bin/env python3
"""
Debug manufacturing creation
"""

import requests
from datetime import date, timedelta

BASE_URL = "http://localhost:8000/api"

def debug_manufacturing():
    print("🔍 Debugging Manufacturing Creation")
    print("=" * 35)
    
    # Login
    response = requests.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    token = response.json().get('access_token')
    headers = {'Authorization': f'Bearer {token}'}
    print("✅ Authenticated")
    
    # Check existing manufacturing requests
    response = requests.get(f"{BASE_URL}/manufacturing", headers=headers)
    print(f"\nExisting manufacturing: {response.status_code}")
    if response.status_code == 200:
        requests_list = response.json()
        print(f"Found {len(requests_list)} manufacturing requests")
    
    # Get vendor
    response = requests.get(f"{BASE_URL}/vendors", headers=headers)
    if response.status_code == 200:
        vendors = response.json()
        if vendors:
            vendor_id = vendors[0]['id']
            print(f"Using vendor ID: {vendor_id}")
        else:
            print("No vendors available")
            return
    
    # Try creating minimal manufacturing request (without diamonds)
    today = date.today()
    expected_date = today + timedelta(days=30)
    
    minimal_request = {
        "vendor_id": vendor_id,
        "order_type": "Diamond Cutting",
        "expected_return_date": expected_date.isoformat(),
        "estimated_cost": 5000
    }
    
    print(f"\nTrying minimal manufacturing request...")
    response = requests.post(f"{BASE_URL}/manufacturing", json=minimal_request, headers=headers)
    print(f"Create manufacturing (minimal): {response.status_code}")
    
    if response.status_code in [200, 201]:
        manufacturing = response.json()
        request_id = manufacturing.get('id')
        print(f"✅ Success! Created request ID: {request_id}")
        
        # Test UPDATE
        update_data = {"status": "in_progress", "notes": "Test update"}
        response = requests.put(f"{BASE_URL}/manufacturing/{request_id}", json=update_data, headers=headers)
        print(f"Update manufacturing: {response.status_code}")
        
        # Test DELETE
        response = requests.delete(f"{BASE_URL}/manufacturing/{request_id}", headers=headers)
        print(f"Delete manufacturing: {response.status_code}")
        
    else:
        print(f"❌ Failed: {response.text}")

if __name__ == "__main__":
    debug_manufacturing()
