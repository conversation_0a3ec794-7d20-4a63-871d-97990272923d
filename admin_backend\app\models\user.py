from app import db
from datetime import datetime
from marshmallow import Schema, fields, validate
from passlib.hash import argon2
from app.utils.error_handler import APIError
from app.config import Config
from sqlalchemy.orm import relationship

class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    is_active = db.Column(db.<PERSON>, default=True)
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id', name='fk_users_role_id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    failed_login_attempts = db.Column(db.Integer, default=0)
    account_locked_until = db.Column(db.DateTime)
      # Adjusted to use string-based lazy reference for Role
    role = db.relationship('Role', back_populates='users')
    
    def __init__(self, email, password, first_name, last_name, role=None, role_name='user'):
        self.email = email
        self.password = password
        self.first_name = first_name
        self.last_name = last_name
        if role:
            self.role = role
        else:
            # Late import to avoid circular dependency
            from app.models.role import Role
            self.role = Role.get_by_name(role_name)
            if not self.role:
                raise APIError(f'Role {role_name} not found', 500)
    
    @property
    def password(self):
        raise AttributeError('password is not a readable attribute')
    
    @password.setter
    def password(self, password):
        # Use Argon2 for password hashing
        self.password_hash = argon2.hash(password)
    
    def verify_password(self, password):
        return argon2.verify(password, self.password_hash)
    
    def is_admin(self):
        return self.role and self.role.name == 'admin'
    
    def record_login_success(self):
        try:
            self.last_login = datetime.utcnow()
            self.failed_login_attempts = 0
            self.account_locked_until = None
            db.session.commit()
        except Exception:
            db.session.rollback()
            raise APIError('Failed to record login success', 500)
    
    def record_login_failure(self):
        try:
            self.failed_login_attempts += 1
            if self.failed_login_attempts >= Config.MAX_LOGIN_ATTEMPTS:
                # Lock account using config duration
                self.account_locked_until = datetime.utcnow() + Config.ACCOUNT_LOCKOUT_DURATION
            db.session.commit()
        except Exception:
            db.session.rollback()
            raise APIError('Failed to record login failure', 500)
    
    def is_account_locked(self):
        if self.account_locked_until:
            return datetime.utcnow() < self.account_locked_until
        return False
    
    def to_dict(self):
        return {
            'id': self.id,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'is_active': self.is_active,
            'role': self.role.name if self.role else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

class UserSchema(Schema):
    id = fields.Int(dump_only=True)
    email = fields.Email(required=True)
    password = fields.Str(required=True, validate=validate.Length(min=8))
    first_name = fields.Str(required=True, validate=validate.Length(min=1))
    last_name = fields.Str(required=True, validate=validate.Length(min=1))
    role = fields.Str(dump_only=True)
    is_active = fields.Bool(load_default=True)
    created_at = fields.DateTime(dump_only=True)
    updated_at = fields.DateTime(dump_only=True)
    last_login = fields.DateTime(dump_only=True)
    
    class Meta:
        model = User
        load_instance = True