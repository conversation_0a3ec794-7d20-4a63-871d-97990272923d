#!/usr/bin/env python3
"""
Test sales directly through database
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'admin_backend'))

from app import create_app, db
from app.models.sale import Sale
from app.models.jewelry import JewelryItem
from datetime import date

def test_sales_direct():
    print("🔍 Testing Sales Direct Database Access")
    print("=" * 40)
    
    app = create_app()
    with app.app_context():
        # Get all sales
        sales = Sale.query.all()
        print(f"Found {len(sales)} sales in database")
        
        # Get available jewelry
        jewelry_items = JewelryItem.query.filter_by(status='in_stock').all()
        print(f"Found {len(jewelry_items)} available jewelry items")
        
        if jewelry_items:
            jewelry = jewelry_items[0]
            print(f"Using jewelry: {jewelry.name} (ID: {jewelry.id})")
            
            # Create sale with minimal required fields
            try:
                sale = Sale(
                    invoice_no=f"TEST{int(__import__('time').time())}",
                    customer_name="Test Customer Direct",
                    total_amount=40000,
                    sale_date=date.today()
                )
                
                db.session.add(sale)
                db.session.commit()
                
                print(f"✅ Sale created: ID {sale.id}")
                
                # Test to_dict method
                try:
                    sale_dict = sale.to_dict()
                    print(f"✅ to_dict() success: {len(sale_dict)} fields")
                except Exception as e:
                    print(f"❌ Error in to_dict(): {str(e)}")
                
                # Clean up
                db.session.delete(sale)
                db.session.commit()
                print("✅ Sale cleaned up")
                
            except Exception as e:
                print(f"❌ Error creating sale: {str(e)}")
                db.session.rollback()
        else:
            print("❌ No available jewelry for testing")

if __name__ == "__main__":
    test_sales_direct()
