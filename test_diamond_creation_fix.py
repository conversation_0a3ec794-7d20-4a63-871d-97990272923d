#!/usr/bin/env python3
"""
Test and fix diamond creation
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api"
LOGIN_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

def test_diamond_creation():
    print("🔧 Testing Diamond Creation Fix")
    print("=" * 40)
    
    # Authenticate
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json=LOGIN_CREDENTIALS)
    if response.status_code != 200:
        print("❌ Authentication failed")
        return
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Step 1: Create a vendor first
    print("\n1. Creating test vendor...")
    vendor_data = {
        "name": "Test Vendor",
        "company_name": "Test Company Ltd",
        "gst_number": "22AAAAA0000A1Z5",
        "contact_number": "+91-9876543210",
        "email": "<EMAIL>",
        "address": "123 Test Street, Test City",
        "city": "Mumbai",
        "state": "Maharashtra",
        "country": "India",
        "postal_code": "400001"
    }
    
    response = session.post(f"{BASE_URL}/vendors", json=vendor_data)
    if response.status_code in [200, 201]:
        vendor = response.json()
        vendor_id = vendor.get('id')
        print(f"✅ Vendor created: ID {vendor_id}")
    else:
        print(f"❌ Vendor creation failed: {response.status_code}")
        print(f"Error: {response.text}")
        return
    
    # Step 2: Get shapes
    print("\n2. Getting shapes...")
    response = session.get(f"{BASE_URL}/shapes")
    if response.status_code == 200:
        shapes = response.json()
        if shapes:
            shape_id = shapes[0]['id']
            print(f"✅ Using shape: {shapes[0]['name']} (ID: {shape_id})")
        else:
            print("❌ No shapes available")
            return
    else:
        print(f"❌ Failed to get shapes: {response.status_code}")
        return
    
    # Step 3: Create diamond with all required fields
    print("\n3. Creating diamond...")
    diamond_data = {
        "shape_id": shape_id,
        "vendor_id": vendor_id,
        "size_mm": "7.5x7.5x4.5",
        "carat": 1.5,
        "color": "D",
        "clarity": "VVS1",
        "cut_grade": "Excellent",
        "polish": "Excellent",
        "symmetry": "Excellent",
        "fluorescence": "None",
        "certificate_no": f"TEST{int(time.time())}",
        "certification_lab": "GIA",
        "cost_price": 50000,
        "retail_price": 75000,
        "quantity": 1,
        "status": "in_stock",
        "location": "Vault A",
        "notes": "Test diamond"
    }
    
    response = session.post(f"{BASE_URL}/diamonds", json=diamond_data)
    if response.status_code in [200, 201]:
        diamond = response.json()
        diamond_id = diamond.get('id')
        print(f"✅ Diamond created: ID {diamond_id}")
        
        # Test profit margin
        profit_margin = diamond.get('profit_margin')
        expected_margin = ((75000 - 50000) / 75000) * 100
        if profit_margin is not None:
            print(f"✅ Profit margin: {profit_margin:.2f}% (expected: {expected_margin:.2f}%)")
        else:
            print(f"⚠️  Profit margin not calculated (got None)")
        
        # Cleanup
        print("\n4. Cleaning up...")
        session.delete(f"{BASE_URL}/diamonds/{diamond_id}")
        session.delete(f"{BASE_URL}/vendors/{vendor_id}")
        print("✅ Cleanup complete")
        
    else:
        print(f"❌ Diamond creation failed: {response.status_code}")
        print(f"Error: {response.text}")
        
        # Cleanup vendor
        session.delete(f"{BASE_URL}/vendors/{vendor_id}")

if __name__ == "__main__":
    test_diamond_creation()
