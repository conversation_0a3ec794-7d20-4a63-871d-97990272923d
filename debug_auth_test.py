#!/usr/bin/env python3
"""
Debug authentication and token issues
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"
LOGIN_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

def debug_auth():
    print("🔍 Debugging Authentication Issues")
    print("=" * 50)
    
    # Step 1: Test login
    print("1. Testing login...")
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=LOGIN_CREDENTIALS)
        print(f"   Login Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            print(f"   Token received: {token[:50]}..." if token else "   No token received")
            
            if token:
                # Step 2: Test protected endpoint with token
                print("\n2. Testing protected endpoint...")
                headers = {'Authorization': f'Bearer {token}'}
                
                # Test diamonds endpoint
                response = requests.get(f"{BASE_URL}/diamonds", headers=headers)
                print(f"   Diamonds Status: {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
                # Test vendors endpoint
                response = requests.get(f"{BASE_URL}/vendors", headers=headers)
                print(f"   Vendors Status: {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
                # Test dashboard endpoint
                response = requests.get(f"{BASE_URL}/dashboard", headers=headers)
                print(f"   Dashboard Status: {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
        else:
            print("   ❌ Login failed")
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")

if __name__ == "__main__":
    debug_auth()
