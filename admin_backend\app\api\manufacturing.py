from flask_restx import Namespace, Resource, fields
from flask import request
from app.models.manufacturing import ManufacturingRequest, manufacturing_diamonds
from app.models.diamond import Diamond
from app import db
from app.utils.decorators import token_required
from datetime import datetime

manufacturing_ns = Namespace('manufacturing', description='Manufacturing operations', path='/manufacturing')

# Enhanced Swagger models
diamond_link_model = manufacturing_ns.model('ManufacturingDiamond', {
    'diamond_id': fields.Integer(required=True),
    'quantity': fields.Integer(required=True),
    'original_weight': fields.Float(description='Original diamond weight'),
    'final_weight': fields.Float(description='Final weight after manufacturing'),
    'loss_weight': fields.Float(description='Weight loss during manufacturing'),
    'notes': fields.String(description='Notes for this specific diamond')
})

manufacturing_model = manufacturing_ns.model('Manufacturing', {
    'id': fields.Integer(readOnly=True),
    'order_number': fields.String(readOnly=True, description='Unique order number'),
    'vendor_id': fields.Integer(required=True),

    # Order Details
    'order_type': fields.String(description='Type of manufacturing work'),
    'priority': fields.String(description='Order priority'),
    'description': fields.String(description='Detailed description of work'),
    'special_instructions': fields.String(description='Special instructions'),

    # Dates and Timeline
    'sent_date': fields.String(description='Date sent to vendor'),
    'expected_return_date': fields.String(description='Expected return date'),
    'actual_return_date': fields.String(description='Actual return date'),
    'promised_delivery_date': fields.String(description='Date promised to customer'),

    # Status Tracking
    'status': fields.String(description='Current status'),
    'progress_percentage': fields.Integer(description='Progress percentage (0-100)'),

    # Quality and Inspection
    'quality_check_status': fields.String(description='Quality check status'),
    'quality_notes': fields.String(description='Quality inspection notes'),
    'inspector_name': fields.String(description='Inspector name'),
    'inspection_date': fields.String(description='Inspection date'),

    # Financial Information
    'estimated_cost': fields.Float(description='Estimated cost'),
    'actual_cost': fields.Float(description='Actual cost'),
    'advance_paid': fields.Float(description='Advance payment'),
    'balance_amount': fields.Float(description='Balance amount'),
    'payment_status': fields.String(description='Payment status'),

    # Weight Tracking
    'total_original_weight': fields.Float(description='Total original weight'),
    'total_final_weight': fields.Float(description='Total final weight'),
    'total_loss_weight': fields.Float(description='Total weight loss'),
    'loss_percentage': fields.Float(description='Loss percentage'),

    # Additional Information
    'notes': fields.String(description='General notes'),
    'internal_notes': fields.String(description='Internal notes'),
    'images_path': fields.String(description='Path to images/documents'),

    # Tracking and Audit
    'created_by': fields.String(description='Created by user'),
    'updated_by': fields.String(description='Last updated by user'),
    'created_at': fields.String(readOnly=True),
    'updated_at': fields.String(readOnly=True),

    # Relationships
    'diamonds': fields.List(fields.Nested(diamond_link_model)),
    'vendor': fields.Nested(manufacturing_ns.model('VendorShort', {
        'id': fields.Integer,
        'name': fields.String,
        'contact_number': fields.String
    })),

    # Calculated Fields
    'is_overdue': fields.Boolean(readOnly=True, description='Is order overdue'),
    'days_remaining': fields.Integer(readOnly=True, description='Days remaining'),

    # Legacy fields
    'return_date': fields.String(description='Legacy field - use actual_return_date')
})

create_manufacturing_model = manufacturing_ns.model('CreateManufacturing', {
    'vendor_id': fields.Integer(required=True),
    'order_type': fields.String(required=True),
    'priority': fields.String(description='Order priority'),
    'description': fields.String(description='Work description'),
    'special_instructions': fields.String(description='Special instructions'),
    'sent_date': fields.String,
    'expected_return_date': fields.String(required=True),
    'promised_delivery_date': fields.String,
    'estimated_cost': fields.Float,
    'advance_paid': fields.Float,
    'notes': fields.String,
    'diamonds': fields.List(fields.Nested(diamond_link_model))
})

update_manufacturing_model = manufacturing_ns.model('UpdateManufacturing', {
    'vendor_id': fields.Integer(required=False),
    'order_type': fields.String(required=False),
    'priority': fields.String(required=False),
    'description': fields.String(required=False),
    'special_instructions': fields.String(required=False),
    'sent_date': fields.String(required=False),
    'expected_return_date': fields.String(required=False),
    'actual_return_date': fields.String(required=False),
    'promised_delivery_date': fields.String(required=False),
    'status': fields.String(required=False),
    'progress_percentage': fields.Integer(required=False),
    'quality_check_status': fields.String(required=False),
    'quality_notes': fields.String(required=False),
    'inspector_name': fields.String(required=False),
    'inspection_date': fields.String(required=False),
    'estimated_cost': fields.Float(required=False),
    'actual_cost': fields.Float(required=False),
    'advance_paid': fields.Float(required=False),
    'payment_status': fields.String(required=False),
    'total_original_weight': fields.Float(required=False),
    'total_final_weight': fields.Float(required=False),
    'notes': fields.String(required=False),
    'internal_notes': fields.String(required=False),
    'updated_by': fields.String(required=False)
})

error_model = manufacturing_ns.model('ManufacturingError', {
    'status': fields.String(description='Error status'),
    'message': fields.String(description='Error message'),
    'status_code': fields.Integer(description='HTTP status code')
})

def manufacturing_to_dict(r):
    """Convert manufacturing request to dictionary using the model's to_dict method."""
    return r.to_dict()

@manufacturing_ns.route('/')
class ManufacturingList(Resource):
    @manufacturing_ns.doc('list_manufacturing')
    @manufacturing_ns.marshal_list_with(manufacturing_model)
    @manufacturing_ns.response(200, 'List of manufacturing requests', [manufacturing_model])
    @manufacturing_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """List all manufacturing requests (optionally filter by status/vendor_id)"""
        query = ManufacturingRequest.query
        for field in ['status', 'vendor_id']:
            value = request.args.get(field)
            if value:
                query = query.filter(getattr(ManufacturingRequest, field) == value)
        requests = query.all()
        return [manufacturing_to_dict(r) for r in requests], 200

    @manufacturing_ns.doc('create_manufacturing')
    @manufacturing_ns.expect(create_manufacturing_model)
    @manufacturing_ns.marshal_with(manufacturing_model, code=201)
    @manufacturing_ns.response(400, 'Validation error', error_model)
    @manufacturing_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def post(self):
        """Create a new manufacturing request"""
        data = request.get_json()
        try:
            # Start a transaction
            with db.session.begin_nested():
                r = ManufacturingRequest(
                    vendor_id=data['vendor_id'],
                    sent_date=datetime.strptime(data['sent_date'], '%Y-%m-%d').date() if 'sent_date' in data else None,
                    expected_return_date=datetime.strptime(data['expected_return_date'], '%Y-%m-%d').date() if 'expected_return_date' in data else None,
                    status='open'
                )
                db.session.add(r)
                db.session.flush()  # Get r.id to use in the association table

                for d_data in data.get('diamonds', []):
                    diamond_id = d_data['diamond_id']
                    quantity_to_use = int(d_data['quantity'])  # Convert to integer

                    # Lock the diamond row for update
                    diamond = Diamond.query.with_for_update().get(diamond_id)

                    if not diamond:
                        manufacturing_ns.abort(404, f'Diamond {diamond_id} not found')

                    if diamond.quantity < quantity_to_use:
                        manufacturing_ns.abort(400, f'Not enough stock for diamond {diamond.id}. Available: {diamond.quantity}, Requested: {quantity_to_use}')

                    # Deduct stock
                    diamond.quantity -= quantity_to_use
                    
                    # Add to association table
                    db.session.execute(manufacturing_diamonds.insert().values(
                        manufacturing_id=r.id, diamond_id=diamond.id, quantity=quantity_to_use
                    ))

            db.session.commit()
            return manufacturing_to_dict(r), 201
        except Exception as e:
            db.session.rollback()
            manufacturing_ns.abort(400, f'Failed to create manufacturing request: {str(e)}')

@manufacturing_ns.route('/<int:request_id>')
@manufacturing_ns.param('request_id', 'The manufacturing request identifier')
@manufacturing_ns.response(404, 'Manufacturing request not found', error_model)
class ManufacturingResource(Resource):
    @manufacturing_ns.doc('get_manufacturing')
    @manufacturing_ns.marshal_with(manufacturing_model)
    @manufacturing_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self, request_id):
        """Get a manufacturing request by ID"""
        r = ManufacturingRequest.query.get_or_404(request_id)
        return manufacturing_to_dict(r), 200

    @manufacturing_ns.doc('update_manufacturing')
    @manufacturing_ns.expect(update_manufacturing_model)
    @manufacturing_ns.marshal_with(manufacturing_model)
    @manufacturing_ns.response(400, 'Validation error', error_model)
    @manufacturing_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def put(self, request_id):
        """Update a manufacturing request"""
        r = ManufacturingRequest.query.get_or_404(request_id)
        data = request.get_json()

        try:
            # Update allowed fields
            allowed_fields = [
                'status', 'notes', 'actual_cost', 'actual_return_date',
                'quality_check_passed', 'inspector_name', 'rework_required'
            ]

            for field in allowed_fields:
                if field in data:
                    setattr(r, field, data[field])

            # Update timestamps
            r.updated_at = datetime.utcnow()

            db.session.commit()
            return manufacturing_to_dict(r), 200

        except Exception as e:
            db.session.rollback()
            manufacturing_ns.abort(400, f'Failed to update manufacturing request: {str(e)}')

    @manufacturing_ns.doc('delete_manufacturing')
    @manufacturing_ns.response(204, 'Manufacturing request deleted')
    @manufacturing_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def delete(self, request_id):
        """Delete a manufacturing request"""
        r = ManufacturingRequest.query.get_or_404(request_id)

        try:
            # Return diamonds to stock if they were deducted
            for diamond_request in r.diamonds:
                diamond = diamond_request.diamond
                diamond.quantity += diamond_request.quantity

            db.session.delete(r)
            db.session.commit()
            return '', 204

        except Exception as e:
            db.session.rollback()
            manufacturing_ns.abort(400, f'Failed to delete manufacturing request: {str(e)}')

@manufacturing_ns.route('/<int:request_id>/return_to_stock')
@manufacturing_ns.param('request_id', 'The manufacturing request identifier')
class ManufacturingReturnToStock(Resource):
    @manufacturing_ns.doc('return_to_stock')
    @token_required
    def post(self, request_id):
        """Return diamonds from a manufacturing request to stock and complete the request."""
        req = ManufacturingRequest.query.get_or_404(request_id)
        if req.status == 'completed':
            return {'message': 'Request already completed'}, 400

        links = db.session.execute(manufacturing_diamonds.select().where(
            manufacturing_diamonds.c.manufacturing_id == req.id)).fetchall()

        for link in links:
            diamond = Diamond.query.get(link.diamond_id)
            if diamond:
                diamond.quantity += link.quantity
                if diamond.status == 'used':
                    diamond.status = 'in_stock'

        req.status = 'completed'
        req.return_date = datetime.utcnow().date()
        db.session.commit()

        return {'message': 'Diamonds returned to stock and request completed.'}, 200

@manufacturing_ns.route('/history')
class ManufacturingHistory(Resource):
    @manufacturing_ns.doc('manufacturing_history')
    @manufacturing_ns.marshal_list_with(manufacturing_model)
    @token_required
    def get(self):
        """Get manufacturing request history (optionally filter by vendor_id and date range)"""
        query = ManufacturingRequest.query
        for field in ['vendor_id']:
            value = request.args.get(field)
            if value:
                query = query.filter(getattr(ManufacturingRequest, field) == value)
        start = request.args.get('start_date')
        end = request.args.get('end_date')
        if start:
            query = query.filter(ManufacturingRequest.sent_date >= datetime.strptime(start, '%Y-%m-%d').date())
        if end:
            query = query.filter(ManufacturingRequest.sent_date <= datetime.strptime(end, '%Y-%m-%d').date())
        requests = query.order_by(ManufacturingRequest.sent_date.desc()).all()
        return [manufacturing_to_dict(r) for r in requests]
