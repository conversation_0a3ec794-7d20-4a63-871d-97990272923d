import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';
import Layout from './components/layout/Layout';
import Login from './pages/auth/Login';
import Dashboard from './pages/Dashboard';
import DiamondList from './pages/diamonds/DiamondList';
import VendorList from './pages/vendors/VendorList';
import ManufacturingList from './pages/manufacturing/ManufacturingList';
import ManufacturingHistory from './pages/manufacturing/ManufacturingHistory';
import JewelryList from './pages/jewelry/JewelryList';
import SalesList from './pages/sales/SalesList';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <ErrorBoundary>
              <Routes>
                <Route path="/login" element={<Login />} />
                <Route
                  path="/*"
                  element={
                    <ProtectedRoute>
                      <Layout />
                    </ProtectedRoute>
                  }
                >
                  <Route path="" element={<Dashboard />} />
                  <Route path="diamonds" element={<DiamondList />} />
                  <Route path="vendors" element={<VendorList />} />
                  <Route path="manufacturing" element={<ManufacturingList />} />
                  <Route path="manufacturing/history" element={<ManufacturingHistory />} />
                  <Route path="jewelry" element={<JewelryList />} />
                  <Route path="sales" element={<SalesList />} />
                </Route>
              </Routes>
            </ErrorBoundary>
          </div>
          <Toaster 
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />
        </Router>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;