import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, CheckCircle, Clock, Eye } from 'lucide-react';
import { api } from '../../lib/api';
import { ManufacturingRequest } from '../../types';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Select from '../../components/ui/Select';
import Modal from '../../components/ui/Modal';
import { LoadingState, ErrorState, EmptyState } from '../../components/DataStates';
import ManufacturingForm from './ManufacturingForm';
import ManufacturingDetails from './ManufacturingDetails';
import toast from 'react-hot-toast';

const ManufacturingList: React.FC = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<ManufacturingRequest | null>(null);
  const [statusFilter, setStatusFilter] = useState('');
  const [vendorFilter, setVendorFilter] = useState('');
  const [detailsLoading, setDetailsLoading] = useState(false);

  const queryClient = useQueryClient();

  const { data: requests, isLoading, error, refetch } = useQuery({
    queryKey: ['manufacturing', { status: statusFilter, vendor: vendorFilter }],
    queryFn: async (): Promise<ManufacturingRequest[]> => {
      const params: any = {};
      if (statusFilter) params.status = statusFilter;
      if (vendorFilter) params.vendor_id = vendorFilter;
      
      const response = await api.manufacturing.list(params);
      return response?.data || [];
    },
    retry: 1
  });

  // Enhanced error handling for vendors query
  const { data: vendors, error: vendorError, isLoading: isLoadingVendors } = useQuery({
    queryKey: ['vendors'],
    queryFn: async () => {
      try {
        const response = await api.vendors.list();
        return response?.data || [];
      } catch (err) {
        console.error('Error fetching vendors:', err);
        throw err;
      }
    }
  });

  // Add debugging for vendor data
  console.log('Vendor data:', vendors);

  // Improved error handling for vendor data
  React.useEffect(() => {
    if (!isLoadingVendors && !vendorError && Array.isArray(vendors) && vendors.length === 0) {
      console.error('No vendors found');
      toast.error('No vendors found');
    }
    if (vendorError) {
      console.error('Vendor query error:', vendorError);
      toast.error('Failed to fetch vendor data');
    }
  }, [vendors, vendorError, isLoadingVendors]);

  // Enhanced error handling for API calls
  const returnToStockMutation = useMutation({
    mutationFn: (id: number) => api.manufacturing.returnToStock(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['manufacturing'] });
      toast.success('Manufacturing request completed and items returned to stock.');
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to complete manufacturing request';
      toast.error(`Error: ${errorMessage}`);
    }
  });

  const handleReturnToStock = (request: ManufacturingRequest) => {
    if (window.confirm('Are you sure you want to mark this request as completed and return the diamonds to stock?')) {
      returnToStockMutation.mutate(request.id);
    }
  };

  const handleViewDetails = async (request: ManufacturingRequest) => {
    setDetailsLoading(true);
    try {
      // Fetch full details from API
      const response = await api.manufacturing.get(request.id);
      let enrichedRequest = response?.data || request;
      // Attach full vendor object if missing
      if (!enrichedRequest.vendor && enrichedRequest.vendor_id && Array.isArray(vendors)) {
        const found = vendors.find((v: any) => v && v.id && enrichedRequest.vendor_id && (v.id === enrichedRequest.vendor_id || v.id.toString() === enrichedRequest.vendor_id.toString()));
        if (found) enrichedRequest.vendor = found;
      }
      setSelectedRequest(enrichedRequest);
      setIsDetailsModalOpen(true);
    } catch (err) {
      toast.error('Failed to load request details');
    } finally {
      setDetailsLoading(false);
    }
  };

  const statusOptions = [
    { value: 'open', label: 'Open' },
    { value: 'completed', label: 'Completed' }
  ];

  const vendorOptions = Array.isArray(vendors) ? vendors.map((vendor: any) => ({
    value: vendor && vendor.id ? vendor.id.toString() : '',
    label: vendor && vendor.name ? vendor.name : 'Unknown Vendor'
  })).filter(option => option.value !== '') : [];

  // Helper to get vendor name by id
  const getVendorName = (vendorObj: any, vendorId: number | string) => {
    if (vendorObj && vendorObj.name) return vendorObj.name;
    if (Array.isArray(vendors) && vendorId) {
      const found = vendors.find((v: any) => v && v.id && vendorId && (v.id === vendorId || v.id.toString() === vendorId.toString()));
      return found ? found.name : 'Unknown Vendor';
    }
    return 'Unknown Vendor';
  };

  if (isLoading) {
    return <LoadingState message="Loading manufacturing requests..." />;
  }

  if (error) {
    return (
      <ErrorState 
        title="Failed to load manufacturing requests"
        onRetry={refetch}
      />
    );
  }

  if (!requests || requests.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Manufacturing Requests</h1>
            <p className="text-gray-600">Track diamonds sent for manufacturing</p>
          </div>
          <Button onClick={() => setIsAddModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Request
          </Button>
        </div>
        
        <EmptyState
          title="No manufacturing requests found"
          description="Start by creating your first manufacturing request"
          action={{
            label: "New Request",
            onClick: () => setIsAddModalOpen(true)
          }}
        />
        
        {/* Modals */}
        <Modal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          title="New Manufacturing Request"
        >
          <ManufacturingForm 
            onSuccess={() => {
              setIsAddModalOpen(false);
              queryClient.invalidateQueries({ queryKey: ['manufacturing'] });
            }} 
          />
        </Modal>
      </div>
    );
  }

  // Refactored table row rendering logic
  const renderTableRows = (requests: ManufacturingRequest[]) => {
    if (!Array.isArray(requests)) {
      return null;
    }
    return requests.map((request) => (
      <tr key={request.id} className="hover:bg-gray-50">
        <td className="px-6 py-4 whitespace-nowrap">
          <div>
            <div className="text-sm font-medium text-gray-900">
              Request #{request.id}
            </div>
            <div className="text-sm text-gray-500">
              {request.diamonds.length} diamond(s)
            </div>
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className="text-sm text-gray-900">
            {getVendorName(request.vendor, request.vendor_id)}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div>
            <div className="text-sm text-gray-900">
              Sent: {new Date(request.sent_date).toLocaleDateString()}
            </div>
            {request.return_date ? (
              <div className="text-sm text-green-600">
                Returned: {new Date(request.return_date).toLocaleDateString()}
              </div>
            ) : (
              <div className="text-sm text-gray-500">
                Expected: {new Date(request.expected_return_date).toLocaleDateString()}
              </div>
            )}
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${
            request.status === 'open' 
              ? 'bg-yellow-100 text-yellow-800' 
              : 'bg-green-100 text-green-800'
          }`}>
            {request.status === 'open' ? (
              <Clock className="h-3 w-3 mr-1" />
            ) : (
              <CheckCircle className="h-3 w-3 mr-1" />
            )}
            {request.status}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleViewDetails(request)}
            >
              <Eye className="h-4 w-4" />
            </Button>
            {request.status === 'open' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleReturnToStock(request)}
                isLoading={returnToStockMutation.isPending}
                title="Mark as Completed and Return to Stock"
              >
                <CheckCircle className="h-4 w-4 text-green-600" />
              </Button>
            )}
          </div>
        </td>
      </tr>
    ));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Manufacturing Requests</h1>
          <p className="text-gray-600">Track diamonds sent for manufacturing</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button onClick={() => setIsAddModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Request
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Select
            options={statusOptions}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            placeholder="Filter by status"
          />
          <Select
            options={vendorOptions}
            value={vendorFilter}
            onChange={(e) => setVendorFilter(e.target.value)}
            placeholder="Filter by vendor"
          />
          <Button
            variant="ghost"
            onClick={() => {
              setStatusFilter('');
              setVendorFilter('');
            }}
          >
            Clear Filters
          </Button>
        </div>
      </Card>

      {/* Manufacturing Requests List */}
      <Card padding={false}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Request Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Vendor
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Dates
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {renderTableRows(requests)}
            </tbody>
          </table>
          {requests?.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No manufacturing requests found.</p>
            </div>
          )}
        </div>
      </Card>

      {/* Modals */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Create Manufacturing Request"
      >
        <ManufacturingForm
          onSuccess={() => {
            setIsAddModalOpen(false);
            queryClient.invalidateQueries({ queryKey: ['manufacturing'] });
          }}
        />
      </Modal>

      <Modal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedRequest(null);
        }}
        title="Manufacturing Request Details"
        size="lg"
      >
        {detailsLoading ? (
          <div className="p-8 text-center text-gray-500">Loading details...</div>
        ) : selectedRequest && (
          <ManufacturingDetails request={selectedRequest} />
        )}
      </Modal>
    </div>
  );
};

export default ManufacturingList;