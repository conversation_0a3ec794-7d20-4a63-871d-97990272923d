#!/usr/bin/env python3
"""
Test security measures
"""

import requests

BASE_URL = "http://localhost:8000/api"

def test_security():
    print("🔒 Testing Security Measures...")
    
    # Test unauthorized access
    unauthorized_session = requests.Session()
    
    endpoints = ['diamonds', 'vendors', 'manufacturing', 'jewelry', 'sales']
    
    for endpoint in endpoints:
        response = unauthorized_session.get(f"{BASE_URL}/{endpoint}")
        if response.status_code == 401:
            print(f"  ✅ {endpoint} properly protected (401)")
        else:
            print(f"  ❌ {endpoint} not properly protected: {response.status_code}")
            print(f"      Response: {response.text[:100]}")

if __name__ == "__main__":
    test_security()
