#!/usr/bin/env python3
"""
Comprehensive test for Diamond page functionality
"""

import requests
import json
import time
from datetime import date

BASE_URL = "http://localhost:8000/api"
FRONTEND_URL = "http://localhost:5173"
LOGIN_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

class DiamondPageTester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.created_items = []
        
    def authenticate(self):
        """Authenticate with the system"""
        print("🔐 Authenticating...")
        try:
            response = self.session.post(f"{BASE_URL}/auth/login", json=LOGIN_CREDENTIALS)
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('access_token')
                if self.token:
                    self.session.headers.update({'Authorization': f'Bearer {self.token}'})
                    print("   ✅ Authentication successful")
                    return True
            print(f"   ❌ Authentication failed: {response.status_code}")
            return False
        except Exception as e:
            print(f"   ❌ Authentication error: {str(e)}")
            return False
    
    def test_frontend_access(self):
        """Test frontend accessibility"""
        print("\n🌐 Testing Frontend Access")
        print("-" * 30)
        
        try:
            response = requests.get(FRONTEND_URL, timeout=10)
            if response.status_code == 200:
                print("   ✅ Frontend accessible")
                return True
            else:
                print(f"   ❌ Frontend error: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ Frontend error: {str(e)}")
            return False
    
    def test_diamond_api_endpoints(self):
        """Test all diamond API endpoints"""
        print("\n💎 Testing Diamond API Endpoints")
        print("-" * 30)
        
        # Test GET /diamonds
        try:
            response = self.session.get(f"{BASE_URL}/diamonds")
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ GET /diamonds: {response.status_code}")
                print(f"      Found {len(data.get('data', []))} diamonds")
            else:
                print(f"   ❌ GET /diamonds: {response.status_code}")
                print(f"      Error: {response.text[:100]}")
        except Exception as e:
            print(f"   ❌ GET /diamonds error: {str(e)}")
        
        # Test GET /shapes
        try:
            response = self.session.get(f"{BASE_URL}/shapes")
            if response.status_code == 200:
                shapes = response.json()
                print(f"   ✅ GET /shapes: {response.status_code}")
                print(f"      Found {len(shapes)} shapes")
                return shapes
            else:
                print(f"   ❌ GET /shapes: {response.status_code}")
                return []
        except Exception as e:
            print(f"   ❌ GET /shapes error: {str(e)}")
            return []
    
    def test_create_diamond(self, shapes):
        """Test creating a diamond"""
        print("\n➕ Testing Diamond Creation")
        print("-" * 30)
        
        if not shapes:
            print("   ❌ No shapes available for testing")
            return None
        
        # Create test diamond
        diamond_data = {
            "shape_id": shapes[0]['id'],
            "carat": 1.5,
            "color": "D",
            "clarity": "VVS1",
            "cut_grade": "Excellent",
            "polish": "Excellent",
            "symmetry": "Excellent",
            "fluorescence": "None",
            "certificate_no": f"TEST{int(time.time())}",
            "certification_lab": "GIA",
            "cost_price": 50000,
            "retail_price": 75000,
            "quantity": 1,
            "status": "in_stock",
            "location": "Vault A",
            "notes": "Test diamond for functionality testing"
        }
        
        try:
            response = self.session.post(f"{BASE_URL}/diamonds", json=diamond_data)
            if response.status_code in [200, 201]:
                diamond = response.json()
                self.created_items.append(('diamond', diamond.get('id')))
                print(f"   ✅ Diamond created: ID {diamond.get('id')}")
                
                # Test profit margin calculation
                expected_margin = ((75000 - 50000) / 75000) * 100
                actual_margin = diamond.get('profit_margin', 0)
                if abs(expected_margin - actual_margin) < 0.01:
                    print(f"   ✅ Profit margin calculated correctly: {actual_margin:.2f}%")
                else:
                    print(f"   ❌ Profit margin incorrect: expected {expected_margin:.2f}%, got {actual_margin:.2f}%")
                
                return diamond
            else:
                print(f"   ❌ Diamond creation failed: {response.status_code}")
                print(f"      Error: {response.text[:200]}")
                return None
        except Exception as e:
            print(f"   ❌ Diamond creation error: {str(e)}")
            return None
    
    def test_diamond_operations(self, diamond):
        """Test diamond CRUD operations"""
        if not diamond:
            return
            
        diamond_id = diamond.get('id')
        print(f"\n🔧 Testing Diamond Operations (ID: {diamond_id})")
        print("-" * 30)
        
        # Test GET single diamond
        try:
            response = self.session.get(f"{BASE_URL}/diamonds/{diamond_id}")
            if response.status_code == 200:
                print(f"   ✅ GET diamond by ID: {response.status_code}")
            else:
                print(f"   ❌ GET diamond by ID: {response.status_code}")
        except Exception as e:
            print(f"   ❌ GET diamond by ID error: {str(e)}")
        
        # Test UPDATE diamond
        try:
            update_data = {
                "notes": "Updated test diamond",
                "cost_price": 55000,
                "retail_price": 80000
            }
            response = self.session.put(f"{BASE_URL}/diamonds/{diamond_id}", json=update_data)
            if response.status_code == 200:
                updated_diamond = response.json()
                print(f"   ✅ Diamond updated: {response.status_code}")
                
                # Check if profit margin was recalculated
                expected_margin = ((80000 - 55000) / 80000) * 100
                actual_margin = updated_diamond.get('profit_margin', 0)
                if abs(expected_margin - actual_margin) < 0.01:
                    print(f"   ✅ Profit margin recalculated: {actual_margin:.2f}%")
                else:
                    print(f"   ❌ Profit margin not recalculated correctly")
            else:
                print(f"   ❌ Diamond update failed: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Diamond update error: {str(e)}")
        
        # Test deduct stock
        try:
            deduct_data = {"quantity": 1}
            response = self.session.patch(f"{BASE_URL}/diamonds/{diamond_id}/deduct", json=deduct_data)
            if response.status_code == 200:
                print(f"   ✅ Stock deduction: {response.status_code}")
            else:
                print(f"   ❌ Stock deduction failed: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Stock deduction error: {str(e)}")
    
    def test_diamond_filtering(self):
        """Test diamond filtering and search"""
        print("\n🔍 Testing Diamond Filtering")
        print("-" * 30)
        
        # Test various filters
        filters = [
            {"color": "D"},
            {"clarity": "VVS1"},
            {"status": "in_stock"},
            {"search": "test"},
            {"min_carat": 1.0, "max_carat": 2.0},
            {"min_price": 40000, "max_price": 100000}
        ]
        
        for filter_params in filters:
            try:
                response = self.session.get(f"{BASE_URL}/diamonds", params=filter_params)
                if response.status_code == 200:
                    data = response.json()
                    filter_name = list(filter_params.keys())[0]
                    print(f"   ✅ Filter {filter_name}: Found {len(data.get('data', []))} diamonds")
                else:
                    print(f"   ❌ Filter {filter_params}: {response.status_code}")
            except Exception as e:
                print(f"   ❌ Filter {filter_params} error: {str(e)}")
    
    def cleanup(self):
        """Clean up created test data"""
        print("\n🧹 Cleaning up test data...")
        
        for item_type, item_id in self.created_items:
            try:
                if item_type == 'diamond':
                    response = self.session.delete(f"{BASE_URL}/diamonds/{item_id}")
                    if response.status_code in [200, 204]:
                        print(f"   ✅ Cleaned up diamond {item_id}")
                    else:
                        print(f"   ⚠️  Could not clean up diamond {item_id}: {response.status_code}")
            except Exception as e:
                print(f"   ❌ Cleanup error for {item_type} {item_id}: {str(e)}")
    
    def run_comprehensive_test(self):
        """Run all diamond page tests"""
        print("💎 COMPREHENSIVE DIAMOND PAGE TESTING")
        print("=" * 50)
        
        # Test authentication
        if not self.authenticate():
            return False
        
        # Test frontend access
        frontend_ok = self.test_frontend_access()
        
        # Test API endpoints
        shapes = self.test_diamond_api_endpoints()
        
        # Test diamond creation
        diamond = self.test_create_diamond(shapes)
        
        # Test diamond operations
        self.test_diamond_operations(diamond)
        
        # Test filtering
        self.test_diamond_filtering()
        
        # Cleanup
        self.cleanup()
        
        print("\n🎯 Diamond Page Testing Complete!")
        return True

if __name__ == "__main__":
    tester = DiamondPageTester()
    success = tester.run_comprehensive_test()
    
    if success:
        print("✅ Diamond page functionality is working correctly!")
    else:
        print("❌ Diamond page has issues that need attention.")
