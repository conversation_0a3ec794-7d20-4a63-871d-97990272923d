import requests
from tests.conftest import get_tokens

def test_manufacturing_crud(base_url, test_user):
    access_token, _ = get_tokens(base_url, test_user)
    headers = {"Authorization": f"Bearer {access_token}"}
    # Create a vendor
    vendor = {
        "name": "Mfg Vendor",
        "gst_number": "MFG123456",
        "contact_number": "1112223333",
        "address": "789 Mfg Road"
    }
    r = requests.post(f"{base_url}/vendors/", json=vendor, headers=headers, timeout=10)
    vendor_id = r.json()["id"]
    # Create manufacturing request
    mfg = {
        "vendor_id": vendor_id,
        "sent_date": "2025-06-21",
        "expected_return_date": "2025-07-01",
        "diamonds": []
    }
    r = requests.post(f"{base_url}/manufacturing/", json=mfg, headers=headers, timeout=10)
    assert r.status_code == 201
    mfg_id = r.json()["id"]
    # Get
    r = requests.get(f"{base_url}/manufacturing/{mfg_id}", headers=headers, timeout=10)
    assert r.status_code == 200
    # Cleanup
    requests.delete(f"{base_url}/vendors/{vendor_id}", headers=headers, timeout=10)
