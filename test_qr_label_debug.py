#!/usr/bin/env python3
"""
Debug QR code and label generation authentication issues
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_qr_label_auth():
    print("🔍 Testing QR Code and Label Generation Authentication")
    print("=" * 60)
    
    # Step 1: Login
    print("\n1. Logging in...")
    login_response = requests.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        return
    
    token = login_response.json().get('access_token')
    print(f"✅ Login successful, token: {token[:20]}...")
    
    # Step 2: Get diamonds to find a test diamond
    print("\n2. Getting diamonds...")
    headers = {'Authorization': f'Bearer {token}'}
    diamonds_response = requests.get(f"{BASE_URL}/diamonds", headers=headers)
    
    if diamonds_response.status_code != 200:
        print(f"❌ Failed to get diamonds: {diamonds_response.status_code}")
        return
    
    diamonds_data = diamonds_response.json()
    diamonds = diamonds_data.get('data', [])
    
    if not diamonds:
        print("❌ No diamonds found")
        return
    
    diamond_id = diamonds[0]['id']
    print(f"✅ Found diamond ID: {diamond_id}")
    
    # Step 3: Test QR code generation
    print(f"\n3. Testing QR code generation for diamond {diamond_id}...")
    qr_url = f"{BASE_URL}/diamonds/{diamond_id}/qr-code"
    print(f"   URL: {qr_url}")
    print(f"   Headers: {headers}")
    
    qr_response = requests.get(qr_url, headers=headers)
    print(f"   Status: {qr_response.status_code}")
    print(f"   Response: {qr_response.text[:300]}...")
    
    # Step 4: Test label generation
    print(f"\n4. Testing label generation for diamond {diamond_id}...")
    label_url = f"{BASE_URL}/diamonds/{diamond_id}/label"
    print(f"   URL: {label_url}")
    
    label_response = requests.get(label_url, headers=headers)
    print(f"   Status: {label_response.status_code}")
    print(f"   Response: {label_response.text[:300]}...")
    
    # Step 5: Test other endpoints for comparison
    print(f"\n5. Testing other endpoints for comparison...")
    
    # Test diamond detail (should work)
    detail_response = requests.get(f"{BASE_URL}/diamonds/{diamond_id}", headers=headers)
    print(f"   Diamond detail status: {detail_response.status_code}")
    
    # Test diamond list (should work)
    list_response = requests.get(f"{BASE_URL}/diamonds", headers=headers)
    print(f"   Diamond list status: {list_response.status_code}")

if __name__ == "__main__":
    test_qr_label_auth()
