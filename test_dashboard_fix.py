#!/usr/bin/env python3
"""
Quick test script to verify dashboard endpoints are working
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:8000/api"
TEST_USER = {
    "email": "<EMAIL>",  # Update with your admin email
    "password": "admin123"
}

def test_dashboard_endpoints():
    """Test all dashboard endpoints"""
    session = requests.Session()
    
    # Login to get token
    print("🔐 Logging in...")
    try:
        login_response = session.post(f"{BASE_URL}/auth/login", json=TEST_USER)
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return False
        
        token_data = login_response.json()
        token = token_data.get('access_token')
        if not token:
            print("❌ No access token received")
            return False
        
        session.headers.update({'Authorization': f'Bearer {token}'})
        print("✅ Login successful")
        
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Test dashboard endpoints
    endpoints = [
        ('/dashboard/summary', 'Dashboard Summary'),
        ('/dashboard/sales', 'Dashboard Sales'),
        ('/dashboard/stock', 'Dashboard Stock'),
        ('/dashboard/activity', 'Dashboard Activity'),
        ('/dashboard/analytics', 'Dashboard Analytics')
    ]
    
    all_success = True
    
    for endpoint, name in endpoints:
        try:
            print(f"🧪 Testing {name}...")
            response = session.get(f"{BASE_URL}{endpoint}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {name}: Success")
                print(f"   Data keys: {list(data.keys()) if isinstance(data, dict) else 'List with ' + str(len(data)) + ' items'}")
            else:
                print(f"❌ {name}: Failed with status {response.status_code}")
                print(f"   Response: {response.text}")
                all_success = False
                
        except Exception as e:
            print(f"❌ {name}: Exception - {e}")
            all_success = False
    
    return all_success

if __name__ == "__main__":
    print("🚀 Testing Dashboard Endpoints...")
    print("=" * 40)
    
    success = test_dashboard_endpoints()
    
    if success:
        print("\n🎉 All dashboard endpoints are working correctly!")
        print("The frontend should now be able to load the dashboard.")
    else:
        print("\n⚠️ Some dashboard endpoints failed.")
        print("Please check the backend logs for more details.")
