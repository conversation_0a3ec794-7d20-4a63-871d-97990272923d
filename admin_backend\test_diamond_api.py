#!/usr/bin/env python3
"""
Comprehensive test script for Diamond Management API
Tests all diamond-related endpoints and functionality
"""

import requests
import json
import sys
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000/api"
TEST_USER = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

class DiamondAPITester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.test_diamond_id = None
        self.test_results = []
        
    def log_test(self, test_name, success, message="", response_data=None):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "response_data": response_data
        })
    
    def authenticate(self):
        """Authenticate and get JWT token"""
        try:
            response = self.session.post(f"{BASE_URL}/auth/login", json=TEST_USER)
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('access_token')
                self.session.headers.update({'Authorization': f'Bearer {self.token}'})
                self.log_test("Authentication", True, "Successfully authenticated")
                return True
            else:
                self.log_test("Authentication", False, f"Failed with status {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Authentication", False, f"Exception: {str(e)}")
            return False
    
    def test_diamond_crud(self):
        """Test Diamond CRUD operations"""
        # Test CREATE
        diamond_data = {
            "shape_id": 1,
            "carat": 1.25,
            "color": "D",
            "clarity": "VVS1",
            "cut_grade": "Excellent",
            "polish": "Excellent",
            "symmetry": "Excellent",
            "fluorescence_intensity": "None",
            "certificate_no": f"TEST{int(time.time())}",
            "certification_lab": "GIA",
            "quantity": 5,
            "minimum_stock": 2,
            "cost_price": 5000.00,
            "retail_price": 7500.00,
            "vendor_id": 1,
            "status": "in_stock",
            "location": "Vault A1",
            "notes": "Test diamond for API testing"
        }
        
        try:
            response = self.session.post(f"{BASE_URL}/diamonds", json=diamond_data)
            if response.status_code == 201:
                data = response.json()
                self.test_diamond_id = data.get('id')
                self.log_test("Diamond CREATE", True, f"Created diamond with ID {self.test_diamond_id}")
            else:
                self.log_test("Diamond CREATE", False, f"Failed with status {response.status_code}: {response.text}")
                return False
        except Exception as e:
            self.log_test("Diamond CREATE", False, f"Exception: {str(e)}")
            return False
        
        # Test READ (single)
        try:
            response = self.session.get(f"{BASE_URL}/diamonds/{self.test_diamond_id}")
            if response.status_code == 200:
                data = response.json()
                if data.get('id') == self.test_diamond_id:
                    self.log_test("Diamond READ (single)", True, "Successfully retrieved diamond")
                else:
                    self.log_test("Diamond READ (single)", False, "Retrieved diamond ID mismatch")
            else:
                self.log_test("Diamond READ (single)", False, f"Failed with status {response.status_code}")
        except Exception as e:
            self.log_test("Diamond READ (single)", False, f"Exception: {str(e)}")
        
        # Test READ (list)
        try:
            response = self.session.get(f"{BASE_URL}/diamonds")
            if response.status_code == 200:
                data = response.json()
                if isinstance(data.get('data'), list):
                    self.log_test("Diamond READ (list)", True, f"Retrieved {len(data['data'])} diamonds")
                else:
                    self.log_test("Diamond READ (list)", False, "Response data is not a list")
            else:
                self.log_test("Diamond READ (list)", False, f"Failed with status {response.status_code}")
        except Exception as e:
            self.log_test("Diamond READ (list)", False, f"Exception: {str(e)}")
        
        # Test UPDATE
        update_data = {
            "retail_price": 8000.00,
            "notes": "Updated test diamond"
        }
        
        try:
            response = self.session.put(f"{BASE_URL}/diamonds/{self.test_diamond_id}", json=update_data)
            if response.status_code == 200:
                data = response.json()
                if data.get('retail_price') == 8000.00:
                    self.log_test("Diamond UPDATE", True, "Successfully updated diamond")
                else:
                    self.log_test("Diamond UPDATE", False, "Update not reflected in response")
            else:
                self.log_test("Diamond UPDATE", False, f"Failed with status {response.status_code}")
        except Exception as e:
            self.log_test("Diamond UPDATE", False, f"Exception: {str(e)}")
        
        return True
    
    def test_diamond_search_and_filters(self):
        """Test diamond search and filtering"""
        test_cases = [
            {"search": "TEST", "description": "Search by certificate number"},
            {"color": "D", "description": "Filter by color"},
            {"clarity": "VVS1", "description": "Filter by clarity"},
            {"status": "in_stock", "description": "Filter by status"},
            {"min_carat": "1.0", "max_carat": "2.0", "description": "Filter by carat range"},
            {"min_price": "5000", "max_price": "10000", "description": "Filter by price range"}
        ]
        
        for test_case in test_cases:
            try:
                response = self.session.get(f"{BASE_URL}/diamonds", params=test_case)
                if response.status_code == 200:
                    data = response.json()
                    self.log_test(f"Diamond Filter: {test_case['description']}", True, 
                                f"Retrieved {len(data.get('data', []))} results")
                else:
                    self.log_test(f"Diamond Filter: {test_case['description']}", False, 
                                f"Failed with status {response.status_code}")
            except Exception as e:
                self.log_test(f"Diamond Filter: {test_case['description']}", False, f"Exception: {str(e)}")
    
    def test_bulk_operations(self):
        """Test bulk operations"""
        if not self.test_diamond_id:
            self.log_test("Bulk Operations", False, "No test diamond available")
            return
        
        # Test bulk update
        bulk_update_data = {
            "diamond_ids": [self.test_diamond_id],
            "updates": {
                "location": "Vault B2",
                "notes": "Bulk updated diamond"
            }
        }
        
        try:
            response = self.session.put(f"{BASE_URL}/diamonds/bulk", json=bulk_update_data)
            if response.status_code == 200:
                data = response.json()
                self.log_test("Bulk UPDATE", True, f"Updated {data.get('updated_count', 0)} diamonds")
            else:
                self.log_test("Bulk UPDATE", False, f"Failed with status {response.status_code}")
        except Exception as e:
            self.log_test("Bulk UPDATE", False, f"Exception: {str(e)}")
    
    def test_barcode_generation(self):
        """Test barcode and QR code generation"""
        if not self.test_diamond_id:
            self.log_test("Barcode Generation", False, "No test diamond available")
            return
        
        # Test QR code generation
        try:
            response = self.session.get(f"{BASE_URL}/diamonds/{self.test_diamond_id}/qr-code")
            if response.status_code == 200:
                data = response.json()
                if data.get('qr_code') and data['qr_code'].startswith('data:image/png;base64,'):
                    self.log_test("QR Code Generation", True, "Successfully generated QR code")
                else:
                    self.log_test("QR Code Generation", False, "Invalid QR code format")
            else:
                self.log_test("QR Code Generation", False, f"Failed with status {response.status_code}")
        except Exception as e:
            self.log_test("QR Code Generation", False, f"Exception: {str(e)}")
        
        # Test label generation
        try:
            response = self.session.get(f"{BASE_URL}/diamonds/{self.test_diamond_id}/label")
            if response.status_code == 200:
                data = response.json()
                if data.get('label') and data['label'].startswith('data:image/png;base64,'):
                    self.log_test("Label Generation", True, "Successfully generated label")
                else:
                    self.log_test("Label Generation", False, "Invalid label format")
            else:
                self.log_test("Label Generation", False, f"Failed with status {response.status_code}")
        except Exception as e:
            self.log_test("Label Generation", False, f"Exception: {str(e)}")
    
    def test_csv_export(self):
        """Test CSV export functionality"""
        try:
            response = self.session.get(f"{BASE_URL}/diamonds/export/csv")
            if response.status_code == 200:
                if response.headers.get('content-type') == 'text/csv':
                    self.log_test("CSV Export", True, f"Successfully exported CSV ({len(response.content)} bytes)")
                else:
                    self.log_test("CSV Export", False, "Invalid content type")
            else:
                self.log_test("CSV Export", False, f"Failed with status {response.status_code}")
        except Exception as e:
            self.log_test("CSV Export", False, f"Exception: {str(e)}")
    
    def test_dashboard_analytics(self):
        """Test dashboard and analytics endpoints"""
        endpoints = [
            ("/dashboard/summary", "Dashboard Summary"),
            ("/dashboard/analytics", "Dashboard Analytics"),
            ("/dashboard/activity", "Dashboard Activity")
        ]
        
        for endpoint, name in endpoints:
            try:
                response = self.session.get(f"{BASE_URL}{endpoint}")
                if response.status_code == 200:
                    data = response.json()
                    self.log_test(name, True, "Successfully retrieved data")
                else:
                    self.log_test(name, False, f"Failed with status {response.status_code}")
            except Exception as e:
                self.log_test(name, False, f"Exception: {str(e)}")
    
    def cleanup(self):
        """Clean up test data"""
        if self.test_diamond_id:
            try:
                response = self.session.delete(f"{BASE_URL}/diamonds/{self.test_diamond_id}")
                if response.status_code == 200:
                    self.log_test("Cleanup", True, "Successfully deleted test diamond")
                else:
                    self.log_test("Cleanup", False, f"Failed to delete test diamond: {response.status_code}")
            except Exception as e:
                self.log_test("Cleanup", False, f"Exception during cleanup: {str(e)}")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Diamond API Tests...")
        print("=" * 50)
        
        if not self.authenticate():
            print("❌ Authentication failed. Cannot proceed with tests.")
            return False
        
        # Run all test suites
        self.test_diamond_crud()
        self.test_diamond_search_and_filters()
        self.test_bulk_operations()
        self.test_barcode_generation()
        self.test_csv_export()
        self.test_dashboard_analytics()
        
        # Cleanup
        self.cleanup()
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['test']}: {result['message']}")
        
        return failed_tests == 0

if __name__ == "__main__":
    tester = DiamondAPITester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
