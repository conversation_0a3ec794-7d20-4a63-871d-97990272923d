#!/usr/bin/env python3
"""
Test the diamond images API functionality
"""

import requests
import json
import os
import tempfile
from PIL import Image

BASE_URL = "http://localhost:8000/api"

def create_test_image():
    """Create a simple test image"""
    # Create a simple 100x100 red image
    img = Image.new('RGB', (100, 100), color='red')
    
    # Save to temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
    img.save(temp_file.name, 'PNG')
    temp_file.close()
    
    return temp_file.name

def test_diamond_images_api():
    print("🖼️  Testing Diamond Images API")
    print("=" * 50)
    
    # Step 1: Login
    print("\n1. 🔐 Authentication")
    login_response = requests.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "Shivam@109"
    })
    
    if login_response.status_code != 200:
        print(f"   ❌ Login failed: {login_response.status_code}")
        return False
    
    token = login_response.json().get('access_token')
    headers = {'Authorization': f'Bearer {token}'}
    print("   ✅ Authentication successful")
    
    # Step 2: Get a test diamond
    print("\n2. 💎 Getting test diamond")
    diamonds_response = requests.get(f"{BASE_URL}/diamonds", headers=headers)
    if diamonds_response.status_code != 200:
        print(f"   ❌ Failed to get diamonds: {diamonds_response.status_code}")
        return False
    
    diamonds_data = diamonds_response.json()
    diamonds = diamonds_data.get('data', [])
    if not diamonds:
        print("   ❌ No diamonds found")
        return False
    
    diamond_id = diamonds[0]['id']
    print(f"   ✅ Using diamond ID: {diamond_id}")
    
    # Step 3: Test get images (should be empty initially)
    print("\n3. 📋 Getting existing images")
    images_response = requests.get(f"{BASE_URL}/diamond-images/diamonds/{diamond_id}/images", headers=headers)
    print(f"   Status: {images_response.status_code}")
    
    if images_response.status_code == 200:
        images = images_response.json()
        print(f"   ✅ Found {len(images)} existing images")
    else:
        print(f"   ❌ Failed to get images: {images_response.text}")
        return False
    
    # Step 4: Test image upload
    print("\n4. ⬆️  Testing image upload")
    test_image_path = create_test_image()
    
    try:
        with open(test_image_path, 'rb') as f:
            files = {'file': ('test_image.png', f, 'image/png')}
            data = {
                'image_type': 'main',
                'is_primary': 'true',
                'alt_text': 'Test diamond image'
            }
            
            upload_response = requests.post(
                f"{BASE_URL}/diamond-images/diamonds/{diamond_id}/images",
                files=files,
                data=data,
                headers=headers
            )
            
        print(f"   Status: {upload_response.status_code}")
        
        if upload_response.status_code == 201:
            uploaded_image = upload_response.json()
            print(f"   ✅ Image uploaded successfully!")
            print(f"   📋 Image ID: {uploaded_image.get('id')}")
            print(f"   📋 Image URL: {uploaded_image.get('image_url')}")
            
            # Step 5: Verify image was uploaded
            print("\n5. ✅ Verifying upload")
            verify_response = requests.get(f"{BASE_URL}/diamond-images/diamonds/{diamond_id}/images", headers=headers)
            if verify_response.status_code == 200:
                updated_images = verify_response.json()
                print(f"   ✅ Now have {len(updated_images)} images")
                
                if len(updated_images) > len(images):
                    print("   ✅ Upload verification successful!")
                    
                    # Step 6: Test image deletion
                    print("\n6. 🗑️  Testing image deletion")
                    image_id = uploaded_image.get('id')
                    delete_response = requests.delete(
                        f"{BASE_URL}/diamond-images/diamonds/{diamond_id}/images/{image_id}",
                        headers=headers
                    )
                    print(f"   Status: {delete_response.status_code}")
                    
                    if delete_response.status_code in [200, 204]:
                        print("   ✅ Image deleted successfully!")
                        
                        # Verify deletion
                        final_response = requests.get(f"{BASE_URL}/diamond-images/diamonds/{diamond_id}/images", headers=headers)
                        if final_response.status_code == 200:
                            final_images = final_response.json()
                            if len(final_images) == len(images):
                                print("   ✅ Deletion verification successful!")
                            else:
                                print(f"   ⚠️  Expected {len(images)} images, found {len(final_images)}")
                    else:
                        print(f"   ❌ Image deletion failed: {delete_response.text}")
                else:
                    print("   ❌ Upload verification failed - image count didn't increase")
            else:
                print(f"   ❌ Verification failed: {verify_response.text}")
        else:
            print(f"   ❌ Image upload failed: {upload_response.text}")
            return False
            
    finally:
        # Clean up test image
        if os.path.exists(test_image_path):
            os.unlink(test_image_path)
    
    print("\n" + "=" * 50)
    print("🎉 DIAMOND IMAGES API TEST COMPLETE!")
    print("=" * 50)
    print("\n📝 Summary:")
    print("   ✅ Authentication working")
    print("   ✅ Get images endpoint working")
    print("   ✅ Image upload working")
    print("   ✅ Image deletion working")
    print("   ✅ Frontend should now work correctly!")
    
    return True

if __name__ == "__main__":
    success = test_diamond_images_api()
    if success:
        print("\n🚀 Diamond image upload is ready!")
    else:
        print("\n❌ Some issues found - check the logs above")
