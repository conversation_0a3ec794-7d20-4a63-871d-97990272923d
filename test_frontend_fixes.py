#!/usr/bin/env python3
"""
Test frontend fixes by checking API responses
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_frontend_fixes():
    print("🔧 Testing Frontend Fixes...")
    
    # Login first
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "Shiva<PERSON>@109"
    })
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Test dashboard endpoints
    print("\n📊 Testing Dashboard APIs...")
    dashboard_endpoints = [
        ('/dashboard', 'Main Dashboard'),
        ('/dashboard/summary', 'Dashboard Summary'),
        ('/dashboard/sales', 'Sales Stats'),
        ('/dashboard/stock', 'Stock Levels')
    ]
    
    for endpoint, name in dashboard_endpoints:
        response = session.get(f"{BASE_URL}{endpoint}")
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ {name}: {type(data)} with {len(data) if isinstance(data, (list, dict)) else 'N/A'} items")
        else:
            print(f"  ❌ {name}: {response.status_code}")
    
    # Test jewelry API structure
    print("\n💍 Testing Jewelry API...")
    response = session.get(f"{BASE_URL}/jewelry")
    if response.status_code == 200:
        data = response.json()
        print(f"  ✅ Jewelry API: {type(data)}")
        if isinstance(data, dict) and 'data' in data:
            print(f"    - Has 'data' property: {len(data['data'])} items")
            print(f"    - Pagination info: page={data.get('page')}, total={data.get('total')}")
        else:
            print(f"    - Direct response: {len(data) if isinstance(data, list) else 'Not a list'}")
    else:
        print(f"  ❌ Jewelry API: {response.status_code}")
    
    # Test jewelry with filters
    print("\n🔍 Testing Jewelry Filters...")
    response = session.get(f"{BASE_URL}/jewelry", params={'status': 'in_stock'})
    if response.status_code == 200:
        data = response.json()
        in_stock_count = len(data.get('data', [])) if isinstance(data, dict) else len(data) if isinstance(data, list) else 0
        print(f"  ✅ In-stock jewelry: {in_stock_count} items")
    else:
        print(f"  ❌ Jewelry filter: {response.status_code}")
    
    # Test diamonds API
    print("\n💎 Testing Diamonds API...")
    response = session.get(f"{BASE_URL}/diamonds")
    if response.status_code == 200:
        data = response.json()
        print(f"  ✅ Diamonds API: {type(data)}")
        if isinstance(data, dict) and 'data' in data:
            print(f"    - Has 'data' property: {len(data['data'])} items")
        else:
            print(f"    - Direct response: {len(data) if isinstance(data, list) else 'Not a list'}")
    else:
        print(f"  ❌ Diamonds API: {response.status_code}")
    
    print("\n🎯 Frontend API compatibility test complete!")

if __name__ == "__main__":
    test_frontend_fixes()
