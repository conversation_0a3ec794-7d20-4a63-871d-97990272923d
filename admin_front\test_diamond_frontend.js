#!/usr/bin/env node
/**
 * Comprehensive Frontend Test Script for Diamond Management
 * Tests UI functionality, API integration, and user workflows
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class DiamondFrontendTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.testResults = [];
        this.baseUrl = 'http://localhost:5173';
        this.testData = {
            username: 'admin',
            password: 'admin123'
        };
    }

    async logTest(testName, success, message = '', screenshot = false) {
        const status = success ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} ${testName}: ${message}`);
        
        this.testResults.push({
            test: testName,
            success,
            message,
            timestamp: new Date().toISOString()
        });

        if (screenshot && this.page) {
            try {
                const screenshotPath = `test-screenshots/${testName.replace(/\s+/g, '_')}.png`;
                await this.page.screenshot({ path: screenshotPath, fullPage: true });
                console.log(`  📸 Screenshot saved: ${screenshotPath}`);
            } catch (error) {
                console.log(`  ⚠️ Screenshot failed: ${error.message}`);
            }
        }
    }

    async setup() {
        try {
            // Create screenshots directory
            if (!fs.existsSync('test-screenshots')) {
                fs.mkdirSync('test-screenshots');
            }

            this.browser = await puppeteer.launch({
                headless: false, // Set to true for CI/CD
                defaultViewport: { width: 1280, height: 720 },
                args: ['--no-sandbox', '--disable-setuid-sandbox']
            });

            this.page = await this.browser.newPage();
            
            // Set up console logging
            this.page.on('console', msg => {
                if (msg.type() === 'error') {
                    console.log(`🔴 Console Error: ${msg.text()}`);
                }
            });

            // Set up error handling
            this.page.on('pageerror', error => {
                console.log(`🔴 Page Error: ${error.message}`);
            });

            await this.logTest('Setup', true, 'Browser launched successfully');
            return true;
        } catch (error) {
            await this.logTest('Setup', false, `Failed to launch browser: ${error.message}`);
            return false;
        }
    }

    async testLogin() {
        try {
            await this.page.goto(`${this.baseUrl}/login`);
            await this.page.waitForSelector('input[type="text"]', { timeout: 5000 });

            // Fill login form
            await this.page.type('input[type="text"]', this.testData.username);
            await this.page.type('input[type="password"]', this.testData.password);
            
            // Click login button
            await this.page.click('button[type="submit"]');
            
            // Wait for redirect to dashboard
            await this.page.waitForNavigation({ timeout: 10000 });
            
            const currentUrl = this.page.url();
            if (currentUrl.includes('/dashboard') || currentUrl.includes('/diamonds')) {
                await this.logTest('Login', true, 'Successfully logged in and redirected');
                return true;
            } else {
                await this.logTest('Login', false, `Unexpected redirect to: ${currentUrl}`, true);
                return false;
            }
        } catch (error) {
            await this.logTest('Login', false, `Login failed: ${error.message}`, true);
            return false;
        }
    }

    async testDashboardLoad() {
        try {
            await this.page.goto(`${this.baseUrl}/dashboard`);
            await this.page.waitForSelector('h1, h2', { timeout: 10000 });

            // Check for key dashboard elements
            const elements = await Promise.all([
                this.page.$('h1, h2'), // Dashboard title
                this.page.$('[class*="card"], [class*="Card"]'), // Dashboard cards
            ]);

            if (elements.every(el => el !== null)) {
                await this.logTest('Dashboard Load', true, 'Dashboard loaded with all key elements');
                return true;
            } else {
                await this.logTest('Dashboard Load', false, 'Missing dashboard elements', true);
                return false;
            }
        } catch (error) {
            await this.logTest('Dashboard Load', false, `Dashboard load failed: ${error.message}`, true);
            return false;
        }
    }

    async testDiamondsPageLoad() {
        try {
            await this.page.goto(`${this.baseUrl}/diamonds`);
            await this.page.waitForSelector('table, [class*="grid"]', { timeout: 15000 });

            // Check for diamonds page elements
            const elements = await Promise.all([
                this.page.$('table, [class*="grid"]'), // Diamond table or grid
                this.page.$('button[class*="add"], button:has-text("Add")'), // Add button
                this.page.$('input[type="search"], input[placeholder*="search"]'), // Search input
            ]);

            const loadedElements = elements.filter(el => el !== null).length;
            if (loadedElements >= 2) {
                await this.logTest('Diamonds Page Load', true, `Loaded with ${loadedElements}/3 key elements`);
                return true;
            } else {
                await this.logTest('Diamonds Page Load', false, `Only ${loadedElements}/3 elements found`, true);
                return false;
            }
        } catch (error) {
            await this.logTest('Diamonds Page Load', false, `Page load failed: ${error.message}`, true);
            return false;
        }
    }

    async testSearchFunctionality() {
        try {
            // Ensure we're on diamonds page
            await this.page.goto(`${this.baseUrl}/diamonds`);
            await this.page.waitForSelector('input[type="search"], input[placeholder*="search"]', { timeout: 10000 });

            const searchInput = await this.page.$('input[type="search"], input[placeholder*="search"]');
            if (!searchInput) {
                await this.logTest('Search Functionality', false, 'Search input not found');
                return false;
            }

            // Test search
            await searchInput.click();
            await searchInput.type('diamond');
            
            // Wait for search results (debounced)
            await this.page.waitForTimeout(1000);

            await this.logTest('Search Functionality', true, 'Search input works');
            return true;
        } catch (error) {
            await this.logTest('Search Functionality', false, `Search test failed: ${error.message}`, true);
            return false;
        }
    }

    async testFilterFunctionality() {
        try {
            await this.page.goto(`${this.baseUrl}/diamonds`);
            await this.page.waitForSelector('select, [role="combobox"]', { timeout: 10000 });

            // Try to find and interact with filter dropdowns
            const filters = await this.page.$$('select, [role="combobox"]');
            
            if (filters.length > 0) {
                // Test first filter
                await filters[0].click();
                await this.page.waitForTimeout(500);
                
                await this.logTest('Filter Functionality', true, `Found ${filters.length} filter controls`);
                return true;
            } else {
                await this.logTest('Filter Functionality', false, 'No filter controls found', true);
                return false;
            }
        } catch (error) {
            await this.logTest('Filter Functionality', false, `Filter test failed: ${error.message}`, true);
            return false;
        }
    }

    async testAddDiamondModal() {
        try {
            await this.page.goto(`${this.baseUrl}/diamonds`);
            
            // Look for Add button
            const addButton = await this.page.$('button:has-text("Add"), button[class*="add"]');
            if (!addButton) {
                // Try alternative selectors
                const buttons = await this.page.$$('button');
                let foundAddButton = false;
                
                for (const button of buttons) {
                    const text = await button.evaluate(el => el.textContent);
                    if (text && text.toLowerCase().includes('add')) {
                        await button.click();
                        foundAddButton = true;
                        break;
                    }
                }
                
                if (!foundAddButton) {
                    await this.logTest('Add Diamond Modal', false, 'Add button not found', true);
                    return false;
                }
            } else {
                await addButton.click();
            }

            // Wait for modal to appear
            await this.page.waitForSelector('[role="dialog"], .modal, [class*="modal"]', { timeout: 5000 });
            
            const modal = await this.page.$('[role="dialog"], .modal, [class*="modal"]');
            if (modal) {
                await this.logTest('Add Diamond Modal', true, 'Modal opened successfully');
                
                // Close modal
                const closeButton = await this.page.$('button:has-text("Cancel"), button:has-text("Close"), [aria-label="close"]');
                if (closeButton) {
                    await closeButton.click();
                }
                
                return true;
            } else {
                await this.logTest('Add Diamond Modal', false, 'Modal did not appear', true);
                return false;
            }
        } catch (error) {
            await this.logTest('Add Diamond Modal', false, `Modal test failed: ${error.message}`, true);
            return false;
        }
    }

    async testResponsiveDesign() {
        try {
            await this.page.goto(`${this.baseUrl}/diamonds`);
            
            // Test mobile viewport
            await this.page.setViewport({ width: 375, height: 667 });
            await this.page.waitForTimeout(1000);
            
            // Check if page is still functional
            const mobileElements = await this.page.$('table, [class*="grid"], [class*="card"]');
            
            // Test tablet viewport
            await this.page.setViewport({ width: 768, height: 1024 });
            await this.page.waitForTimeout(1000);
            
            const tabletElements = await this.page.$('table, [class*="grid"], [class*="card"]');
            
            // Reset to desktop
            await this.page.setViewport({ width: 1280, height: 720 });
            
            if (mobileElements && tabletElements) {
                await this.logTest('Responsive Design', true, 'Page adapts to different screen sizes');
                return true;
            } else {
                await this.logTest('Responsive Design', false, 'Page not responsive', true);
                return false;
            }
        } catch (error) {
            await this.logTest('Responsive Design', false, `Responsive test failed: ${error.message}`, true);
            return false;
        }
    }

    async testPerformance() {
        try {
            const startTime = Date.now();
            
            await this.page.goto(`${this.baseUrl}/diamonds`);
            await this.page.waitForSelector('table, [class*="grid"]', { timeout: 15000 });
            
            const loadTime = Date.now() - startTime;
            
            if (loadTime < 5000) {
                await this.logTest('Performance', true, `Page loaded in ${loadTime}ms`);
                return true;
            } else {
                await this.logTest('Performance', false, `Slow load time: ${loadTime}ms`, true);
                return false;
            }
        } catch (error) {
            await this.logTest('Performance', false, `Performance test failed: ${error.message}`, true);
            return false;
        }
    }

    async cleanup() {
        try {
            if (this.browser) {
                await this.browser.close();
                await this.logTest('Cleanup', true, 'Browser closed successfully');
            }
        } catch (error) {
            await this.logTest('Cleanup', false, `Cleanup failed: ${error.message}`);
        }
    }

    async runAllTests() {
        console.log('🚀 Starting Diamond Frontend Tests...');
        console.log('=' .repeat(50));

        if (!(await this.setup())) {
            console.log('❌ Setup failed. Cannot proceed with tests.');
            return false;
        }

        // Run all test suites
        await this.testLogin();
        await this.testDashboardLoad();
        await this.testDiamondsPageLoad();
        await this.testSearchFunctionality();
        await this.testFilterFunctionality();
        await this.testAddDiamondModal();
        await this.testResponsiveDesign();
        await this.testPerformance();

        // Cleanup
        await this.cleanup();

        // Print summary
        console.log('\n' + '='.repeat(50));
        console.log('📊 TEST SUMMARY');
        console.log('='.repeat(50));

        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(result => result.success).length;
        const failedTests = totalTests - passedTests;

        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests} ✅`);
        console.log(`Failed: ${failedTests} ❌`);
        console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

        if (failedTests > 0) {
            console.log('\n❌ FAILED TESTS:');
            this.testResults
                .filter(result => !result.success)
                .forEach(result => {
                    console.log(`  - ${result.test}: ${result.message}`);
                });
        }

        return failedTests === 0;
    }
}

// Run tests if called directly
if (require.main === module) {
    const tester = new DiamondFrontendTester();
    tester.runAllTests()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ Test runner failed:', error);
            process.exit(1);
        });
}

module.exports = DiamondFrontendTester;
