#!/usr/bin/env python3
"""
Test diamond update after fixes
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_diamond_update_fixed():
    print("🔧 Testing Diamond Update After Fixes...")
    
    # Login
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    diamond_id = 1
    
    # Test with proper integer vendor_id
    print("\n💎 Testing with proper integer vendor_id...")
    update_data = {
        "shape_id": 1,
        "carat": 1.5,
        "color": "D",
        "clarity": "VVS1",
        "certificate_no": "TEST1753041088",
        "vendor_id": 1  # Integer, not string
    }
    
    print(f"Sending update: {json.dumps(update_data, indent=2)}")
    response = session.put(f"{BASE_URL}/diamonds/{diamond_id}", json=update_data)
    print(f"Update status: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Diamond update successful!")
        updated_diamond = response.json()
        print(f"Updated diamond: {json.dumps(updated_diamond, indent=2)[:300]}...")
    else:
        try:
            error_data = response.json()
            print(f"❌ Error: {json.dumps(error_data, indent=2)}")
        except:
            print(f"❌ Raw error: {response.text}")
    
    # Test diamond creation
    print("\n💎 Testing diamond creation...")
    new_diamond = {
        "shape_id": 1,
        "carat": 2.0,
        "color": "E",
        "clarity": "VS1",
        "certificate_no": f"TEST{hash('new_diamond') % 100000:05d}",
        "vendor_id": 1,
        "purchase_date": "2025-07-21",
        "quantity": 1,
        "minimum_stock": 1,
        "status": "in_stock"
    }
    
    response = session.post(f"{BASE_URL}/diamonds", json=new_diamond)
    print(f"Creation status: {response.status_code}")
    
    if response.status_code in [200, 201]:
        print("✅ Diamond creation successful!")
    else:
        try:
            error_data = response.json()
            print(f"❌ Creation error: {json.dumps(error_data, indent=2)}")
        except:
            print(f"❌ Raw creation error: {response.text}")

if __name__ == "__main__":
    test_diamond_update_fixed()
