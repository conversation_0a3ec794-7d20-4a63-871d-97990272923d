from app import db
from datetime import datetime, date

class Vendor(db.Model):
    """Enhanced vendor model for professional jewelry business management."""
    __tablename__ = 'vendors'
    __table_args__ = {'extend_existing': True}

    # Basic Information
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    company_name = db.Column(db.String(150))  # Legal company name
    vendor_code = db.Column(db.String(20), unique=True)  # Internal vendor code

    # Contact Information
    contact_person = db.Column(db.String(100))  # Primary contact person
    contact_number = db.Column(db.String(20), nullable=False)
    alternate_contact = db.Column(db.String(20))  # Secondary contact
    email = db.Column(db.String(120))  # Email address
    website = db.Column(db.String(200))  # Company website

    # Address Information
    address = db.Column(db.String(255), nullable=False)
    city = db.Column(db.String(100))
    state = db.Column(db.String(100))
    country = db.Column(db.String(100), default='India')
    postal_code = db.Column(db.String(20))

    # Business Information
    gst_number = db.Column(db.String(30), unique=True, nullable=False, index=True)
    pan_number = db.Column(db.String(20))  # PAN number
    business_type = db.Column(db.String(50))  # Manufacturer, Supplier, Trader, etc.
    specialization = db.Column(db.String(200))  # What they specialize in

    # Financial Information
    credit_limit = db.Column(db.Float, default=0.0)  # Credit limit
    payment_terms = db.Column(db.String(100))  # Payment terms (e.g., "30 days")
    bank_name = db.Column(db.String(100))
    bank_account = db.Column(db.String(50))
    ifsc_code = db.Column(db.String(20))

    # Rating and Performance
    rating = db.Column(db.Float, default=0.0)  # Vendor rating (1-5)
    total_orders = db.Column(db.Integer, default=0)  # Total orders placed
    total_value = db.Column(db.Float, default=0.0)  # Total business value

    # Status and Tracking
    status = db.Column(db.String(20), default='active')  # active, inactive, blacklisted
    is_verified = db.Column(db.Boolean, default=False)  # Verification status
    verification_date = db.Column(db.Date)

    # Additional Information
    notes = db.Column(db.Text)  # Additional notes
    documents_path = db.Column(db.String(500))  # Path to vendor documents

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_order_date = db.Column(db.Date)  # Last order date

    # Industry-specific constants
    VALID_STATUSES = ['active', 'inactive', 'blacklisted', 'pending_verification']
    BUSINESS_TYPES = [
        'Manufacturer', 'Supplier', 'Trader', 'Wholesaler',
        'Artisan', 'Importer', 'Exporter'
    ]

    def __init__(self, **kwargs):
        super(Vendor, self).__init__(**kwargs)
        if not self.vendor_code:
            self.generate_vendor_code()

    def generate_vendor_code(self):
        """Generate a unique vendor code"""
        if self.name:
            # Create code from first 3 letters of name + timestamp
            prefix = ''.join(c.upper() for c in self.name if c.isalpha())[:3]
            timestamp = datetime.now().strftime('%y%m%d')
            self.vendor_code = f"VEN{prefix}{timestamp}"

    def update_rating(self):
        """Update vendor rating based on performance metrics"""
        # This would be implemented based on business logic
        # For now, just a placeholder
        pass

    def get_outstanding_amount(self):
        """Calculate outstanding amount from this vendor"""
        # This would calculate based on orders and payments
        # Placeholder for now
        return 0.0

    def is_credit_limit_exceeded(self, amount):
        """Check if adding this amount would exceed credit limit"""
        outstanding = self.get_outstanding_amount()
        return (outstanding + amount) > self.credit_limit

    @classmethod
    def validate_gst_number(cls, gst_number):
        """Validate GST number format"""
        import re
        # Basic GST validation pattern
        pattern = r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$'
        return bool(re.match(pattern, gst_number))

    @classmethod
    def validate_pan_number(cls, pan_number):
        """Validate PAN number format"""
        import re
        pattern = r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$'
        return bool(re.match(pattern, pan_number))

    def to_dict(self):
        """Convert vendor to dictionary for API responses"""
        return {
            'id': self.id,
            'name': self.name,
            'company_name': self.company_name,
            'vendor_code': self.vendor_code,
            'contact_person': self.contact_person,
            'contact_number': self.contact_number,
            'alternate_contact': self.alternate_contact,
            'email': self.email,
            'website': self.website,
            'address': self.address,
            'city': self.city,
            'state': self.state,
            'country': self.country,
            'postal_code': self.postal_code,
            'gst_number': self.gst_number,
            'pan_number': self.pan_number,
            'business_type': self.business_type,
            'specialization': self.specialization,
            'credit_limit': self.credit_limit,
            'payment_terms': self.payment_terms,
            'bank_name': self.bank_name,
            'bank_account': self.bank_account,
            'ifsc_code': self.ifsc_code,
            'rating': self.rating,
            'total_orders': self.total_orders,
            'total_value': self.total_value,
            'status': self.status,
            'is_verified': self.is_verified,
            'verification_date': (
                self.verification_date.isoformat() if self.verification_date else None
            ),
            'notes': self.notes,
            'documents_path': self.documents_path,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_order_date': self.last_order_date.isoformat() if self.last_order_date else None,
            'outstanding_amount': self.get_outstanding_amount()
        }
