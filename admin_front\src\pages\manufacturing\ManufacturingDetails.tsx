import React from 'react';
import { ManufacturingRequest } from '../../types';
import Card from '../../components/ui/Card';

interface ManufacturingDetailsProps {
  request: ManufacturingRequest;
}

const ManufacturingDetails: React.FC<ManufacturingDetailsProps> = ({ request }) => {
  const safeDiamonds = Array.isArray(request.diamonds) ? request.diamonds : [];

  return (
    <div className="space-y-6">
      {/* Request Information */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Request Information</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="text-sm text-gray-500">Request ID:</span>
            <p className="font-medium">{request.id ? `#${request.id.toString().slice(-8)}` : '-'}</p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Status:</span>
            <p className={`font-medium ${request.status === 'open' ? 'text-yellow-600' : 'text-green-600'}`}>
              {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
            </p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Vendor:</span>
            <p className="font-medium">{request.vendor?.name || '-'}</p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Sent Date:</span>
            <p className="font-medium">{request.sent_date ? new Date(request.sent_date).toLocaleDateString() : '-'}</p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Expected Return:</span>
            <p className="font-medium">{request.expected_return_date ? new Date(request.expected_return_date).toLocaleDateString() : '-'}</p>
          </div>
          {request.return_date && (
            <div>
              <span className="text-sm text-gray-500">Returned On:</span>
              <p className="font-medium text-green-600">{new Date(request.return_date).toLocaleDateString()}</p>
            </div>
          )}
          <div>
            <span className="text-sm text-gray-500">Created:</span>
            <p className="font-medium">{request.created_at ? new Date(request.created_at).toLocaleDateString() : '-'}</p>
          </div>
        </div>
      </Card>

      {/* Diamonds Sent */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Diamonds Sent</h3>
        <div className="space-y-2">
          {safeDiamonds.map((item, idx) => (
            <div key={idx} className="text-sm text-gray-700">
              <strong>{item.diamond?.shape || 'Unknown Shape'}:</strong> {item.diamond?.certificate_no || 'No Certificate'} — {item.quantity} pcs
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default ManufacturingDetails;