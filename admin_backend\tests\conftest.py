import pytest
import requests

BASE_URL = "http://127.0.0.1:8000/api"

@pytest.fixture(scope="session")
def base_url():
    return BASE_URL

@pytest.fixture(scope="session")
def test_user():
    return {
        "email": "<EMAIL>",
        "password": "Test1234!",
        "first_name": "API",
        "last_name": "Tester"
    }

# Helper function, not a fixture
def get_tokens(url, user):
    requests.post(f"{url}/auth/register", json=user, timeout=10)
    resp = requests.post(f"{url}/auth/login", json={
        "email": user["email"],
        "password": user["password"]
    }, timeout=10)
    data = resp.json()
    return data.get("access_token"), data.get("refresh_token")