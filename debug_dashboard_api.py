#!/usr/bin/env python3
"""
Debug dashboard API responses
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def debug_dashboard():
    print("🔍 Debugging Dashboard API...")
    
    # Login first
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Test dashboard endpoints
    endpoints = [
        '/dashboard',
        '/dashboard/summary',
        '/dashboard/sales',
        '/dashboard/stock',
        '/dashboard/activity'
    ]
    
    for endpoint in endpoints:
        print(f"\n📊 Testing {endpoint}...")
        response = session.get(f"{BASE_URL}{endpoint}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response structure: {type(data)}")
                if isinstance(data, dict):
                    print(f"Keys: {list(data.keys())}")
                elif isinstance(data, list):
                    print(f"List length: {len(data)}")
                print(f"Sample data: {json.dumps(data, indent=2)[:300]}...")
            except Exception as e:
                print(f"JSON parse error: {e}")
                print(f"Raw response: {response.text[:200]}")
        else:
            print(f"Error: {response.text[:200]}")

if __name__ == "__main__":
    debug_dashboard()
