import React from 'react';

interface TableProps<T> {
  data: T[];
  columns: { key: keyof T; label: string }[];
  renderRow: (item: T) => React.ReactNode;
}

const Table = <T,>({ data, columns, renderRow }: TableProps<T>) => {
  if (!data.length) {
    return <div className="text-center text-gray-500">No data available</div>;
  }

  return (
    <table
      className="table-auto w-full border-collapse border border-gray-200"
      aria-label="Data Table"
      role="table"
    >
      <thead>
        <tr role="row">
          {columns.map((column, index) => (
            <th
              key={index}
              className="border border-gray-300 px-4 py-2 text-left bg-gray-100"
              scope="col"
              role="columnheader"
            >
              {column.label}
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {data.map((item, index) => (
          <tr
            key={index}
            className="border border-gray-300 hover:bg-gray-50"
            role="row"
          >
            {renderRow(item)}
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default Table;
