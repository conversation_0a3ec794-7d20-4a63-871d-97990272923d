import requests
from tests.conftest import get_tokens
from datetime import datetime

def test_diamond_crud(base_url, test_user):
    access_token, _ = get_tokens(base_url, test_user)
    headers = {"Authorization": f"Bearer {access_token}"}
    # Create
    diamond = {
        "shape": "Round",
        "size_mm": "6.50*7.90",
        "carat": 1.0,
        "clarity": "VS1",
        "color": "D",
        "certificate_no": f"CERT{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
        "quantity": 2,
        "purchase_date": "2025-06-21",
        "status": "in_stock"
    }
    r = requests.post(f"{base_url}/diamonds/", json=diamond, headers=headers, timeout=10)
    assert r.status_code == 201
    diamond_id = r.json()["id"]
    # Get
    r = requests.get(f"{base_url}/diamonds/{diamond_id}", headers=headers, timeout=10)
    assert r.status_code == 200
    # Update
    r = requests.put(f"{base_url}/diamonds/{diamond_id}", json={"status": "used"}, headers=headers, timeout=10)
    assert r.status_code == 200
    # Delete
    r = requests.delete(f"{base_url}/diamonds/{diamond_id}", headers=headers, timeout=10)
    assert r.status_code == 200

def test_diamond_assignment(base_url, test_user):
    access_token, _ = get_tokens(base_url, test_user)
    headers = {"Authorization": f"Bearer {access_token}"}

    # Create diamond
    diamond = {
        "shape": "Round",
        "size_mm": "6.50*7.90",
        "carat": 1.0,
        "clarity": "VS1",
        "color": "D",
        "certificate_no": f"CERT{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
        "quantity": 2,
        "purchase_date": "2025-06-21",
        "status": "in_stock"
    }
    r = requests.post(f"{base_url}/diamonds/", json=diamond, headers=headers, timeout=10)
    assert r.status_code == 201
    diamond_id = r.json()["id"]

    # Create manufacturing
    manufacturing = {"name": "Cutting"}
    r = requests.post(f"{base_url}/manufacturing", json=manufacturing, headers=headers, timeout=10)
    assert r.status_code == 201
    manufacturing_id = r.json()["id"]

    # Assign diamond to manufacturing
    r = requests.patch(f"{base_url}/diamonds/{diamond_id}/assign-manufacturing/{manufacturing_id}", headers=headers, timeout=10)
    assert r.status_code == 200

    # Create jewelry
    jewelry = {"name": "Ring"}
    r = requests.post(f"{base_url}/jewelry", json=jewelry, headers=headers, timeout=10)
    assert r.status_code == 201
    jewelry_id = r.json()["id"]

    # Assign diamond to jewelry
    r = requests.patch(f"{base_url}/diamonds/{diamond_id}/assign-jewelry/{jewelry_id}", headers=headers, timeout=10)
    assert r.status_code == 200

    # Cleanup
    requests.delete(f"{base_url}/diamonds/{diamond_id}", headers=headers, timeout=10)
    requests.delete(f"{base_url}/manufacturing/{manufacturing_id}", headers=headers, timeout=10)
    requests.delete(f"{base_url}/jewelry/{jewelry_id}", headers=headers, timeout=10)
