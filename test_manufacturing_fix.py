#!/usr/bin/env python3
"""
Test manufacturing endpoint fixes
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_manufacturing_fix():
    print("🔧 Testing Manufacturing Endpoint Fixes...")
    
    # Login
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Test manufacturing list first
    print("\n🏭 Testing manufacturing list...")
    response = session.get(f"{BASE_URL}/manufacturing")
    if response.status_code == 200:
        manufacturing_data = response.json()
        print(f"✅ Manufacturing list: {len(manufacturing_data)} items")
    else:
        print(f"❌ Manufacturing list failed: {response.status_code}")
    
    # Get available diamonds
    print("\n💎 Getting available diamonds...")
    response = session.get(f"{BASE_URL}/diamonds")
    if response.status_code == 200:
        diamonds_data = response.json()
        diamonds = diamonds_data.get('data', [])
        available_diamonds = [d for d in diamonds if d.get('quantity', 0) > 0]
        
        if available_diamonds:
            diamond = available_diamonds[0]
            print(f"✅ Found diamond: ID {diamond['id']}, Quantity: {diamond['quantity']}")
            
            # Test manufacturing request creation
            print("\n🔧 Testing manufacturing request creation...")
            manufacturing_request = {
                "vendor_id": 1,
                "request_type": "custom_jewelry",
                "description": "Test manufacturing request",
                "diamonds": [
                    {
                        "diamond_id": diamond['id'],
                        "quantity": 1  # Send as integer, not string
                    }
                ],
                "expected_completion": "2025-08-01",
                "notes": "Test request for fixing type errors"
            }
            
            print(f"Sending request: {json.dumps(manufacturing_request, indent=2)}")
            response = session.post(f"{BASE_URL}/manufacturing", json=manufacturing_request)
            print(f"Response status: {response.status_code}")
            
            if response.status_code in [200, 201]:
                created_request = response.json()
                print(f"✅ Manufacturing request created: ID {created_request.get('id', 'Unknown')}")
            else:
                try:
                    error_data = response.json()
                    print(f"❌ Error: {json.dumps(error_data, indent=2)}")
                except:
                    print(f"❌ Raw error: {response.text}")
        else:
            print("❌ No diamonds with available quantity found")
    else:
        print(f"❌ Failed to get diamonds: {response.status_code}")

if __name__ == "__main__":
    test_manufacturing_fix()
