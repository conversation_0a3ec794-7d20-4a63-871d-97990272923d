#!/usr/bin/env python3
"""
Quick test script to verify login functionality is working
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:8000/api"

# Test both admin accounts
ADMIN_ACCOUNTS = [
    {
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109",
        "name": "<PERSON><PERSON>"
    },
    {
        "email": "<EMAIL>", 
        "password": "Ya<PERSON>@9049",
        "name": "<PERSON><PERSON>ala"
    }
]

def test_login(credentials):
    """Test login with given credentials"""
    try:
        print(f"🧪 Testing login for {credentials['name']} ({credentials['email']})...")
        
        response = requests.post(
            f"{BASE_URL}/auth/login",
            json={
                "email": credentials["email"],
                "password": credentials["password"]
            },
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            
            # Check if we got the expected fields
            required_fields = ['access_token', 'refresh_token', 'user']
            missing_fields = [field for field in required_fields if field not in data]
            
            if missing_fields:
                print(f"❌ Login response missing fields: {missing_fields}")
                return False
            
            user = data['user']
            if user.get('role') != 'admin':
                print(f"❌ User role is '{user.get('role')}', expected 'admin'")
                return False
            
            print(f"✅ Login successful!")
            print(f"   User: {user.get('first_name')} {user.get('last_name')}")
            print(f"   Role: {user.get('role')}")
            print(f"   Token: {data['access_token'][:20]}...")
            
            # Test the /auth/me endpoint with the token
            me_response = requests.get(
                f"{BASE_URL}/auth/me",
                headers={"Authorization": f"Bearer {data['access_token']}"}
            )
            
            if me_response.status_code == 200:
                print(f"✅ Token validation successful!")
                return True
            else:
                print(f"❌ Token validation failed: {me_response.status_code}")
                return False
                
        else:
            print(f"❌ Login failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('message', 'Unknown error')}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection failed - is the backend running on {BASE_URL}?")
        return False
    except Exception as e:
        print(f"❌ Login test failed with exception: {e}")
        return False

def main():
    print("🚀 Testing Login Functionality...")
    print("=" * 50)
    
    all_success = True
    
    for credentials in ADMIN_ACCOUNTS:
        success = test_login(credentials)
        all_success = all_success and success
        print()  # Empty line between tests
    
    print("=" * 50)
    if all_success:
        print("🎉 All login tests passed!")
        print("✅ The frontend login should now work correctly.")
        print("\n📋 Login Instructions:")
        print("1. Go to http://localhost:5173/login")
        print("2. Use one of these credentials:")
        for cred in ADMIN_ACCOUNTS:
            print(f"   - Email: {cred['email']}")
            print(f"     Password: {cred['password']}")
    else:
        print("⚠️ Some login tests failed.")
        print("Please check the backend logs and ensure the server is running.")

if __name__ == "__main__":
    main()
