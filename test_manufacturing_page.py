#!/usr/bin/env python3
"""
Test manufacturing page functionality
"""

import requests
import time
from datetime import date, timedelta

BASE_URL = "http://localhost:8000/api"
LOGIN_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

def test_manufacturing_functionality():
    print("🔧 MANUFACTURING PAGE FUNCTIONALITY TEST")
    print("=" * 45)
    
    # Authenticate
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json=LOGIN_CREDENTIALS)
    if response.status_code != 200:
        print("❌ Authentication failed")
        return False
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Test GET manufacturing requests
    print("\n1. Testing GET manufacturing requests...")
    response = session.get(f"{BASE_URL}/manufacturing")
    if response.status_code == 200:
        manufacturing_requests = response.json()
        print(f"✅ GET manufacturing: Found {len(manufacturing_requests)} requests")
    else:
        print(f"❌ GET manufacturing failed: {response.status_code}")
        return False
    
    # Get or create a vendor for testing
    print("\n2. Setting up test vendor...")
    response = session.get(f"{BASE_URL}/vendors")
    if response.status_code == 200:
        vendors = response.json()
        if vendors:
            vendor_id = vendors[0]['id']
            print(f"✅ Using existing vendor: ID {vendor_id}")
        else:
            # Create a vendor
            unique_id = int(time.time())
            vendor_data = {
                "name": f"Manufacturing Test Vendor {unique_id}",
                "gst_number": f"29AAAAA{unique_id % 100000:05d}A1Z5",
                "contact_number": "+91-9876543210",
                "address": "Test Manufacturing Address"
            }
            response = session.post(f"{BASE_URL}/vendors", json=vendor_data)
            if response.status_code in [200, 201]:
                vendor = response.json()
                vendor_id = vendor.get('id')
                print(f"✅ Created test vendor: ID {vendor_id}")
            else:
                print(f"❌ Failed to create vendor: {response.status_code}")
                return False
    else:
        print(f"❌ Failed to get vendors: {response.status_code}")
        return False
    
    # Get or create diamonds for testing
    print("\n3. Setting up test diamonds...")
    response = session.get(f"{BASE_URL}/diamonds")
    if response.status_code == 200:
        diamonds_data = response.json()
        diamonds = diamonds_data.get('data', [])
        
        if not diamonds:
            # Create a test diamond
            shapes_response = session.get(f"{BASE_URL}/shapes")
            if shapes_response.status_code == 200:
                shapes = shapes_response.json()
                if shapes:
                    diamond_data = {
                        "shape_id": shapes[0]['id'],
                        "vendor_id": vendor_id,
                        "size_mm": "7.0x7.0x4.0",
                        "carat": 1.0,
                        "color": "E",
                        "clarity": "VS1",
                        "certificate_no": f"MFGTEST{int(time.time())}",
                        "cost_price": 30000,
                        "retail_price": 45000,
                        "quantity": 2,
                        "status": "in_stock"
                    }
                    response = session.post(f"{BASE_URL}/diamonds", json=diamond_data)
                    if response.status_code in [200, 201]:
                        diamond = response.json()
                        diamond_id = diamond.get('id')
                        print(f"✅ Created test diamond: ID {diamond_id}")
                    else:
                        print(f"❌ Failed to create diamond: {response.status_code}")
                        return False
        else:
            diamond_id = diamonds[0]['id']
            print(f"✅ Using existing diamond: ID {diamond_id}")
    else:
        print(f"❌ Failed to get diamonds: {response.status_code}")
        return False
    
    # Test CREATE manufacturing request
    print("\n4. Testing CREATE manufacturing request...")
    today = date.today()
    expected_date = today + timedelta(days=30)
    
    manufacturing_data = {
        "vendor_id": vendor_id,
        "order_type": "Diamond Cutting",
        "priority": "normal",
        "description": "Test manufacturing request for diamond cutting",
        "sent_date": today.isoformat(),
        "expected_return_date": expected_date.isoformat(),
        "estimated_cost": 5000,
        "diamonds": [
            {
                "diamond_id": diamond_id,
                "quantity": 1,
                "original_weight": 1.0
            }
        ]
    }
    
    response = session.post(f"{BASE_URL}/manufacturing", json=manufacturing_data)
    if response.status_code in [200, 201]:
        manufacturing_request = response.json()
        request_id = manufacturing_request.get('id')
        print(f"✅ CREATE manufacturing: ID {request_id}")
        
        # Check enhanced fields
        enhanced_fields = ['order_type', 'priority', 'estimated_cost', 'description']
        found_fields = [f for f in enhanced_fields if manufacturing_request.get(f) is not None]
        print(f"✅ Enhanced fields: {found_fields}")
        
    else:
        print(f"❌ CREATE manufacturing failed: {response.status_code}")
        print(f"Error: {response.text[:200]}")
        return False
    
    # Test READ manufacturing request
    print("\n5. Testing READ manufacturing request...")
    response = session.get(f"{BASE_URL}/manufacturing/{request_id}")
    if response.status_code == 200:
        print(f"✅ READ manufacturing: Retrieved successfully")
    else:
        print(f"❌ READ manufacturing failed: {response.status_code}")
    
    # Test UPDATE manufacturing request
    print("\n6. Testing UPDATE manufacturing request...")
    update_data = {
        "status": "in_progress",
        "notes": "Manufacturing started",
        "actual_cost": 5500
    }
    
    response = session.put(f"{BASE_URL}/manufacturing/{request_id}", json=update_data)
    if response.status_code == 200:
        updated_request = response.json()
        print(f"✅ UPDATE manufacturing: Updated successfully")
        print(f"   Status: {updated_request.get('status')}")
    else:
        print(f"❌ UPDATE manufacturing failed: {response.status_code}")
    
    # Test manufacturing filtering
    print("\n7. Testing manufacturing filtering...")
    filters = [
        {"status": "in_progress", "name": "Status filter"},
        {"vendor_id": vendor_id, "name": "Vendor filter"}
    ]
    
    for filter_data in filters:
        filter_name = filter_data.pop('name')
        response = session.get(f"{BASE_URL}/manufacturing", params=filter_data)
        if response.status_code == 200:
            filtered_requests = response.json()
            print(f"   ✅ {filter_name}: Found {len(filtered_requests)} requests")
        else:
            print(f"   ❌ {filter_name} failed: {response.status_code}")
    
    # Test manufacturing history
    print("\n8. Testing manufacturing history...")
    response = session.get(f"{BASE_URL}/manufacturing/history")
    if response.status_code == 200:
        history = response.json()
        print(f"✅ Manufacturing history: Found {len(history)} records")
    else:
        print(f"❌ Manufacturing history failed: {response.status_code}")
    
    # Test DELETE manufacturing request
    print("\n9. Testing DELETE manufacturing request...")
    response = session.delete(f"{BASE_URL}/manufacturing/{request_id}")
    if response.status_code in [200, 204]:
        print(f"✅ DELETE manufacturing: Deleted successfully")
    else:
        print(f"❌ DELETE manufacturing failed: {response.status_code}")
    
    print("\n🎯 Manufacturing functionality test complete!")
    return True

if __name__ == "__main__":
    success = test_manufacturing_functionality()
    if success:
        print("✅ All manufacturing functionality is working correctly!")
    else:
        print("❌ Some manufacturing functionality needs attention.")
