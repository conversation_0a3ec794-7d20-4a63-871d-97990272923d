#!/usr/bin/env python3
"""
Final diamond functionality test
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000/api"
LOGIN_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

def test_diamond_functionality():
    print("💎 FINAL DIAMOND FUNCTIONALITY TEST")
    print("=" * 50)
    
    # Authenticate
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json=LOGIN_CREDENTIALS)
    if response.status_code != 200:
        print("❌ Authentication failed")
        return False
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Get existing vendors or create one
    print("\n1. Getting vendors...")
    response = session.get(f"{BASE_URL}/vendors")
    if response.status_code == 200:
        vendors = response.json()
        if vendors:
            vendor_id = vendors[0]['id']
            print(f"✅ Using existing vendor: ID {vendor_id}")
        else:
            # Create vendor with unique GST
            vendor_data = {
                "name": f"Test Vendor {int(time.time())}",
                "company_name": "Test Company Ltd",
                "gst_number": f"22AAAAA{int(time.time()) % 10000:04d}A1Z5",
                "contact_number": "+91-9876543210",
                "email": f"test{int(time.time())}@vendor.com",
                "address": "123 Test Street, Test City"
            }
            
            response = session.post(f"{BASE_URL}/vendors", json=vendor_data)
            if response.status_code in [200, 201]:
                vendor = response.json()
                vendor_id = vendor.get('id')
                print(f"✅ Created vendor: ID {vendor_id}")
            else:
                print(f"❌ Vendor creation failed: {response.status_code}")
                return False
    else:
        print(f"❌ Failed to get vendors: {response.status_code}")
        return False
    
    # Get shapes
    print("\n2. Getting shapes...")
    response = session.get(f"{BASE_URL}/shapes")
    if response.status_code == 200:
        shapes = response.json()
        if shapes:
            shape_id = shapes[0]['id']
            print(f"✅ Using shape: {shapes[0]['name']} (ID: {shape_id})")
        else:
            print("❌ No shapes available")
            return False
    else:
        print(f"❌ Failed to get shapes: {response.status_code}")
        return False
    
    # Test diamond CRUD operations
    print("\n3. Testing Diamond CRUD Operations...")
    
    # CREATE
    diamond_data = {
        "shape_id": shape_id,
        "vendor_id": vendor_id,
        "size_mm": "7.5x7.5x4.5",
        "carat": 1.5,
        "color": "D",
        "clarity": "VVS1",
        "cut_grade": "Excellent",
        "polish": "Excellent",
        "symmetry": "Excellent",
        "fluorescence": "None",
        "certificate_no": f"TEST{int(time.time())}",
        "certification_lab": "GIA",
        "cost_price": 50000,
        "retail_price": 75000,
        "quantity": 1,
        "status": "in_stock",
        "location": "Vault A",
        "notes": "Test diamond for CRUD testing"
    }
    
    response = session.post(f"{BASE_URL}/diamonds", json=diamond_data)
    if response.status_code in [200, 201]:
        diamond = response.json()
        diamond_id = diamond.get('id')
        print(f"   ✅ CREATE: Diamond created (ID: {diamond_id})")
        
        # Check profit margin
        profit_margin = diamond.get('profit_margin')
        if profit_margin is not None:
            expected = ((75000 - 50000) / 75000) * 100
            print(f"   ✅ Profit margin: {profit_margin:.2f}% (expected: {expected:.2f}%)")
        else:
            print("   ⚠️  Profit margin not calculated")
    else:
        print(f"   ❌ CREATE failed: {response.status_code}")
        print(f"   Error: {response.text[:200]}")
        return False
    
    # READ
    response = session.get(f"{BASE_URL}/diamonds/{diamond_id}")
    if response.status_code == 200:
        print(f"   ✅ READ: Diamond retrieved successfully")
    else:
        print(f"   ❌ READ failed: {response.status_code}")
    
    # UPDATE
    update_data = {
        "notes": "Updated test diamond",
        "cost_price": 55000,
        "retail_price": 80000
    }
    response = session.put(f"{BASE_URL}/diamonds/{diamond_id}", json=update_data)
    if response.status_code == 200:
        updated_diamond = response.json()
        print(f"   ✅ UPDATE: Diamond updated successfully")
        
        # Check updated profit margin
        new_margin = updated_diamond.get('profit_margin')
        if new_margin is not None:
            expected = ((80000 - 55000) / 80000) * 100
            print(f"   ✅ Updated profit margin: {new_margin:.2f}% (expected: {expected:.2f}%)")
    else:
        print(f"   ❌ UPDATE failed: {response.status_code}")
    
    # Test filtering
    print("\n4. Testing Diamond Filtering...")
    filters = [
        {"color": "D", "name": "Color filter"},
        {"status": "in_stock", "name": "Status filter"},
        {"search": "test", "name": "Search filter"}
    ]
    
    for filter_data in filters:
        filter_name = filter_data.pop('name')
        response = session.get(f"{BASE_URL}/diamonds", params=filter_data)
        if response.status_code == 200:
            data = response.json()
            count = len(data.get('data', []))
            print(f"   ✅ {filter_name}: Found {count} diamonds")
        else:
            print(f"   ❌ {filter_name} failed: {response.status_code}")
    
    # Test stock deduction
    print("\n5. Testing Stock Operations...")
    deduct_data = {"quantity": 1}
    response = session.patch(f"{BASE_URL}/diamonds/{diamond_id}/deduct", json=deduct_data)
    if response.status_code == 200:
        print(f"   ✅ Stock deduction successful")
    else:
        print(f"   ❌ Stock deduction failed: {response.status_code}")
    
    # DELETE
    response = session.delete(f"{BASE_URL}/diamonds/{diamond_id}")
    if response.status_code in [200, 204]:
        print(f"   ✅ DELETE: Diamond deleted successfully")
    else:
        print(f"   ❌ DELETE failed: {response.status_code}")
    
    print("\n🎯 Diamond functionality test complete!")
    return True

if __name__ == "__main__":
    success = test_diamond_functionality()
    if success:
        print("✅ All diamond functionality is working correctly!")
    else:
        print("❌ Some diamond functionality needs attention.")
