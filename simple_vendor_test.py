#!/usr/bin/env python3
import requests

BASE_URL = "http://localhost:8000/api"

# Login
session = requests.Session()
response = session.post(f"{BASE_URL}/auth/login", json={
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
})

token = response.json().get('access_token')
session.headers.update({'Authorization': f'Bearer {token}'})

# Test minimal vendor
vendor = {
    "name": "Simple Test",
    "contact_number": "1234567890",
    "address": "Test Address",
    "gst_number": "27SIMPLE123456Z5"
}

response = session.post(f"{BASE_URL}/vendors", json=vendor)
print(f"Status: {response.status_code}")
print(f"Response: {response.text}")
