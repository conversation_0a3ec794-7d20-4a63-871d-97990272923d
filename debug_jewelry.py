#!/usr/bin/env python3
"""
Debug jewelry operations
"""

import requests

BASE_URL = "http://localhost:8000/api"

def debug_jewelry():
    print("🔍 Debugging Jewelry Operations")
    print("=" * 30)
    
    # Login
    response = requests.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    token = response.json().get('access_token')
    headers = {'Authorization': f'Bearer {token}'}
    print("✅ Authenticated")
    
    # Check existing jewelry
    response = requests.get(f"{BASE_URL}/jewelry", headers=headers)
    print(f"\nExisting jewelry: {response.status_code}")
    if response.status_code == 200:
        jewelry_items = response.json()
        print(f"Found {len(jewelry_items)} jewelry items")
        if jewelry_items:
            jewelry_id = jewelry_items[0]['id']
            print(f"First jewelry ID: {jewelry_id}")
            
            # Test READ specific jewelry
            response = requests.get(f"{BASE_URL}/jewelry/{jewelry_id}", headers=headers)
            print(f"READ jewelry {jewelry_id}: {response.status_code}")
            if response.status_code != 200:
                print(f"Error: {response.text}")
            
            # Test UPDATE jewelry
            update_data = {"name": "Updated Test Jewelry"}
            response = requests.put(f"{BASE_URL}/jewelry/{jewelry_id}", json=update_data, headers=headers)
            print(f"UPDATE jewelry {jewelry_id}: {response.status_code}")
            if response.status_code != 200:
                print(f"Error: {response.text}")
            
            # Test DELETE jewelry (be careful!)
            # response = requests.delete(f"{BASE_URL}/jewelry/{jewelry_id}", headers=headers)
            # print(f"DELETE jewelry {jewelry_id}: {response.status_code}")
            # if response.status_code not in [200, 204]:
            #     print(f"Error: {response.text}")
    
    # Test analytics endpoint
    response = requests.get(f"{BASE_URL}/jewelry/analytics", headers=headers)
    print(f"Analytics: {response.status_code}")
    if response.status_code != 200:
        print(f"Analytics error: {response.text}")

if __name__ == "__main__":
    debug_jewelry()
