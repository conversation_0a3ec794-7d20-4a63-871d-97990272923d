#!/usr/bin/env python3
"""
Comprehensive Diamond Page Testing - Backend & Frontend Integration
Production-ready testing for real-world jewelry business use
"""

import requests
import time
import json
from datetime import date, datetime

BASE_URL = "http://localhost:8000/api"
FRONTEND_URL = "http://localhost:5173"

class DiamondPageTester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.test_results = {
            'backend_auth': False,
            'backend_crud': {},
            'backend_validation': {},
            'backend_business_logic': {},
            'frontend_accessibility': False,
            'frontend_functionality': {},
            'integration': {},
            'performance': {},
            'security': {},
            'production_readiness': {}
        }
    
    def authenticate(self):
        """Test authentication system"""
        print("🔐 Testing Authentication System...")
        
        # Test login
        login_data = {
            "email": "<EMAIL>",
            "password": "<PERSON><PERSON>@109"
        }
        
        response = self.session.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            self.token = data.get('access_token')
            self.session.headers.update({'Authorization': f'Bearer {self.token}'})
            self.test_results['backend_auth'] = True
            print("✅ Authentication successful")
            return True
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    
    def test_backend_diamond_crud(self):
        """Test all Diamond CRUD operations"""
        print("\n💎 Testing Diamond CRUD Operations...")
        
        crud_results = {}
        
        # Test GET diamonds (list)
        print("  Testing GET /diamonds...")
        response = self.session.get(f"{BASE_URL}/diamonds")
        crud_results['list'] = {
            'status': response.status_code,
            'success': response.status_code == 200,
            'response_time': response.elapsed.total_seconds()
        }
        if response.status_code == 200:
            data = response.json()
            crud_results['list']['count'] = len(data.get('data', []))
            print(f"    ✅ GET diamonds: {crud_results['list']['count']} items")
        else:
            print(f"    ❌ GET diamonds failed: {response.status_code}")
        
        # Test GET shapes
        print("  Testing GET /shapes...")
        response = self.session.get(f"{BASE_URL}/shapes")
        crud_results['shapes'] = {
            'status': response.status_code,
            'success': response.status_code == 200
        }
        if response.status_code == 200:
            shapes = response.json()
            crud_results['shapes']['count'] = len(shapes)
            print(f"    ✅ GET shapes: {len(shapes)} shapes available")
        else:
            print(f"    ❌ GET shapes failed: {response.status_code}")
        
        # Test CREATE diamond
        print("  Testing POST /diamonds...")
        test_diamond = {
            "shape_id": 1,
            "carat": 1.25,
            "color": "G",
            "clarity": "VS1",
            "cut_grade": "Excellent",
            "polish": "Excellent",
            "symmetry": "Very Good",
            "fluorescence": "None",
            "certificate_no": f"TEST{int(time.time())}",
            "certification_lab": "GIA",
            "cost_price": 5000,
            "retail_price": 8000,
            "quantity": 1,
            "status": "in_stock",
            "vendor_id": 1,
            "location": "Vault A",
            "size_mm": "7.2x7.2x4.3"
        }
        
        response = self.session.post(f"{BASE_URL}/diamonds", json=test_diamond)
        crud_results['create'] = {
            'status': response.status_code,
            'success': response.status_code in [200, 201]
        }
        
        if crud_results['create']['success']:
            created_diamond = response.json()
            diamond_id = created_diamond.get('id')
            crud_results['create']['diamond_id'] = diamond_id
            print(f"    ✅ CREATE diamond: ID {diamond_id}")
            
            # Test GET single diamond
            print("  Testing GET /diamonds/{id}...")
            response = self.session.get(f"{BASE_URL}/diamonds/{diamond_id}")
            crud_results['get_single'] = {
                'status': response.status_code,
                'success': response.status_code == 200
            }
            if response.status_code == 200:
                print(f"    ✅ GET single diamond: Retrieved successfully")
            else:
                print(f"    ❌ GET single diamond failed: {response.status_code}")
            
            # Test UPDATE diamond
            print("  Testing PUT /diamonds/{id}...")
            update_data = {
                "carat": 1.30,
                "cost_price": 5200,
                "retail_price": 8500,
                "status": "reserved"
            }
            response = self.session.put(f"{BASE_URL}/diamonds/{diamond_id}", json=update_data)
            crud_results['update'] = {
                'status': response.status_code,
                'success': response.status_code == 200
            }
            if response.status_code == 200:
                print(f"    ✅ UPDATE diamond: Updated successfully")
            else:
                print(f"    ❌ UPDATE diamond failed: {response.status_code}")
            
            # Test DELETE diamond
            print("  Testing DELETE /diamonds/{id}...")
            response = self.session.delete(f"{BASE_URL}/diamonds/{diamond_id}")
            crud_results['delete'] = {
                'status': response.status_code,
                'success': response.status_code in [200, 204]
            }
            if crud_results['delete']['success']:
                print(f"    ✅ DELETE diamond: Deleted successfully")
            else:
                print(f"    ❌ DELETE diamond failed: {response.status_code}")
        else:
            print(f"    ❌ CREATE diamond failed: {response.status_code}")
            print(f"    Response: {response.text}")
        
        self.test_results['backend_crud'] = crud_results
        return crud_results
    
    def test_backend_validation(self):
        """Test backend validation rules"""
        print("\n🔍 Testing Backend Validation...")
        
        validation_results = {}
        
        # Test invalid data
        invalid_tests = [
            {
                'name': 'Invalid color',
                'data': {'shape_id': 1, 'carat': 1.0, 'color': 'XX', 'clarity': 'VS1', 'certificate_no': f'INVALID_COLOR_{int(time.time())}'},
                'expected_error': 'color'
            },
            {
                'name': 'Invalid clarity',
                'data': {'shape_id': 1, 'carat': 1.0, 'color': 'G', 'clarity': 'INVALID', 'certificate_no': f'INVALID_CLARITY_{int(time.time())}'},
                'expected_error': 'clarity'
            },
            {
                'name': 'Negative carat',
                'data': {'shape_id': 1, 'carat': -1.0, 'color': 'G', 'clarity': 'VS1', 'certificate_no': f'NEGATIVE_CARAT_{int(time.time())}'},
                'expected_error': 'carat'
            },
            {
                'name': 'Missing required fields',
                'data': {'shape_id': 1},
                'expected_error': 'required'
            }
        ]
        
        for test in invalid_tests:
            print(f"  Testing {test['name']}...")
            response = self.session.post(f"{BASE_URL}/diamonds", json=test['data'])
            validation_results[test['name']] = {
                'status': response.status_code,
                'rejected': response.status_code == 400,
                'response': response.text[:100] if response.text else ''
            }
            if response.status_code == 400:
                print(f"    ✅ {test['name']}: Properly rejected")
            else:
                print(f"    ❌ {test['name']}: Should have been rejected (got {response.status_code})")
        
        self.test_results['backend_validation'] = validation_results
        return validation_results
    
    def test_frontend_accessibility(self):
        """Test frontend accessibility"""
        print("\n🌐 Testing Frontend Accessibility...")
        
        try:
            response = requests.get(FRONTEND_URL, timeout=10)
            if response.status_code == 200:
                self.test_results['frontend_accessibility'] = True
                print("✅ Frontend accessible")
                return True
            else:
                print(f"❌ Frontend not accessible: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Frontend connection error: {str(e)}")
            return False
    
    def test_performance(self):
        """Test performance metrics"""
        print("\n⚡ Testing Performance...")
        
        performance_results = {}
        
        # Test API response times
        start_time = time.time()
        response = self.session.get(f"{BASE_URL}/diamonds")
        end_time = time.time()
        
        performance_results['api_response_time'] = (end_time - start_time) * 1000  # ms
        performance_results['api_performance'] = 'excellent' if performance_results['api_response_time'] < 500 else 'acceptable' if performance_results['api_response_time'] < 2000 else 'poor'
        
        print(f"  API Response Time: {performance_results['api_response_time']:.2f}ms ({performance_results['api_performance']})")
        
        self.test_results['performance'] = performance_results
        return performance_results
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "="*60)
        print("📊 COMPREHENSIVE DIAMOND PAGE TEST REPORT")
        print("="*60)
        
        # Authentication
        auth_status = "✅ PASS" if self.test_results['backend_auth'] else "❌ FAIL"
        print(f"🔐 Authentication: {auth_status}")
        
        # Backend CRUD
        crud = self.test_results['backend_crud']
        crud_passed = sum(1 for op in crud.values() if op.get('success', False))
        crud_total = len(crud)
        print(f"💎 CRUD Operations: {crud_passed}/{crud_total} passed")
        
        # Validation
        validation = self.test_results['backend_validation']
        validation_passed = sum(1 for test in validation.values() if test.get('rejected', False))
        validation_total = len(validation)
        print(f"🔍 Validation Tests: {validation_passed}/{validation_total} passed")
        
        # Frontend
        frontend_status = "✅ ACCESSIBLE" if self.test_results['frontend_accessibility'] else "❌ NOT ACCESSIBLE"
        print(f"🌐 Frontend: {frontend_status}")
        
        # Performance
        perf = self.test_results['performance']
        if perf:
            print(f"⚡ Performance: {perf['api_response_time']:.2f}ms ({perf['api_performance']})")
        
        # Overall Status
        overall_score = 0
        total_tests = 0
        
        if self.test_results['backend_auth']:
            overall_score += 1
        total_tests += 1
        
        if crud_total > 0:
            overall_score += crud_passed / crud_total
            total_tests += 1
        
        if validation_total > 0:
            overall_score += validation_passed / validation_total
            total_tests += 1
        
        if self.test_results['frontend_accessibility']:
            overall_score += 1
        total_tests += 1
        
        overall_percentage = (overall_score / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n🎯 OVERALL SCORE: {overall_percentage:.1f}%")
        
        if overall_percentage >= 90:
            print("🏆 STATUS: PRODUCTION READY")
        elif overall_percentage >= 70:
            print("⚠️  STATUS: NEEDS MINOR FIXES")
        else:
            print("❌ STATUS: NEEDS MAJOR FIXES")
        
        return overall_percentage

def main():
    """Run comprehensive diamond page testing"""
    tester = DiamondPageTester()
    
    print("🧪 COMPREHENSIVE DIAMOND PAGE TESTING")
    print("Testing both backend and frontend for production readiness")
    print("="*60)
    
    # Test authentication
    if not tester.authenticate():
        print("❌ Cannot proceed without authentication")
        return
    
    # Test backend functionality
    tester.test_backend_diamond_crud()
    tester.test_backend_validation()
    
    # Test frontend
    tester.test_frontend_accessibility()
    
    # Test performance
    tester.test_performance()
    
    # Generate final report
    score = tester.generate_report()
    
    return score

if __name__ == "__main__":
    score = main()
    print(f"\nTest completed with score: {score:.1f}%")
