#!/usr/bin/env python3
"""
Quick test to verify the diamond creation and CSV export fixes
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://127.0.0.1:8000/api"
TEST_USER = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

def test_fixes():
    """Test the diamond creation and CSV export fixes"""
    session = requests.Session()
    
    # Login first
    print("🔐 Logging in...")
    try:
        login_response = session.post(f"{BASE_URL}/auth/login", json=TEST_USER)
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        token_data = login_response.json()
        token = token_data.get('access_token')
        session.headers.update({'Authorization': f'Bearer {token}'})
        print("✅ Login successful")
        
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Test 1: Diamond Creation
    print("\n🧪 Testing Diamond Creation...")
    cert_no = f"FIX_TEST_{int(time.time())}"
    print(f"📋 Certificate Number: {cert_no}")
    diamond_data = {
        "shape_id": 1,
        "carat": 1.25,
        "color": "D",
        "clarity": "VVS1",
        "certificate_no": cert_no,
        "certification_lab": "GIA",
        "quantity": 1,
        "status": "in_stock"
    }
    
    try:
        response = session.post(f"{BASE_URL}/diamonds", json=diamond_data)
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 201:
            print("✅ Diamond creation successful!")
            created_diamond = response.json()
            diamond_id = created_diamond.get('id')
            print(f"   Created diamond ID: {diamond_id}")
        else:
            print(f"❌ Diamond creation failed")
            try:
                error_data = response.json()
                print(f"   Error: {error_data}")
            except:
                print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Diamond creation error: {e}")
        return False
    
    # Test 2: CSV Export (original endpoint)
    print("\n🧪 Testing CSV Export (/diamonds/export)...")
    try:
        response = session.get(f"{BASE_URL}/diamonds/export")
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ CSV export (original) successful!")
            print(f"   Content-Type: {response.headers.get('content-type')}")
            print(f"   Content-Length: {len(response.content)} bytes")
        else:
            print(f"❌ CSV export (original) failed")
            
    except Exception as e:
        print(f"❌ CSV export (original) error: {e}")
    
    # Test 3: CSV Export (alternative endpoint)
    print("\n🧪 Testing CSV Export (/diamonds/export/csv)...")
    try:
        response = session.get(f"{BASE_URL}/diamonds/export/csv")
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ CSV export (alternative) successful!")
            print(f"   Content-Type: {response.headers.get('content-type')}")
            print(f"   Content-Length: {len(response.content)} bytes")
        else:
            print(f"❌ CSV export (alternative) failed")
            
    except Exception as e:
        print(f"❌ CSV export (alternative) error: {e}")
    
    return True

if __name__ == "__main__":
    print("🚀 Testing Diamond API Fixes...")
    print("=" * 50)
    
    test_fixes()
    
    print("\n🎯 Next Steps:")
    print("1. Run the full test suite: python admin_backend/test_diamond_api.py")
    print("2. Check if all tests now pass")
    print("3. Verify the frontend can create diamonds and export CSV")
