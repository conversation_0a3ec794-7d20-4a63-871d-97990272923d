import qrcode
from io import BytesIO
import base64
from PIL import Image, ImageDraw, ImageFont
import os

def generate_qr_code(data: str, size: int = 200) -> str:
    """
    Generate a QR code for the given data and return as base64 string.
    
    Args:
        data: The data to encode in the QR code
        size: Size of the QR code image
        
    Returns:
        Base64 encoded PNG image string
    """
    try:
        # Create QR code instance
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        
        # Add data and make QR code
        qr.add_data(data)
        qr.make(fit=True)
        
        # Create image
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Resize if needed
        if size != img.size[0]:
            img = img.resize((size, size), Image.Resampling.LANCZOS)
        
        # Convert to base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{img_str}"
        
    except Exception as e:
        raise Exception(f"Failed to generate QR code: {str(e)}")

def generate_diamond_qr_code(diamond_id: int, certificate_no: str = None) -> str:
    """
    Generate a QR code specifically for a diamond.
    
    Args:
        diamond_id: The diamond ID
        certificate_no: Optional certificate number
        
    Returns:
        Base64 encoded QR code image
    """
    # Create diamond-specific data
    qr_data = {
        "type": "diamond",
        "id": diamond_id,
        "certificate": certificate_no or "",
        "url": f"https://your-domain.com/diamonds/{diamond_id}"
    }
    
    # Convert to JSON string
    import json
    data_string = json.dumps(qr_data, separators=(',', ':'))
    
    return generate_qr_code(data_string)

def generate_diamond_label(diamond_data: dict, include_qr: bool = True) -> str:
    """
    Generate a printable label for a diamond with QR code.
    
    Args:
        diamond_data: Dictionary containing diamond information
        include_qr: Whether to include QR code
        
    Returns:
        Base64 encoded label image
    """
    try:
        # Create label image (300x200 pixels for standard label)
        width, height = 300, 200
        img = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(img)
        
        # Try to load a font, fallback to default if not available
        try:
            font_large = ImageFont.truetype("arial.ttf", 14)
            font_medium = ImageFont.truetype("arial.ttf", 12)
            font_small = ImageFont.truetype("arial.ttf", 10)
        except:
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # Draw border
        draw.rectangle([2, 2, width-3, height-3], outline='black', width=2)
        
        # Title
        title = f"Diamond #{diamond_data.get('id', 'N/A')}"
        draw.text((10, 10), title, fill='black', font=font_large)
        
        # Diamond details
        y_pos = 35
        details = [
            f"Shape: {diamond_data.get('shape', 'N/A')}",
            f"Carat: {diamond_data.get('carat', 'N/A')}",
            f"Color: {diamond_data.get('color', 'N/A')}",
            f"Clarity: {diamond_data.get('clarity', 'N/A')}",
            f"Cert: {diamond_data.get('certificate_no', 'N/A')}"
        ]
        
        for detail in details:
            draw.text((10, y_pos), detail, fill='black', font=font_medium)
            y_pos += 18
        
        # Add QR code if requested
        if include_qr and diamond_data.get('id'):
            qr_code_data = generate_diamond_qr_code(
                diamond_data['id'], 
                diamond_data.get('certificate_no')
            )
            
            # Decode base64 QR code
            qr_data = qr_code_data.split(',')[1]
            qr_bytes = base64.b64decode(qr_data)
            qr_img = Image.open(BytesIO(qr_bytes))
            
            # Resize QR code to fit label
            qr_size = 80
            qr_img = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)
            
            # Paste QR code on the right side
            img.paste(qr_img, (width - qr_size - 10, height - qr_size - 10))
        
        # Convert to base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{img_str}"
        
    except Exception as e:
        raise Exception(f"Failed to generate diamond label: {str(e)}")

def generate_barcode_128(data: str, width: int = 200, height: int = 50) -> str:
    """
    Generate a Code 128 barcode (simplified version using text).
    For production use, consider using python-barcode library.
    
    Args:
        data: Data to encode
        width: Barcode width
        height: Barcode height
        
    Returns:
        Base64 encoded barcode image
    """
    try:
        # Create simple barcode-like image with text
        img = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(img)
        
        # Draw border
        draw.rectangle([0, 0, width-1, height-1], outline='black', width=1)
        
        # Draw vertical lines to simulate barcode
        line_width = 2
        x_pos = 10
        while x_pos < width - 10:
            line_height = height - 20
            draw.rectangle([x_pos, 10, x_pos + line_width, 10 + line_height], fill='black')
            x_pos += 4
        
        # Add text below
        try:
            font = ImageFont.truetype("arial.ttf", 12)
        except:
            font = ImageFont.load_default()
        
        text_bbox = draw.textbbox((0, 0), data, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_x = (width - text_width) // 2
        draw.text((text_x, height - 15), data, fill='black', font=font)
        
        # Convert to base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{img_str}"
        
    except Exception as e:
        raise Exception(f"Failed to generate barcode: {str(e)}")
