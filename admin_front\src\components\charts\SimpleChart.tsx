import React from 'react';

interface ChartData {
  label: string;
  value: number;
  color?: string;
}

interface SimpleChartProps {
  data: ChartData[];
  type: 'bar' | 'pie' | 'doughnut';
  title?: string;
  className?: string;
}

const SimpleChart: React.FC<SimpleChartProps> = ({ 
  data, 
  type, 
  title, 
  className = '' 
}) => {
  const colors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
    '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
  ];

  const maxValue = Math.max(...data.map(d => d.value));

  if (type === 'bar') {
    return (
      <div className={`bg-white p-4 rounded-lg shadow ${className}`}>
        {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
        <div className="space-y-3">
          {data.map((item, index) => (
            <div key={item.label} className="flex items-center">
              <div className="w-20 text-sm text-gray-600 truncate">
                {item.label}
              </div>
              <div className="flex-1 mx-3">
                <div className="bg-gray-200 rounded-full h-4 relative">
                  <div
                    className="h-4 rounded-full transition-all duration-300"
                    style={{
                      width: `${(item.value / maxValue) * 100}%`,
                      backgroundColor: item.color || colors[index % colors.length]
                    }}
                  />
                </div>
              </div>
              <div className="w-12 text-sm font-medium text-right">
                {item.value}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (type === 'pie' || type === 'doughnut') {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    let currentAngle = 0;
    const radius = 80;
    const centerX = 100;
    const centerY = 100;
    const innerRadius = type === 'doughnut' ? 40 : 0;

    const createPath = (startAngle: number, endAngle: number, outerRadius: number, innerRadius: number) => {
      const startAngleRad = (startAngle * Math.PI) / 180;
      const endAngleRad = (endAngle * Math.PI) / 180;

      const x1 = centerX + outerRadius * Math.cos(startAngleRad);
      const y1 = centerY + outerRadius * Math.sin(startAngleRad);
      const x2 = centerX + outerRadius * Math.cos(endAngleRad);
      const y2 = centerY + outerRadius * Math.sin(endAngleRad);

      const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

      if (innerRadius === 0) {
        return `M ${centerX} ${centerY} L ${x1} ${y1} A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;
      } else {
        const x3 = centerX + innerRadius * Math.cos(endAngleRad);
        const y3 = centerY + innerRadius * Math.sin(endAngleRad);
        const x4 = centerX + innerRadius * Math.cos(startAngleRad);
        const y4 = centerY + innerRadius * Math.sin(startAngleRad);

        return `M ${x1} ${y1} A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2} L ${x3} ${y3} A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4} Z`;
      }
    };

    return (
      <div className={`bg-white p-4 rounded-lg shadow ${className}`}>
        {title && <h3 className="text-lg font-semibold mb-4">{title}</h3>}
        <div className="flex items-center">
          <svg width="200" height="200" className="flex-shrink-0">
            {data.map((item, index) => {
              const percentage = (item.value / total) * 100;
              const angle = (item.value / total) * 360;
              const path = createPath(currentAngle, currentAngle + angle, radius, innerRadius);
              currentAngle += angle;

              return (
                <path
                  key={item.label}
                  d={path}
                  fill={item.color || colors[index % colors.length]}
                  stroke="white"
                  strokeWidth="2"
                />
              );
            })}
            {type === 'doughnut' && (
              <text
                x={centerX}
                y={centerY}
                textAnchor="middle"
                dominantBaseline="middle"
                className="text-lg font-semibold fill-gray-700"
              >
                {total}
              </text>
            )}
          </svg>
          <div className="ml-6 space-y-2">
            {data.map((item, index) => (
              <div key={item.label} className="flex items-center text-sm">
                <div
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: item.color || colors[index % colors.length] }}
                />
                <span className="text-gray-600">{item.label}:</span>
                <span className="ml-1 font-medium">{item.value}</span>
                <span className="ml-1 text-gray-500">
                  ({((item.value / total) * 100).toFixed(1)}%)
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default SimpleChart;
