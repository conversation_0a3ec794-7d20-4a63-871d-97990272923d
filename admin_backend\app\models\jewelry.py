from app import db
from datetime import date, datetime
from sqlalchemy import func

# Enhanced association table for jewelry-diamond with detailed tracking
jewelry_diamonds = db.<PERSON>(
    'jewelry_diamonds',
    db.<PERSON>umn('jewelry_id', db.Integer, db.<PERSON>ey('jewelry_items.id', name='fk_jewelry_diamonds_jewelry_id'), primary_key=True),
    db.<PERSON>n('diamond_id', db.Integer, db.<PERSON>('diamonds.id', name='fk_jewelry_diamonds_diamond_id'), primary_key=True),
    db.Column('quantity', db.Integer, nullable=False, default=1),
    db.Column('setting_type', db.String(50)),  # prong, bezel, pave, channel, etc.
    db.Column('position', db.String(50)),  # center, side, accent, etc.
    db.Column('notes', db.Text),  # Notes for this specific diamond setting
    extend_existing=True
)

class JewelryItem(db.Model):
    """Enhanced jewelry item model for professional jewelry inventory management."""
    __tablename__ = 'jewelry_items'

    # Basic Information
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    design_code = db.Column(db.String(50), nullable=False, unique=True)
    sku = db.Column(db.String(50), unique=True)  # Stock Keeping Unit
    vendor_id = db.Column(db.Integer, db.ForeignKey('vendors.id', name='fk_jewelry_vendor_id'), nullable=False)

    # Product Details
    category = db.Column(db.String(50))  # ring, necklace, earrings, bracelet, etc.
    subcategory = db.Column(db.String(50))  # engagement, wedding, tennis, etc.
    collection = db.Column(db.String(100))  # Collection name
    style = db.Column(db.String(50))  # modern, vintage, classic, etc.
    gender = db.Column(db.String(20))  # men, women, unisex

    # Physical Specifications
    gross_weight = db.Column(db.Float, nullable=False)  # Total weight including stones
    net_weight = db.Column(db.Float)  # Metal weight only
    metal_type = db.Column(db.String(30), nullable=False)  # gold, silver, platinum, etc.
    metal_purity = db.Column(db.String(20))  # 14K, 18K, 22K, 925, PT950, etc.
    metal_color = db.Column(db.String(20))  # yellow, white, rose, etc.

    # Size and Dimensions
    size = db.Column(db.String(20))  # Ring size, chain length, etc.
    length_mm = db.Column(db.Float)  # Length in millimeters
    width_mm = db.Column(db.Float)  # Width in millimeters
    height_mm = db.Column(db.Float)  # Height in millimeters

    # Pricing and Cost
    cost_price = db.Column(db.Float)  # Cost from vendor
    making_charges = db.Column(db.Float)  # Manufacturing/labor charges
    retail_price = db.Column(db.Float)  # Retail selling price
    discount_price = db.Column(db.Float)  # Discounted price
    profit_margin = db.Column(db.Float)  # Profit margin percentage

    # Inventory Management
    quantity = db.Column(db.Integer, default=1)  # Quantity in stock
    minimum_stock = db.Column(db.Integer, default=1)  # Minimum stock level
    location = db.Column(db.String(100))  # Storage location
    barcode = db.Column(db.String(100))  # Barcode for scanning

    # Status and Lifecycle
    status = db.Column(db.String(30), default='in_stock')  # in_stock, sold, reserved, damaged, etc.
    condition = db.Column(db.String(20), default='new')  # new, used, refurbished
    availability = db.Column(db.String(20), default='available')  # available, reserved, sold

    # Dates
    received_date = db.Column(db.Date, default=date.today)
    manufactured_date = db.Column(db.Date)
    last_sold_date = db.Column(db.Date)
    last_maintenance_date = db.Column(db.Date)

    # Certification and Documentation
    certificate_number = db.Column(db.String(100))  # Jewelry certificate number
    hallmark_number = db.Column(db.String(100))  # Hallmark certification
    appraisal_value = db.Column(db.Float)  # Appraised value for insurance
    appraisal_date = db.Column(db.Date)  # Date of appraisal

    # Quality and Features
    finish = db.Column(db.String(50))  # polished, matte, brushed, etc.
    setting_style = db.Column(db.String(50))  # prong, bezel, pave, etc.
    clasp_type = db.Column(db.String(50))  # For necklaces/bracelets
    special_features = db.Column(db.Text)  # Special features or techniques

    # Care and Maintenance
    care_instructions = db.Column(db.Text)  # Care and maintenance instructions
    warranty_period = db.Column(db.Integer)  # Warranty period in months
    warranty_terms = db.Column(db.Text)  # Warranty terms and conditions

    # Marketing and Sales
    description = db.Column(db.Text)  # Detailed product description
    tags = db.Column(db.String(500))  # Comma-separated tags for search
    is_featured = db.Column(db.Boolean, default=False)  # Featured product
    is_bestseller = db.Column(db.Boolean, default=False)  # Bestseller flag
    seo_title = db.Column(db.String(200))  # SEO title for online listings
    seo_description = db.Column(db.Text)  # SEO description

    # Images and Media
    image_path = db.Column(db.String(255))  # Primary image path
    additional_images = db.Column(db.Text)  # JSON array of additional image paths
    video_url = db.Column(db.String(500))  # Product video URL

    # Analytics and Performance
    view_count = db.Column(db.Integer, default=0)  # Number of views
    inquiry_count = db.Column(db.Integer, default=0)  # Number of inquiries
    times_sold = db.Column(db.Integer, default=0)  # Number of times sold
    rating = db.Column(db.Float)  # Average customer rating
    review_count = db.Column(db.Integer, default=0)  # Number of reviews

    # Additional Information
    notes = db.Column(db.Text)  # Internal notes
    custom_fields = db.Column(db.Text)  # JSON for custom fields

    # Tracking and Audit
    created_by = db.Column(db.String(100))  # Who created the record
    updated_by = db.Column(db.String(100))  # Who last updated the record
    created_at = db.Column(db.DateTime, default=func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())

    # Relationships
    vendor = db.relationship('Vendor', backref='jewelry_items')
    diamonds = db.relationship('Diamond', secondary=jewelry_diamonds, backref='jewelry_items')

    # Industry-specific constants
    VALID_STATUSES = [
        'in_stock', 'sold', 'reserved', 'on_hold', 'damaged',
        'under_repair', 'discontinued', 'out_of_stock'
    ]

    CATEGORIES = [
        'Ring', 'Necklace', 'Earrings', 'Bracelet', 'Pendant',
        'Brooch', 'Anklet', 'Cufflinks', 'Tie Pin', 'Watch'
    ]

    METAL_TYPES = ['Gold', 'Silver', 'Platinum', 'Palladium', 'Titanium', 'Stainless Steel']
    METAL_PURITIES = ['14K', '18K', '22K', '24K', '925', 'PT950', 'PT900']
    METAL_COLORS = ['Yellow', 'White', 'Rose', 'Two-Tone', 'Tri-Color']
    CONDITIONS = ['new', 'used', 'refurbished', 'vintage', 'antique']
    AVAILABILITIES = ['available', 'reserved', 'sold', 'on_hold']

    def __init__(self, **kwargs):
        super(JewelryItem, self).__init__(**kwargs)
        if not self.sku:
            self.generate_sku()

    def generate_sku(self):
        """Generate a unique SKU"""
        timestamp = datetime.now().strftime('%y%m%d%H%M')
        category_code = (self.category[:3].upper() if self.category else 'JWL')
        self.sku = f"{category_code}{timestamp}"

    def calculate_profit_margin(self):
        """Calculate profit margin percentage"""
        if self.cost_price and self.retail_price:
            profit = self.retail_price - self.cost_price
            self.profit_margin = (profit / self.retail_price) * 100

    def is_low_stock(self):
        """Check if item is low on stock"""
        return self.quantity <= self.minimum_stock

    def get_total_cost(self):
        """Get total cost including making charges"""
        total = self.cost_price or 0
        if self.making_charges:
            total += self.making_charges
        return total

    def get_effective_price(self):
        """Get effective selling price (discount price if available, else retail price)"""
        return self.discount_price if self.discount_price else self.retail_price

    def update_analytics(self, action):
        """Update analytics counters"""
        if action == 'view':
            self.view_count = (self.view_count or 0) + 1
        elif action == 'inquiry':
            self.inquiry_count = (self.inquiry_count or 0) + 1
        elif action == 'sold':
            self.times_sold = (self.times_sold or 0) + 1
            self.last_sold_date = date.today()

    @classmethod
    def validate_status(cls, status):
        """Validate jewelry status"""
        return status in cls.VALID_STATUSES

    @classmethod
    def validate_category(cls, category):
        """Validate jewelry category"""
        return category in cls.CATEGORIES

    def to_dict(self):
        """Convert jewelry item to dictionary for API responses"""
        return {
            'id': self.id,
            'name': self.name,
            'design_code': self.design_code,
            'sku': self.sku,
            'vendor_id': self.vendor_id,
            'vendor': self.vendor.to_dict() if self.vendor else None,
            'category': self.category,
            'subcategory': self.subcategory,
            'collection': self.collection,
            'style': self.style,
            'gender': self.gender,
            'gross_weight': self.gross_weight,
            'net_weight': self.net_weight,
            'metal_type': self.metal_type,
            'metal_purity': self.metal_purity,
            'metal_color': self.metal_color,
            'size': self.size,
            'length_mm': self.length_mm,
            'width_mm': self.width_mm,
            'height_mm': self.height_mm,
            'cost_price': self.cost_price,
            'making_charges': self.making_charges,
            'retail_price': self.retail_price,
            'discount_price': self.discount_price,
            'profit_margin': self.profit_margin,
            'quantity': self.quantity,
            'minimum_stock': self.minimum_stock,
            'location': self.location,
            'barcode': self.barcode,
            'status': self.status,
            'condition': self.condition,
            'availability': self.availability,
            'received_date': self.received_date.isoformat() if self.received_date else None,
            'manufactured_date': self.manufactured_date.isoformat() if self.manufactured_date else None,
            'last_sold_date': self.last_sold_date.isoformat() if self.last_sold_date else None,
            'last_maintenance_date': self.last_maintenance_date.isoformat() if self.last_maintenance_date else None,
            'certificate_number': self.certificate_number,
            'hallmark_number': self.hallmark_number,
            'appraisal_value': self.appraisal_value,
            'appraisal_date': self.appraisal_date.isoformat() if self.appraisal_date else None,
            'finish': self.finish,
            'setting_style': self.setting_style,
            'clasp_type': self.clasp_type,
            'special_features': self.special_features,
            'care_instructions': self.care_instructions,
            'warranty_period': self.warranty_period,
            'warranty_terms': self.warranty_terms,
            'description': self.description,
            'tags': self.tags,
            'is_featured': self.is_featured,
            'is_bestseller': self.is_bestseller,
            'seo_title': self.seo_title,
            'seo_description': self.seo_description,
            'image_path': self.image_path,
            'additional_images': self.additional_images,
            'video_url': self.video_url,
            'view_count': self.view_count,
            'inquiry_count': self.inquiry_count,
            'times_sold': self.times_sold,
            'rating': self.rating,
            'review_count': self.review_count,
            'notes': self.notes,
            'custom_fields': self.custom_fields,
            'created_by': self.created_by,
            'updated_by': self.updated_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'diamonds': [d.to_dict() for d in self.diamonds] if self.diamonds else [],
            'is_low_stock': self.is_low_stock(),
            'total_cost': self.get_total_cost(),
            'effective_price': self.get_effective_price()
        }

    def __repr__(self):
        return f'<JewelryItem {self.design_code} - {self.name}>'
