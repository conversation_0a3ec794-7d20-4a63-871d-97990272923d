#!/usr/bin/env python3
"""
Test the corrected shapes API endpoint for frontend compatibility
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_shapes_api_fix():
    print("🔧 Testing Frontend Shapes API Fix")
    print("=" * 50)
    
    # Step 1: Login
    print("\n1. Authenticating...")
    login_response = requests.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "Shivam@109"
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        return
    
    token = login_response.json().get('access_token')
    headers = {'Authorization': f'Bearer {token}'}
    print("✅ Authentication successful")
    
    # Step 2: Test old endpoint (should fail)
    print("\n2. Testing old endpoint /api/shapes...")
    old_response = requests.get(f"{BASE_URL}/shapes", headers=headers)
    print(f"   Status: {old_response.status_code}")
    if old_response.status_code == 404:
        print("   ✅ Old endpoint correctly returns 404 (as expected)")
    else:
        print(f"   ⚠️  Unexpected status: {old_response.status_code}")
    
    # Step 3: Test new endpoint (should work)
    print("\n3. Testing new endpoint /api/diamonds/shapes...")
    new_response = requests.get(f"{BASE_URL}/diamonds/shapes", headers=headers)
    print(f"   Status: {new_response.status_code}")
    
    if new_response.status_code == 200:
        shapes_data = new_response.json()
        print(f"   ✅ New endpoint works! Found {len(shapes_data)} shapes")
        print(f"   📋 Sample shapes:")
        for shape in shapes_data[:5]:
            print(f"      - ID: {shape['id']}, Name: {shape['name']}")
    else:
        print(f"   ❌ New endpoint failed: {new_response.text}")
        return
    
    # Step 4: Test shape creation (POST)
    print("\n4. Testing shape creation...")
    test_shape_name = f"TestShape_{int(requests.get('http://worldtimeapi.org/api/timezone/UTC').json()['unixtime']) % 10000}"
    create_response = requests.post(f"{BASE_URL}/diamonds/shapes", 
                                  json={"name": test_shape_name}, 
                                  headers=headers)
    print(f"   Status: {create_response.status_code}")
    
    if create_response.status_code in [200, 201]:
        created_shape = create_response.json()
        print(f"   ✅ Shape creation successful! Created: {created_shape}")
        
        # Step 5: Test shape deletion
        print("\n5. Testing shape deletion...")
        shape_id = created_shape.get('id')
        if shape_id:
            delete_response = requests.delete(f"{BASE_URL}/diamonds/shapes/{shape_id}", headers=headers)
            print(f"   Status: {delete_response.status_code}")
            if delete_response.status_code in [200, 204]:
                print("   ✅ Shape deletion successful!")
            else:
                print(f"   ❌ Shape deletion failed: {delete_response.text}")
        else:
            print("   ⚠️  No shape ID returned, skipping deletion test")
    else:
        print(f"   ❌ Shape creation failed: {create_response.text}")
    
    print("\n🎯 Frontend shapes API fix test complete!")
    print("\n📝 Summary:")
    print("   - Old endpoint /api/shapes: ❌ (404 - as expected)")
    print("   - New endpoint /api/diamonds/shapes: ✅ (200 - working)")
    print("   - Frontend should now work correctly with shapes!")

if __name__ == "__main__":
    test_shapes_api_fix()
