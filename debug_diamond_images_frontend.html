<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Diamond Images Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        #results { margin-top: 20px; padding: 10px; background: #f5f5f5; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🔧 Diamond Images Frontend Debug</h1>
    
    <div class="test-section">
        <h3>1. Test API Endpoints</h3>
        <button onclick="testLogin()">Test Login</button>
        <button onclick="testGetImages()">Test Get Images</button>
        <button onclick="testUploadImage()">Test Upload Image</button>
    </div>
    
    <div class="test-section">
        <h3>2. Test Frontend Integration</h3>
        <button onclick="testModalOpen()">Test Modal Open</button>
        <button onclick="testFileSelection()">Test File Selection</button>
        <input type="file" id="testFileInput" accept="image/*" style="display: none;">
    </div>
    
    <div id="results">
        <h4>Test Results:</h4>
        <div id="output"></div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8000/api';
        let authToken = null;
        
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            output.appendChild(div);
            console.log(message);
        }
        
        async function testLogin() {
            log('🔐 Testing login...', 'info');
            try {
                const response = await fetch(`${BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Shivam@109'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;
                    log('✅ Login successful!', 'success');
                    log(`Token: ${authToken.substring(0, 20)}...`, 'info');
                } else {
                    log(`❌ Login failed: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`❌ Login error: ${error.message}`, 'error');
            }
        }
        
        async function testGetImages() {
            if (!authToken) {
                log('❌ Please login first', 'error');
                return;
            }
            
            log('🖼️ Testing get images...', 'info');
            try {
                const response = await fetch(`${BASE_URL}/diamond-images/diamonds/9/images`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const images = await response.json();
                    log(`✅ Get images successful! Found ${images.length} images`, 'success');
                    log(`Response: ${JSON.stringify(images, null, 2)}`, 'info');
                } else {
                    log(`❌ Get images failed: ${response.status}`, 'error');
                    const errorText = await response.text();
                    log(`Error: ${errorText}`, 'error');
                }
            } catch (error) {
                log(`❌ Get images error: ${error.message}`, 'error');
            }
        }
        
        async function testUploadImage() {
            if (!authToken) {
                log('❌ Please login first', 'error');
                return;
            }
            
            log('⬆️ Testing image upload...', 'info');
            
            // Create a simple test image
            const canvas = document.createElement('canvas');
            canvas.width = 100;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = 'red';
            ctx.fillRect(0, 0, 100, 100);
            
            canvas.toBlob(async (blob) => {
                try {
                    const formData = new FormData();
                    formData.append('file', blob, 'test-image.png');
                    formData.append('image_type', 'main');
                    formData.append('is_primary', 'true');
                    formData.append('alt_text', 'Test image from debug');
                    
                    const response = await fetch(`${BASE_URL}/diamond-images/diamonds/9/images`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        },
                        body: formData
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        log('✅ Upload successful!', 'success');
                        log(`Uploaded image ID: ${result.id}`, 'info');
                        log(`Image URL: ${result.image_url}`, 'info');
                    } else {
                        log(`❌ Upload failed: ${response.status}`, 'error');
                        const errorText = await response.text();
                        log(`Error: ${errorText}`, 'error');
                    }
                } catch (error) {
                    log(`❌ Upload error: ${error.message}`, 'error');
                }
            }, 'image/png');
        }
        
        function testModalOpen() {
            log('🔧 Testing modal functionality...', 'info');
            
            // Simulate modal open
            const modal = document.createElement('div');
            modal.style.position = 'fixed';
            modal.style.top = '50%';
            modal.style.left = '50%';
            modal.style.transform = 'translate(-50%, -50%)';
            modal.style.background = 'white';
            modal.style.padding = '20px';
            modal.style.border = '2px solid #333';
            modal.style.borderRadius = '10px';
            modal.style.zIndex = '1000';
            modal.innerHTML = `
                <h3>Test Upload Modal</h3>
                <input type="file" accept="image/*" id="modalFileInput">
                <br><br>
                <button onclick="this.parentElement.remove()">Close</button>
            `;
            
            document.body.appendChild(modal);
            log('✅ Modal opened successfully!', 'success');
        }
        
        function testFileSelection() {
            log('📁 Testing file selection...', 'info');
            const fileInput = document.getElementById('testFileInput');
            fileInput.style.display = 'block';
            fileInput.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    log(`✅ File selected: ${file.name} (${file.size} bytes)`, 'success');
                    log(`File type: ${file.type}`, 'info');
                } else {
                    log('❌ No file selected', 'error');
                }
                fileInput.style.display = 'none';
            };
            fileInput.click();
        }
        
        // Auto-run login on page load
        window.onload = function() {
            log('🚀 Debug page loaded. Click buttons to test functionality.', 'info');
        };
    </script>
</body>
</html>
