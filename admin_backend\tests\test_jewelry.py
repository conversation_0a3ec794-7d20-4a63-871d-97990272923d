import requests
import pytest
from app import create_app, db
from app.models import JewelryItem, Diamond
from tests.conftest import get_tokens

@pytest.fixture
def app():
    app = create_app()
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    with app.app_context():
        db.create_all()
        yield app
        db.session.remove()
        db.drop_all()

@pytest.fixture
def client(app):
    return app.test_client()

def test_jewelry_crud(base_url, test_user):
    access_token, _ = get_tokens(base_url, test_user)
    headers = {"Authorization": f"Bearer {access_token}"}
    # Create a vendor first
    vendor = {
        "name": "Jewelry Vendor",
        "gst_number": "GSTJ123456",
        "contact_number": "9876543210",
        "address": "456 Jewelry Lane"
    }
    r = requests.post(f"{base_url}/vendors/", json=vendor, headers=headers, timeout=10)
    vendor_id = r.json()["id"]
    # Create jewelry
    jewelry = {
        "name": "Diamond Ring",
        "design_code": "RING001",
        "vendor_id": vendor_id,
        "gross_weight": 5.0,
        "metal_type": "Gold",
        "received_date": "2025-06-21",
        "status": "in_stock",
        "diamonds": []
    }
    r = requests.post(f"{base_url}/jewelry/", json=jewelry, headers=headers, timeout=10)
    assert r.status_code == 201
    jewelry_id = r.json()["id"]
    # Get
    r = requests.get(f"{base_url}/jewelry/{jewelry_id}", headers=headers, timeout=10)
    assert r.status_code == 200
    # Update
    r = requests.put(f"{base_url}/jewelry/{jewelry_id}", json={"status": "sold"}, headers=headers, timeout=10)
    assert r.status_code == 200
    # Delete
    r = requests.delete(f"{base_url}/jewelry/{jewelry_id}", headers=headers, timeout=10)
    assert r.status_code == 200
    # Cleanup vendor
    r = requests.delete(f"{base_url}/vendors/{vendor_id}", headers=headers, timeout=10)

def test_deduct_diamonds(client):
    # Setup test data
    jewelry = JewelryItem(id=1, name='Test Jewelry')
    diamond = Diamond(id=1, quantity=10)
    db.session.add(jewelry)
    db.session.add(diamond)
    db.session.commit()

    # Associate diamond with jewelry
    db.session.execute(
        "INSERT INTO jewelry_diamonds (jewelry_id, diamond_id, quantity) VALUES (1, 1, 5)"
    )
    db.session.commit()

    # Test deduct diamonds endpoint
    response = client.patch('/jewelry/1/deduct-diamonds')
    assert response.status_code == 200
    assert response.json['message'] == 'Diamonds deducted successfully'

    # Verify diamond quantity
    updated_diamond = Diamond.query.get(1)
    assert updated_diamond.quantity == 5

def test_deduct_diamonds_invalid_diamond(client):
    response = client.patch('/jewelry/1/deduct-diamonds')
    assert response.status_code == 400
    assert 'No associated diamonds to deduct.' in response.json['message']
