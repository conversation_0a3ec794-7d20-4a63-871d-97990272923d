import React, { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Plus, Trash2 } from 'lucide-react';
import { api } from '../../lib/api';
import { Jewelry } from '../../types';
import Input from '../../components/ui/Input';
import Select from '../../components/ui/Select';
import Button from '../../components/ui/Button';
import toast from 'react-hot-toast';

interface JewelryFormProps {
  jewelry?: Jewelry;
  onSuccess: () => void;
}

interface JewelryFormData {
  name: string;
  design_code: string;
  vendor_id: string;
  gross_weight: number;
  metal_type: string;
  received_date: string;
  status: 'in_stock' | 'sold';
  diamonds: Array<{
    diamond_id: string;
    quantity: number;
  }>;
}

const JewelryForm: React.FC<JewelryFormProps> = ({ jewelry, onSuccess }) => {
  const isEditing = !!jewelry;
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(jewelry?.image_path || null);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    watch
  } = useForm<JewelryFormData>({
    defaultValues: jewelry ? {
      name: jewelry.name,
      design_code: jewelry.design_code,
      vendor_id: jewelry.vendor_id,
      gross_weight: jewelry.gross_weight,
      metal_type: jewelry.metal_type,
      received_date: jewelry.received_date.split('T')[0],
      status: jewelry.status,
      diamonds: jewelry.diamonds || []
    } : {
      status: 'in_stock',
      received_date: new Date().toISOString().split('T')[0],
      diamonds: [{ diamond_id: '', quantity: 1 }]
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'diamonds'
  });

  const { data: vendors } = useQuery({
    queryKey: ['vendors'],
    queryFn: async () => {
      const response = await api.vendors.list();
      return response?.data || [];
    }
  });

  const { data: diamonds } = useQuery({
    queryKey: ['diamonds'],
    queryFn: async () => {
      const response = await api.diamonds.list();
      return response?.data || [];
    }
  });

  const mutation = useMutation({
    mutationFn: async (data: JewelryFormData) => {
      const payload = {
        ...data,
        vendor_id: Number(data.vendor_id),
        diamonds: data.diamonds
          .filter(d => d.diamond_id && d.quantity > 0)
          .map(d => ({
            diamond_id: Number(d.diamond_id),
            quantity: Number(d.quantity)
          }))
      };
      
      if (isEditing) {
        await api.put(`/jewelry/${jewelry.id}`, payload);
      } else {
        await api.jewelry.create(payload);
      }
    },
    onSuccess: () => {
      toast.success(`Jewelry ${isEditing ? 'updated' : 'created'} successfully`);
      onSuccess();
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || `Failed to ${isEditing ? 'update' : 'create'} jewelry`;
      toast.error(message);
    }
  });

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedImage(file);
      setImagePreview(URL.createObjectURL(file));
    }
  };

  const handleImageUpload = async (jewelryId: number) => {
    if (selectedImage) {
      await api.jewelry.uploadImage(jewelryId, selectedImage);
    }
  };

  const onSubmit = async (data: JewelryFormData) => {
    let newJewelryId: number | undefined;
    if (isEditing) {
      await mutation.mutateAsync(data);
      newJewelryId = jewelry.id;
    } else {
      // Get the response from create to extract the new ID
      const res = await api.jewelry.create({
        ...data,
        vendor_id: Number(data.vendor_id),
        diamonds: data.diamonds
          .filter(d => d.diamond_id && d.quantity > 0)
          .map(d => ({
            diamond_id: Number(d.diamond_id),
            quantity: Number(d.quantity)
          }))
      });
      newJewelryId = res?.data?.id;
    }
    if (selectedImage && newJewelryId) {
      await handleImageUpload(newJewelryId);
    }
    onSuccess();
  };

  const vendorOptions = vendors?.map((vendor: any) => ({
    value: vendor.id,
    label: vendor.name
  })) || [];

  const diamondOptions = Array.isArray(diamonds)
    ? diamonds.map((diamond: any) => ({
        value: diamond.id,
        label: `${diamond.shape} - ${diamond.carat}ct - ${diamond.certificate_no}`
      }))
    : [];

  const metalTypeOptions = [
    { value: 'Gold', label: 'Gold' },
    { value: 'Silver', label: 'Silver' },
    { value: 'Platinum', label: 'Platinum' },
    { value: 'White Gold', label: 'White Gold' },
    { value: 'Rose Gold', label: 'Rose Gold' }
  ];

  const statusOptions = [
    { value: 'in_stock', label: 'In Stock' },
    { value: 'sold', label: 'Sold' }
  ];

  const watchedDiamonds = watch('diamonds');

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label="Jewelry Name"
          type="text"
          required
          {...register('name', { 
            required: 'Jewelry name is required',
            minLength: { value: 2, message: 'Name must be at least 2 characters' }
          })}
          error={errors.name?.message}
        />

        <Input
          label="Design Code"
          type="text"
          required
          {...register('design_code', { required: 'Design code is required' })}
          error={errors.design_code?.message}
        />

        <Select
          label="Vendor"
          options={vendorOptions}
          required
          {...register('vendor_id', { required: 'Vendor is required' })}
          error={errors.vendor_id?.message}
        />

        <Input
          label="Gross Weight (grams)"
          type="number"
          step="0.01"
          required
          {...register('gross_weight', { 
            required: 'Gross weight is required',
            min: { value: 0.01, message: 'Weight must be greater than 0' }
          })}
          error={errors.gross_weight?.message}
        />

        <Select
          label="Metal Type"
          options={metalTypeOptions}
          required
          {...register('metal_type', { required: 'Metal type is required' })}
          error={errors.metal_type?.message}
        />

        <Input
          label="Received Date"
          type="date"
          required
          {...register('received_date', { required: 'Received date is required' })}
          error={errors.received_date?.message}
        />

        <Select
          label="Status"
          options={statusOptions}
          required
          {...register('status', { required: 'Status is required' })}
          error={errors.status?.message}
        />
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Diamonds Used</h3>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => append({ diamond_id: '', quantity: 1 })}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Diamond
          </Button>
        </div>

        {fields.map((field, index) => (
          <div key={field.id} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border border-gray-200 rounded-lg">
            <div className="md:col-span-2">
              <Select
                label="Diamond"
                options={diamondOptions}
                {...register(`diamonds.${index}.diamond_id`)}
                error={errors.diamonds?.[index]?.diamond_id?.message}
              />
            </div>

            <div className="flex items-end space-x-2">
              <Input
                label="Quantity"
                type="number"
                min="1"
                {...register(`diamonds.${index}.quantity`, { 
                  min: { value: 1, message: 'Quantity must be at least 1' }
                })}
                error={errors.diamonds?.[index]?.quantity?.message}
              />
              
              {fields.length > 1 && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => remove(index)}
                  className="mb-1"
                >
                  <Trash2 className="h-4 w-4 text-red-600" />
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="space-y-4">
        <label className="block font-medium">Image</label>
        <input type="file" accept="image/*" onChange={handleImageChange} />
        {imagePreview && (
          <img src={imagePreview} alt="Jewelry Preview" className="max-h-40 mt-2 rounded border" />
        )}
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          type="submit"
          isLoading={mutation.isPending}
        >
          {isEditing ? 'Update Jewelry' : 'Create Jewelry'}
        </Button>
      </div>
    </form>
  );
};

export default JewelryForm;