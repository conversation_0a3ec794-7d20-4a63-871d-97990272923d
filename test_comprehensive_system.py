#!/usr/bin/env python3
"""
Comprehensive test script for the enhanced admin panel system
Tests all enhanced features across diamonds, vendors, and manufacturing
"""

import requests
import json
import sys
from datetime import datetime, date

# Configuration
BASE_URL = "http://localhost:8000/api"

class ComprehensiveSystemTester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.test_results = []
        
    def log_test(self, test_name, success, message=""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if message:
            print(f"   {message}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })
    
    def test_enhanced_diamonds(self):
        """Test enhanced diamond functionality"""
        print("\n🔹 Testing Enhanced Diamond System")
        print("-" * 40)
        
        try:
            # Test diamond listing with new fields
            response = self.session.get(f"{BASE_URL}/diamonds")
            if response.status_code == 200:
                data = response.json()
                diamonds = data.get('data', [])
                
                # Check if enhanced fields are present
                if diamonds:
                    diamond = diamonds[0]
                    enhanced_fields = [
                        'cut_grade', 'polish', 'symmetry', 'fluorescence',
                        'certification_lab', 'cost_price', 'retail_price',
                        'profit_margin', 'is_low_stock'
                    ]
                    
                    present_fields = [field for field in enhanced_fields if field in diamond]
                    self.log_test("Enhanced Diamond Fields", 
                                len(present_fields) > 5, 
                                f"Found {len(present_fields)}/{len(enhanced_fields)} enhanced fields")
                else:
                    self.log_test("Enhanced Diamond Fields", True, "No diamonds to test, but API is working")
            else:
                self.log_test("Enhanced Diamond Fields", False, f"API error: {response.status_code}")
                
        except Exception as e:
            self.log_test("Enhanced Diamond Fields", False, f"Error: {str(e)}")
    
    def test_enhanced_vendors(self):
        """Test enhanced vendor functionality"""
        print("\n🔹 Testing Enhanced Vendor System")
        print("-" * 40)
        
        try:
            # Test vendor listing with new fields
            response = self.session.get(f"{BASE_URL}/vendors")
            if response.status_code == 200:
                vendors = response.json()
                
                # Check if enhanced fields are present
                if vendors:
                    vendor = vendors[0]
                    enhanced_fields = [
                        'company_name', 'vendor_code', 'contact_person',
                        'email', 'business_type', 'credit_limit',
                        'rating', 'status', 'is_verified'
                    ]
                    
                    present_fields = [field for field in enhanced_fields if field in vendor]
                    self.log_test("Enhanced Vendor Fields", 
                                len(present_fields) > 5, 
                                f"Found {len(present_fields)}/{len(enhanced_fields)} enhanced fields")
                else:
                    self.log_test("Enhanced Vendor Fields", True, "No vendors to test, but API is working")
            else:
                self.log_test("Enhanced Vendor Fields", False, f"API error: {response.status_code}")
                
        except Exception as e:
            self.log_test("Enhanced Vendor Fields", False, f"Error: {str(e)}")
    
    def test_enhanced_manufacturing(self):
        """Test enhanced manufacturing functionality"""
        print("\n🔹 Testing Enhanced Manufacturing System")
        print("-" * 40)
        
        try:
            # Test manufacturing listing
            response = self.session.get(f"{BASE_URL}/manufacturing")
            if response.status_code == 200:
                manufacturing_requests = response.json()
                
                # Check if enhanced fields are present
                if manufacturing_requests:
                    request = manufacturing_requests[0]
                    enhanced_fields = [
                        'order_number', 'order_type', 'priority',
                        'progress_percentage', 'quality_check_status',
                        'estimated_cost', 'actual_cost', 'payment_status'
                    ]
                    
                    present_fields = [field for field in enhanced_fields if field in request]
                    self.log_test("Enhanced Manufacturing Fields", 
                                len(present_fields) > 4, 
                                f"Found {len(present_fields)}/{len(enhanced_fields)} enhanced fields")
                else:
                    self.log_test("Enhanced Manufacturing Fields", True, "No manufacturing requests to test, but API is working")
            else:
                self.log_test("Enhanced Manufacturing Fields", False, f"API error: {response.status_code}")
                
        except Exception as e:
            self.log_test("Enhanced Manufacturing Fields", False, f"Error: {str(e)}")
    
    def test_api_endpoints(self):
        """Test all main API endpoints"""
        print("\n🔹 Testing API Endpoints")
        print("-" * 40)
        
        endpoints = [
            ('/diamonds', 'Diamonds API'),
            ('/vendors', 'Vendors API'),
            ('/manufacturing', 'Manufacturing API'),
            ('/jewelry', 'Jewelry API'),
            ('/sales', 'Sales API'),
            ('/dashboard', 'Dashboard API'),
            ('/shapes', 'Shapes API')
        ]
        
        for endpoint, name in endpoints:
            try:
                response = self.session.get(f"{BASE_URL}{endpoint}")
                success = response.status_code in [200, 401]  # 401 is OK (auth required)
                self.log_test(f"{name} Endpoint", success, 
                            f"Status: {response.status_code}")
            except Exception as e:
                self.log_test(f"{name} Endpoint", False, f"Error: {str(e)}")
    
    def test_database_integrity(self):
        """Test database integrity and relationships"""
        print("\n🔹 Testing Database Integrity")
        print("-" * 40)
        
        try:
            # Test if we can fetch related data
            response = self.session.get(f"{BASE_URL}/diamonds")
            if response.status_code == 200:
                data = response.json()
                diamonds = data.get('data', [])
                
                # Check for proper relationships
                has_vendor_relation = any(d.get('vendorName') for d in diamonds)
                has_shape_relation = any(d.get('shape') for d in diamonds)
                
                self.log_test("Diamond Relationships", 
                            has_vendor_relation or has_shape_relation,
                            "Vendor and shape relationships working")
            else:
                self.log_test("Diamond Relationships", False, "Could not test relationships")
                
        except Exception as e:
            self.log_test("Database Integrity", False, f"Error: {str(e)}")
    
    def test_frontend_constants(self):
        """Test if frontend constants files exist and are properly structured"""
        print("\n🔹 Testing Frontend Constants")
        print("-" * 40)
        
        import os
        
        constants_files = [
            'admin_front/src/constants/diamond.ts',
            'admin_front/src/constants/vendor.ts',
            'admin_front/src/constants/manufacturing.ts'
        ]
        
        for file_path in constants_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                        # Check for key exports
                        has_exports = 'export const' in content and 'export const VALID' in content
                        self.log_test(f"Constants File: {os.path.basename(file_path)}", 
                                    has_exports, "File exists with proper exports")
                except Exception as e:
                    self.log_test(f"Constants File: {os.path.basename(file_path)}", 
                                False, f"Error reading file: {str(e)}")
            else:
                self.log_test(f"Constants File: {os.path.basename(file_path)}", 
                            False, "File does not exist")
    
    def run_all_tests(self):
        """Run all comprehensive tests"""
        print("🔍 Starting Comprehensive System Tests")
        print("=" * 50)
        
        # Test API endpoints first
        self.test_api_endpoints()
        
        # Test enhanced features
        self.test_enhanced_diamonds()
        self.test_enhanced_vendors()
        self.test_enhanced_manufacturing()
        
        # Test database integrity
        self.test_database_integrity()
        
        # Test frontend constants
        self.test_frontend_constants()
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 Comprehensive Test Summary")
        print("=" * 50)
        
        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        # Show failed tests
        failed_tests = [result for result in self.test_results if not result['success']]
        if failed_tests:
            print(f"\n❌ Failed Tests ({len(failed_tests)}):")
            for test in failed_tests:
                print(f"  - {test['test']}: {test['message']}")
        
        if passed == total:
            print("\n🎉 All tests passed! System is working correctly.")
        else:
            print(f"\n⚠️  {total - passed} test(s) failed. Please review the issues above.")
        
        return passed == total

if __name__ == "__main__":
    tester = ComprehensiveSystemTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
