#!/usr/bin/env python3
"""
Debug diamond update issue
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def debug_diamond_update():
    print("🔍 Debugging Diamond Update...")
    
    # Login
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Get existing diamond
    print("\n💎 Getting existing diamond...")
    response = session.get(f"{BASE_URL}/diamonds")
    if response.status_code == 200:
        diamonds_data = response.json()
        diamonds = diamonds_data.get('data', [])
        if diamonds:
            diamond = diamonds[0]
            diamond_id = diamond['id']
            print(f"Found diamond ID: {diamond_id}")
            print(f"Current data: {json.dumps(diamond, indent=2)[:500]}...")
            
            # Test minimal update
            print(f"\n🔧 Testing minimal update...")
            minimal_update = {
                "carat": diamond.get('carat', 1.0),
                "color": diamond.get('color', 'D'),
                "clarity": diamond.get('clarity', 'FL'),
                "certificate_no": diamond.get('certificate_no', 'TEST123')
            }
            
            print(f"Sending update: {json.dumps(minimal_update, indent=2)}")
            response = session.put(f"{BASE_URL}/diamonds/{diamond_id}", json=minimal_update)
            print(f"Update status: {response.status_code}")
            
            if response.status_code != 200:
                try:
                    error_data = response.json()
                    print(f"Error response: {json.dumps(error_data, indent=2)}")
                except:
                    print(f"Raw error: {response.text}")
            else:
                print("✅ Update successful!")
        else:
            print("❌ No diamonds found")
    else:
        print(f"❌ Failed to get diamonds: {response.status_code}")

if __name__ == "__main__":
    debug_diamond_update()
