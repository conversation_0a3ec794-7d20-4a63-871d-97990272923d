#!/usr/bin/env python3
"""
Comprehensive test script for enhanced diamond functionality
Tests the new professional diamond grading system and all features
"""

import requests
import json
import sys
from datetime import datetime, date

# Configuration
BASE_URL = "http://localhost:8000/api"
# Try different credentials
TEST_CREDENTIALS = [
    {"email": "<EMAIL>", "password": "test123"},
    {"email": "<EMAIL>", "password": "<PERSON><PERSON>@109"},
    {"email": "<EMAIL>", "password": "password"},
    {"email": "<EMAIL>", "password": "admin123"},
]

class DiamondTester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None
        self.test_results = []
        
    def log_test(self, test_name, success, message=""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if message:
            print(f"   {message}")
        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message
        })
    
    def authenticate(self):
        """Authenticate and get JWT token"""
        for cred in TEST_CREDENTIALS:
            try:
                response = self.session.post(f"{BASE_URL}/auth/login", json=cred)

                if response.status_code == 200:
                    data = response.json()
                    self.token = data.get('access_token')
                    if self.token:
                        self.session.headers.update({
                            'Authorization': f'Bearer {self.token}'
                        })
                        self.log_test("Authentication", True, f"Logged in as {cred['email']}")
                        return True

                print(f"Failed login attempt with {cred['email']}: {response.status_code} - {response.text}")
            except Exception as e:
                print(f"Login error with {cred['email']}: {str(e)}")

        self.log_test("Authentication", False, "All login attempts failed")
        return False
    
    def test_shapes_api(self):
        """Test shapes API"""
        try:
            response = self.session.get(f"{BASE_URL}/shapes")
            if response.status_code == 200:
                shapes = response.json()
                self.log_test("Shapes API", True, f"Retrieved {len(shapes)} shapes")
                return shapes
            else:
                self.log_test("Shapes API", False, f"Failed to get shapes: {response.text}")
                return []
        except Exception as e:
            self.log_test("Shapes API", False, f"Error: {str(e)}")
            return []
    
    def test_vendors_api(self):
        """Test vendors API"""
        try:
            response = self.session.get(f"{BASE_URL}/vendors")
            if response.status_code == 200:
                vendors = response.json()
                self.log_test("Vendors API", True, f"Retrieved {len(vendors)} vendors")
                return vendors
            else:
                self.log_test("Vendors API", False, f"Failed to get vendors: {response.text}")
                return []
        except Exception as e:
            self.log_test("Vendors API", False, f"Error: {str(e)}")
            return []
    
    def test_create_enhanced_diamond(self, shapes):
        """Test creating a diamond with enhanced fields"""
        if not shapes:
            self.log_test("Create Enhanced Diamond", False, "No shapes available")
            return None
            
        diamond_data = {
            "shape_id": shapes[0]['id'],
            "carat": 1.25,
            "color": "G",
            "clarity": "VS1",
            "cut_grade": "Excellent",
            "polish": "Excellent",
            "symmetry": "Very Good",
            "fluorescence": "Faint",
            "fluorescence_color": "Blue",
            "length_mm": 6.85,
            "width_mm": 6.82,
            "depth_mm": 4.15,
            "depth_percent": 60.8,
            "table_percent": 58.0,
            "girdle": "Medium",
            "culet": "None",
            "certificate_no": f"GIA-TEST-{datetime.now().strftime('%Y%m%d%H%M%S')}",
            "certification_lab": "GIA",
            "certificate_date": "2024-01-15",
            "certificate_url": "https://www.gia.edu/report-check",
            "cost_price": 5000.00,
            "retail_price": 8500.00,
            "market_value": 7800.00,
            "quantity": 1,
            "reserved_quantity": 0,
            "minimum_stock": 1,
            "status": "in_stock",
            "location": "Vault A, Shelf 3",
            "notes": "Premium quality diamond with excellent characteristics",
            "purchase_date": "2024-01-10"
        }
        
        try:
            response = self.session.post(f"{BASE_URL}/diamonds", json=diamond_data)
            if response.status_code == 201:
                diamond = response.json()
                self.log_test("Create Enhanced Diamond", True, f"Created diamond ID: {diamond.get('id')}")
                return diamond
            else:
                self.log_test("Create Enhanced Diamond", False, f"Failed: {response.text}")
                return None
        except Exception as e:
            self.log_test("Create Enhanced Diamond", False, f"Error: {str(e)}")
            return None
    
    def test_diamond_validation(self, shapes):
        """Test diamond validation with invalid data"""
        if not shapes:
            return
            
        # Test invalid color
        invalid_data = {
            "shape_id": shapes[0]['id'],
            "carat": 1.0,
            "color": "INVALID",  # Invalid color
            "clarity": "VS1",
            "certificate_no": "TEST-INVALID-COLOR",
            "quantity": 1,
            "purchase_date": "2024-01-10"
        }
        
        try:
            response = self.session.post(f"{BASE_URL}/diamonds", json=invalid_data)
            if response.status_code == 400:
                self.log_test("Diamond Validation (Invalid Color)", True, "Correctly rejected invalid color")
            else:
                self.log_test("Diamond Validation (Invalid Color)", False, "Should have rejected invalid color")
        except Exception as e:
            self.log_test("Diamond Validation (Invalid Color)", False, f"Error: {str(e)}")
        
        # Test invalid carat
        invalid_data["color"] = "G"
        invalid_data["carat"] = -1.0  # Invalid carat
        invalid_data["certificate_no"] = "TEST-INVALID-CARAT"
        
        try:
            response = self.session.post(f"{BASE_URL}/diamonds", json=invalid_data)
            if response.status_code == 400:
                self.log_test("Diamond Validation (Invalid Carat)", True, "Correctly rejected invalid carat")
            else:
                self.log_test("Diamond Validation (Invalid Carat)", False, "Should have rejected invalid carat")
        except Exception as e:
            self.log_test("Diamond Validation (Invalid Carat)", False, f"Error: {str(e)}")
    
    def test_diamond_search_and_filter(self):
        """Test diamond search and filtering"""
        try:
            # Test basic listing
            response = self.session.get(f"{BASE_URL}/diamonds")
            if response.status_code == 200:
                data = response.json()
                diamonds = data.get('data', [])
                self.log_test("Diamond Listing", True, f"Retrieved {len(diamonds)} diamonds")
                
                # Test filtering by color
                response = self.session.get(f"{BASE_URL}/diamonds?color=G")
                if response.status_code == 200:
                    filtered_data = response.json()
                    filtered_diamonds = filtered_data.get('data', [])
                    self.log_test("Diamond Filtering (Color)", True, f"Filtered to {len(filtered_diamonds)} diamonds")
                else:
                    self.log_test("Diamond Filtering (Color)", False, f"Filter failed: {response.text}")
            else:
                self.log_test("Diamond Listing", False, f"Failed: {response.text}")
        except Exception as e:
            self.log_test("Diamond Search and Filter", False, f"Error: {str(e)}")
    
    def test_diamond_reservation(self, diamond_id):
        """Test diamond reservation functionality"""
        if not diamond_id:
            return
            
        try:
            # Test deducting stock (simulating reservation)
            response = self.session.patch(f"{BASE_URL}/diamonds/{diamond_id}/deduct", json={
                "quantity": 1
            })
            if response.status_code == 200:
                self.log_test("Diamond Stock Deduction", True, "Successfully deducted stock")
            else:
                self.log_test("Diamond Stock Deduction", False, f"Failed: {response.text}")
        except Exception as e:
            self.log_test("Diamond Stock Deduction", False, f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🔍 Starting Enhanced Diamond System Tests")
        print("=" * 50)
        
        # Authenticate
        if not self.authenticate():
            print("❌ Authentication failed. Cannot proceed with tests.")
            return
        
        # Test APIs
        shapes = self.test_shapes_api()
        vendors = self.test_vendors_api()
        
        # Test diamond creation with enhanced fields
        diamond = self.test_create_enhanced_diamond(shapes)
        
        # Test validation
        self.test_diamond_validation(shapes)
        
        # Test search and filtering
        self.test_diamond_search_and_filter()
        
        # Test reservation if diamond was created
        if diamond:
            self.test_diamond_reservation(diamond.get('id'))
        
        # Print summary
        print("\n" + "=" * 50)
        print("📊 Test Summary")
        print("=" * 50)
        
        passed = sum(1 for result in self.test_results if result['success'])
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 All tests passed! Enhanced diamond system is working correctly.")
        else:
            print(f"\n⚠️  {total - passed} test(s) failed. Please review the issues above.")
        
        return passed == total

if __name__ == "__main__":
    tester = DiamondTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
