<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Invoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }
        .container {
            width: 90%;
            margin: auto;
            padding: 20px;
        }
        .header {
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
        }
        .section {
            margin-top: 20px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .table th, .table td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #f4f4f4;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #888;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">Invoice #{{ sale.invoice_no }}</div>
        <div class="section">
            <strong>Customer:</strong> {{ sale.customer_name }}<br>
            <strong>Date:</strong> {{ sale.sale_date }}<br>
            <strong>Status:</strong> {{ sale.payment_status }}<br>
        </div>
        <div class="section">
            <table class="table">
                <thead>
                    <tr>
                        <th>Jewelry Item</th>
                        <th>Design Code</th>
                        <th>Weight</th>
                        <th>Metal</th>
                        <th>Image</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{ jewelry.name }}</td>
                        <td>{{ jewelry.design_code }}</td>
                        <td>{{ jewelry.gross_weight }}</td>
                        <td>{{ jewelry.metal_type }}</td>
                        <td>{% if jewelry.image_path %}<img src="{{ jewelry.image_path }}" width="80" style="max-height: 80px; object-fit: contain;">{% endif %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="section">
            <strong>Total Amount:</strong> {{ sale.total_amount }}
        </div>
        <div class="footer">
            Thank you for your business!
        </div>
    </div>
</body>
</html>
