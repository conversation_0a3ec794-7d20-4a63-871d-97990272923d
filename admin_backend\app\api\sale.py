from flask_restx import Namespace, Resource, fields
from flask import request, render_template, make_response, current_app
from app.models.sale import Sale
from app.models.jewelry import JewelryItem
from app import db
from app.utils.decorators import token_required
from datetime import datetime
import io
from xhtml2pdf import pisa
from sqlalchemy.exc import IntegrityError

sale_ns = Namespace('sales', description='Sales and invoice management', path='/sales')

sale_model = sale_ns.model('Sale', {
    'id': fields.Integer(readOnly=True),
    'invoice_no': fields.String(required=True),
    'order_number': fields.String,
    'customer_name': fields.String(required=True),
    'customer_email': fields.String,
    'customer_phone': fields.String,
    'customer_address': fields.String,
    'customer_city': fields.String,
    'customer_state': fields.String,
    'customer_postal_code': fields.String,
    'customer_country': fields.String,
    'sale_date': fields.String,
    'sale_time': fields.String,
    'sale_type': fields.String,
    'sales_channel': fields.String,
    'subtotal': fields.Float,
    'discount_percentage': fields.Float,
    'discount_amount': fields.Float,
    'tax_percentage': fields.Float,
    'tax_amount': fields.Float,
    'shipping_charges': fields.Float,
    'other_charges': fields.Float,
    'total_amount': fields.Float(required=True),
    'payment_status': fields.String,
    'payment_method': fields.String,
    'payment_reference': fields.String,
    'notes': fields.String,
    'created_at': fields.String(readOnly=True),
    'updated_at': fields.String(readOnly=True)
})

create_sale_model = sale_ns.model('CreateSale', {
    'invoice_no': fields.String(required=False),  # Auto-generate if not provided
    'customer_name': fields.String(required=True),
    'customer_email': fields.String,
    'customer_phone': fields.String,
    'customer_address': fields.String,
    'customer_city': fields.String,
    'customer_state': fields.String,
    'customer_postal_code': fields.String,
    'customer_country': fields.String,
    'sale_date': fields.String,
    'sale_type': fields.String,
    'sales_channel': fields.String,
    'subtotal': fields.Float,
    'discount_amount': fields.Float,
    'tax_percentage': fields.Float,
    'tax_amount': fields.Float,
    'total_amount': fields.Float(required=True),
    'payment_status': fields.String,
    'payment_method': fields.String,
    'jewelry_id': fields.Integer(required=True),
    'quantity': fields.Integer,
    'unit_price': fields.Float,
    'notes': fields.String
})

error_model = sale_ns.model('SaleError', {
    'status': fields.String(description='Error status'),
    'message': fields.String(description='Error message'),
    'status_code': fields.Integer(description='HTTP status code')
})

def sale_to_dict(s):
    """Convert sale to dictionary with safe field access."""
    try:
        return {
            'id': s.id,
            'invoice_no': getattr(s, 'invoice_no', None),
            'order_number': getattr(s, 'order_number', None),
            'customer_name': getattr(s, 'customer_name', None),
            'customer_email': getattr(s, 'customer_email', None),
            'customer_phone': getattr(s, 'customer_phone', None),
            'customer_address': getattr(s, 'customer_address', None),
            'customer_city': getattr(s, 'customer_city', None),
            'customer_state': getattr(s, 'customer_state', None),
            'customer_postal_code': getattr(s, 'customer_postal_code', None),
            'customer_country': getattr(s, 'customer_country', None),
            'sale_date': s.sale_date.isoformat() if getattr(s, 'sale_date', None) else None,
            'sale_type': getattr(s, 'sale_type', None),
            'sales_channel': getattr(s, 'sales_channel', None),
            'subtotal': getattr(s, 'subtotal', None),
            'discount_amount': getattr(s, 'discount_amount', None),
            'tax_percentage': getattr(s, 'tax_percentage', None),
            'tax_amount': getattr(s, 'tax_amount', None),
            'total_amount': getattr(s, 'total_amount', None),
            'payment_status': getattr(s, 'payment_status', None),
            'payment_method': getattr(s, 'payment_method', None),
            'notes': getattr(s, 'notes', None),
            'created_at': s.created_at.isoformat() if getattr(s, 'created_at', None) else None,
            'updated_at': s.updated_at.isoformat() if getattr(s, 'updated_at', None) else None
        }
    except Exception as e:
        # Fallback to basic fields only
        return {
            'id': getattr(s, 'id', None),
            'customer_name': getattr(s, 'customer_name', None),
            'total_amount': getattr(s, 'total_amount', None),
            'payment_status': getattr(s, 'payment_status', None),
            'error': f'Error serializing sale: {str(e)}'
        }

@sale_ns.route('/')
class SaleList(Resource):
    @sale_ns.doc('list_sales')
    @sale_ns.response(200, 'List of sales', [sale_model])
    @sale_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """List all sales (optionally filter by customer_name/payment_status/date range)"""
        query = Sale.query
        for field in ['customer_name', 'payment_status']:
            value = request.args.get(field)
            if value:
                query = query.filter(getattr(Sale, field) == value)
        start = request.args.get('start_date')
        end = request.args.get('end_date')
        if start:
            query = query.filter(Sale.sale_date >= datetime.strptime(start, '%Y-%m-%d').date())
        if end:
            query = query.filter(Sale.sale_date <= datetime.strptime(end, '%Y-%m-%d').date())
        
        # Pagination
        page = request.args.get('page', default=1, type=int)
        limit = request.args.get('limit', default=20, type=int)
        pagination = query.paginate(page=page, per_page=limit, error_out=False)
        sales = pagination.items
        return {
            'data': [sale_to_dict(s) for s in sales],
            'total': pagination.total,
            'page': page,
            'limit': limit
        }, 200

    @sale_ns.doc('create_sale')
    @sale_ns.expect(create_sale_model)
    @sale_ns.marshal_with(sale_model, code=201)
    @sale_ns.response(400, 'Validation error', error_model)
    @sale_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def post(self):
        """Create a new sale"""
        data = request.get_json()
        try:
            jewelry = JewelryItem.query.get(data['jewelry_id'])
            if not jewelry or jewelry.status == 'sold':
                sale_ns.abort(400, 'Jewelry not available')

            # Generate invoice number if not provided
            if not data.get('invoice_no'):
                import time
                data['invoice_no'] = f"INV{int(time.time())}"

            # Create sale with enhanced fields
            s = Sale(
                invoice_no=data['invoice_no'],
                customer_name=data['customer_name'],
                customer_email=data.get('customer_email'),
                customer_phone=data.get('customer_phone'),
                customer_address=data.get('customer_address'),
                customer_city=data.get('customer_city'),
                customer_state=data.get('customer_state'),
                customer_postal_code=data.get('customer_postal_code'),
                customer_country=data.get('customer_country', 'India'),
                sale_date=datetime.strptime(data['sale_date'], '%Y-%m-%d').date() if 'sale_date' in data else None,
                sale_type=data.get('sale_type', 'retail'),
                sales_channel=data.get('sales_channel'),
                subtotal=data.get('subtotal', data['total_amount']),
                discount_amount=data.get('discount_amount', 0.0),
                tax_percentage=data.get('tax_percentage', 0.0),
                tax_amount=data.get('tax_amount', 0.0),
                total_amount=data['total_amount'],
                payment_status=data.get('payment_status', 'unpaid'),
                payment_method=data.get('payment_method'),
                notes=data.get('notes')
            )

            # Mark jewelry as sold
            jewelry.status = 'sold'

            db.session.add(s)
            db.session.commit()
            return sale_to_dict(s), 201

        except IntegrityError:
            db.session.rollback()
            sale_ns.abort(409, "Duplicate invoice number")
        except Exception as e:
            db.session.rollback()
            sale_ns.abort(400, f'Failed to create sale: {str(e)}')

@sale_ns.route('/<int:sale_id>')
@sale_ns.param('sale_id', 'The sale identifier')
class SaleResource(Resource):
    @sale_ns.doc('get_sale')
    @sale_ns.marshal_with(sale_model)
    @token_required
    def get(self, sale_id):
        """Get a sale by ID"""
        s = Sale.query.get_or_404(sale_id)
        return sale_to_dict(s)

    @sale_ns.doc('update_sale')
    @sale_ns.expect(create_sale_model)
    @sale_ns.marshal_with(sale_model)
    @token_required
    def put(self, sale_id):
        """Update a sale"""
        s = Sale.query.get_or_404(sale_id)
        data = request.get_json()

        try:
            # Update allowed fields
            allowed_fields = [
                'customer_name', 'customer_email', 'customer_phone', 'customer_address',
                'customer_city', 'customer_state', 'customer_postal_code', 'customer_country',
                'sale_type', 'sales_channel', 'subtotal', 'discount_amount', 'tax_percentage',
                'tax_amount', 'total_amount', 'payment_status', 'payment_method', 'notes'
            ]

            for field in allowed_fields:
                if field in data:
                    setattr(s, field, data[field])

            # Handle date fields
            if 'sale_date' in data:
                s.sale_date = datetime.strptime(data['sale_date'], '%Y-%m-%d').date()

            db.session.commit()
            return sale_to_dict(s), 200

        except Exception as e:
            db.session.rollback()
            sale_ns.abort(400, f'Failed to update sale: {str(e)}')

    @sale_ns.doc('delete_sale')
    @token_required
    def delete(self, sale_id):
        """Delete a sale"""
        s = Sale.query.get_or_404(sale_id)

        try:
            # Return jewelry to available status if it was marked as sold
            if hasattr(s, 'jewelry_item') and s.jewelry_item:
                s.jewelry_item.status = 'in_stock'

            db.session.delete(s)
            db.session.commit()
            return {'message': 'Sale deleted successfully'}, 200

        except Exception as e:
            db.session.rollback()
            sale_ns.abort(400, f'Failed to delete sale: {str(e)}')

@sale_ns.route('/<int:sale_id>/mark-paid')
@sale_ns.param('sale_id', 'The sale identifier')
class SaleMarkPaid(Resource):
    @sale_ns.doc('mark_sale_paid')
    @sale_ns.marshal_with(sale_model)
    @token_required
    def patch(self, sale_id):
        """Mark a sale as paid"""
        s = Sale.query.get_or_404(sale_id)
        s.payment_status = 'paid'
        db.session.commit()
        return sale_to_dict(s)

@sale_ns.route('/invoice/<int:sale_id>')
@sale_ns.param('sale_id', 'The sale identifier')
class SaleInvoice(Resource):
    @sale_ns.doc('download_invoice')
    @token_required
    def get(self, sale_id):
        """Download invoice as PDF for a sale"""
        s = Sale.query.get_or_404(sale_id)
        jewelry = JewelryItem.query.get(s.jewelry_id)
        html = render_template('invoice_template.html', sale=s, jewelry=jewelry)
        try:
            pdf = io.BytesIO()
            result = pisa.CreatePDF(io.StringIO(html), dest=pdf)
            if result.err:
                current_app.logger.error(f"PDF generation failed: {result.err}")
                sale_ns.abort(500, "Failed to generate PDF")
            pdf.seek(0)
            response = make_response(pdf.read())
            response.headers['Content-Type'] = 'application/pdf'
            response.headers['Content-Disposition'] = f'attachment; filename=invoice_{s.invoice_no}.pdf'
            return response
        except (IOError, ValueError) as pdf_error:
            current_app.logger.error(f"PDF generation failed: {pdf_error}")
            sale_ns.abort(500, "Failed to generate PDF")
        except Exception as general_error:
            current_app.logger.error(f"Unexpected error during PDF generation: {str(general_error)}")
            sale_ns.abort(500, "An error occurred while generating the PDF")
