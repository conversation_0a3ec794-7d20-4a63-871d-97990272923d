// Diamond Industry Standards and Constants

export const DIAMOND_COLORS = [
  { value: 'D', label: 'D (Colorless)' },
  { value: 'E', label: 'E (Colorless)' },
  { value: 'F', label: 'F (Colorless)' },
  { value: 'G', label: 'G (Near Colorless)' },
  { value: 'H', label: 'H (Near Colorless)' },
  { value: 'I', label: 'I (Near Colorless)' },
  { value: 'J', label: 'J (Near Colorless)' },
  { value: 'K', label: 'K (Faint Yellow)' },
  { value: 'L', label: 'L (Faint Yellow)' },
  { value: 'M', label: 'M (Faint Yellow)' },
  { value: 'N', label: 'N (Very Light Yellow)' },
  { value: 'O', label: 'O (Very Light Yellow)' },
  { value: 'P', label: 'P (Very Light Yellow)' },
  { value: 'Q', label: 'Q (Light Yellow)' },
  { value: 'R', label: 'R (Light Yellow)' },
  { value: 'S', label: 'S (Light Yellow)' },
  { value: 'T', label: 'T (Light Yellow)' },
  { value: 'U', label: 'U (Light Yellow)' },
  { value: 'V', label: 'V (Light Yellow)' },
  { value: 'W', label: 'W (Light Yellow)' },
  { value: 'X', label: 'X (Light Yellow)' },
  { value: 'Y', label: 'Y (Light Yellow)' },
  { value: 'Z', label: 'Z (Light Yellow)' }
];

export const DIAMOND_CLARITIES = [
  { value: 'FL', label: 'FL (Flawless)' },
  { value: 'IF', label: 'IF (Internally Flawless)' },
  { value: 'VVS1', label: 'VVS1 (Very Very Slightly Included)' },
  { value: 'VVS2', label: 'VVS2 (Very Very Slightly Included)' },
  { value: 'VS1', label: 'VS1 (Very Slightly Included)' },
  { value: 'VS2', label: 'VS2 (Very Slightly Included)' },
  { value: 'SI1', label: 'SI1 (Slightly Included)' },
  { value: 'SI2', label: 'SI2 (Slightly Included)' },
  { value: 'SI3', label: 'SI3 (Slightly Included)' },
  { value: 'I1', label: 'I1 (Included)' },
  { value: 'I2', label: 'I2 (Included)' },
  { value: 'I3', label: 'I3 (Included)' }
];

export const CUT_GRADES = [
  { value: 'Excellent', label: 'Excellent' },
  { value: 'Very Good', label: 'Very Good' },
  { value: 'Good', label: 'Good' },
  { value: 'Fair', label: 'Fair' },
  { value: 'Poor', label: 'Poor' }
];

export const POLISH_SYMMETRY_GRADES = [
  { value: 'Excellent', label: 'Excellent' },
  { value: 'Very Good', label: 'Very Good' },
  { value: 'Good', label: 'Good' },
  { value: 'Fair', label: 'Fair' },
  { value: 'Poor', label: 'Poor' }
];

export const FLUORESCENCE_INTENSITIES = [
  { value: 'None', label: 'None' },
  { value: 'Faint', label: 'Faint' },
  { value: 'Medium', label: 'Medium' },
  { value: 'Strong', label: 'Strong' },
  { value: 'Very Strong', label: 'Very Strong' }
];

export const FLUORESCENCE_COLORS = [
  { value: 'Blue', label: 'Blue' },
  { value: 'Yellow', label: 'Yellow' },
  { value: 'White', label: 'White' },
  { value: 'Green', label: 'Green' },
  { value: 'Orange', label: 'Orange' },
  { value: 'Red', label: 'Red' }
];

export const DIAMOND_STATUSES = [
  { value: 'in_stock', label: 'In Stock' },
  { value: 'reserved', label: 'Reserved' },
  { value: 'sold', label: 'Sold' },
  { value: 'manufacturing', label: 'In Manufacturing' },
  { value: 'damaged', label: 'Damaged' },
  { value: 'lost', label: 'Lost' }
];

export const CERTIFICATION_LABS = [
  { value: 'GIA', label: 'GIA (Gemological Institute of America)' },
  { value: 'AGS', label: 'AGS (American Gem Society)' },
  { value: 'EGL', label: 'EGL (European Gemological Laboratory)' },
  { value: 'GCAL', label: 'GCAL (Gem Certification & Assurance Lab)' },
  { value: 'SSEF', label: 'SSEF (Swiss Gemmological Institute)' },
  { value: 'Gübelin', label: 'Gübelin Gem Lab' },
  { value: 'AIGS', label: 'AIGS (Asian Institute of Gemological Sciences)' },
  { value: 'GRS', label: 'GRS (Gem Research Swisslab)' },
  { value: 'Lotus', label: 'Lotus Gemology' }
];

export const CULET_SIZES = [
  { value: 'None', label: 'None' },
  { value: 'Very Small', label: 'Very Small' },
  { value: 'Small', label: 'Small' },
  { value: 'Medium', label: 'Medium' },
  { value: 'Large', label: 'Large' },
  { value: 'Very Large', label: 'Very Large' }
];

export const GIRDLE_DESCRIPTIONS = [
  { value: 'Extremely Thin', label: 'Extremely Thin' },
  { value: 'Very Thin', label: 'Very Thin' },
  { value: 'Thin', label: 'Thin' },
  { value: 'Medium', label: 'Medium' },
  { value: 'Slightly Thick', label: 'Slightly Thick' },
  { value: 'Thick', label: 'Thick' },
  { value: 'Very Thick', label: 'Very Thick' },
  { value: 'Extremely Thick', label: 'Extremely Thick' }
];

// Validation ranges
export const VALIDATION_RANGES = {
  carat: { min: 0.01, max: 100 },
  length_mm: { min: 0.1, max: 50 },
  width_mm: { min: 0.1, max: 50 },
  depth_mm: { min: 0.1, max: 50 },
  depth_percent: { min: 1, max: 100 },
  table_percent: { min: 1, max: 100 },
  cost_price: { min: 0, max: 10000000 },
  retail_price: { min: 0, max: 10000000 },
  market_value: { min: 0, max: 10000000 }
};

// Helper functions
export const getColorGrade = (color: string) => {
  const colorOption = DIAMOND_COLORS.find(c => c.value === color);
  return colorOption ? colorOption.label : color;
};

export const getClarityGrade = (clarity: string) => {
  const clarityOption = DIAMOND_CLARITIES.find(c => c.value === clarity);
  return clarityOption ? clarityOption.label : clarity;
};

export const getStatusLabel = (status: string) => {
  const statusOption = DIAMOND_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.label : status;
};

export const getCertificationLabLabel = (lab: string) => {
  const labOption = CERTIFICATION_LABS.find(l => l.value === lab);
  return labOption ? labOption.label : lab;
};
