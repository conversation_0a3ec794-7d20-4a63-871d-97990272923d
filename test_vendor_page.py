#!/usr/bin/env python3
"""
Test vendor page functionality
"""

import requests
import time

BASE_URL = "http://localhost:8000/api"
LOGIN_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

def test_vendor_functionality():
    print("🏢 VENDOR PAGE FUNCTIONALITY TEST")
    print("=" * 40)
    
    # Authenticate
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json=LOGIN_CREDENTIALS)
    if response.status_code != 200:
        print("❌ Authentication failed")
        return False
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Test GET vendors
    print("\n1. Testing GET vendors...")
    response = session.get(f"{BASE_URL}/vendors")
    if response.status_code == 200:
        vendors = response.json()
        print(f"✅ GET vendors: Found {len(vendors)} vendors")
    else:
        print(f"❌ GET vendors failed: {response.status_code}")
        return False
    
    # Test CREATE vendor
    print("\n2. Testing CREATE vendor...")
    vendor_data = {
        "name": f"Test Vendor {int(time.time())}",
        "company_name": "Test Company Ltd",
        "gst_number": f"22AAAAA{int(time.time()) % 10000:04d}A1Z5",
        "contact_number": "+91-9876543210",
        "email": f"test{int(time.time())}@vendor.com",
        "address": "123 Test Street, Test City",
        "city": "Mumbai",
        "state": "Maharashtra",
        "country": "India",
        "postal_code": "400001",
        "business_type": "Manufacturer",
        "credit_limit": 100000,
        "payment_terms": "30 days"
    }
    
    response = session.post(f"{BASE_URL}/vendors", json=vendor_data)
    if response.status_code in [200, 201]:
        vendor = response.json()
        vendor_id = vendor.get('id')
        print(f"✅ CREATE vendor: ID {vendor_id}")
        
        # Check enhanced fields
        enhanced_fields = ['business_type', 'credit_limit', 'payment_terms', 'city', 'state']
        found_fields = [f for f in enhanced_fields if vendor.get(f) is not None]
        print(f"✅ Enhanced fields: {found_fields}")
        
    else:
        print(f"❌ CREATE vendor failed: {response.status_code}")
        print(f"Error: {response.text[:200]}")
        return False
    
    # Test READ vendor
    print("\n3. Testing READ vendor...")
    response = session.get(f"{BASE_URL}/vendors/{vendor_id}")
    if response.status_code == 200:
        print(f"✅ READ vendor: Retrieved successfully")
    else:
        print(f"❌ READ vendor failed: {response.status_code}")
    
    # Test UPDATE vendor
    print("\n4. Testing UPDATE vendor...")
    update_data = {
        "name": f"Updated Vendor {int(time.time())}",
        "credit_limit": 150000,
        "payment_terms": "45 days",
        "notes": "Updated vendor for testing"
    }
    
    response = session.put(f"{BASE_URL}/vendors/{vendor_id}", json=update_data)
    if response.status_code == 200:
        updated_vendor = response.json()
        print(f"✅ UPDATE vendor: Updated successfully")
        print(f"   New credit limit: {updated_vendor.get('credit_limit')}")
    else:
        print(f"❌ UPDATE vendor failed: {response.status_code}")
    
    # Test vendor validation
    print("\n5. Testing vendor validation...")
    
    # Test invalid GST
    invalid_vendor = {
        "name": "Invalid Vendor",
        "gst_number": "INVALID_GST",
        "contact_number": "+91-9876543210",
        "address": "Test Address"
    }
    
    response = session.post(f"{BASE_URL}/vendors", json=invalid_vendor)
    if response.status_code in [400, 422]:
        print(f"✅ GST validation: Correctly rejected invalid GST")
    else:
        print(f"❌ GST validation: Should have rejected invalid GST (got {response.status_code})")
    
    # Test duplicate GST
    duplicate_vendor = vendor_data.copy()
    duplicate_vendor['name'] = "Duplicate Vendor"
    
    response = session.post(f"{BASE_URL}/vendors", json=duplicate_vendor)
    if response.status_code == 409:
        print(f"✅ Duplicate GST: Correctly rejected duplicate GST")
    else:
        print(f"❌ Duplicate GST: Should have rejected duplicate (got {response.status_code})")
    
    # Test vendor filtering/search
    print("\n6. Testing vendor search...")
    search_params = {"search": "Test"}
    response = session.get(f"{BASE_URL}/vendors", params=search_params)
    if response.status_code == 200:
        search_results = response.json()
        print(f"✅ Search: Found {len(search_results)} vendors")
    else:
        print(f"❌ Search failed: {response.status_code}")
    
    # Test DELETE vendor
    print("\n7. Testing DELETE vendor...")
    response = session.delete(f"{BASE_URL}/vendors/{vendor_id}")
    if response.status_code in [200, 204]:
        print(f"✅ DELETE vendor: Deleted successfully")
    else:
        print(f"❌ DELETE vendor failed: {response.status_code}")
    
    print("\n🎯 Vendor functionality test complete!")
    return True

if __name__ == "__main__":
    success = test_vendor_functionality()
    if success:
        print("✅ All vendor functionality is working correctly!")
    else:
        print("❌ Some vendor functionality needs attention.")
