from app import db
from datetime import date, datetime
from sqlalchemy import func

# Association table for sales-jewelry items (for multiple items per sale)
sale_items = db.Table(
    'sale_items',
    db.Column('sale_id', db.Integer, db.<PERSON>ey('sales.id', name='fk_sale_items_sale_id'), primary_key=True),
    db.<PERSON>umn('jewelry_id', db.Integer, db.<PERSON>('jewelry_items.id', name='fk_sale_items_jewelry_id'), primary_key=True),
    db.Column('quantity', db.Integer, nullable=False, default=1),
    db.Column('unit_price', db.Float, nullable=False),  # Price per unit at time of sale
    db.Column('discount_amount', db.Float, default=0.0),  # Discount on this item
    db.Column('tax_amount', db.Float, default=0.0),  # Tax on this item
    db.Column('total_amount', db.Float, nullable=False),  # Total for this line item
    extend_existing=True
)

class Sale(db.Model):
    """Enhanced sales model for professional jewelry sales management."""
    __tablename__ = 'sales'

    # Basic Information
    id = db.Column(db.Integer, primary_key=True)
    invoice_no = db.Column(db.String(50), unique=True, nullable=False)
    order_number = db.Column(db.String(50), unique=True)  # Internal order number

    # Customer Information
    customer_name = db.Column(db.String(100), nullable=False)
    customer_email = db.Column(db.String(100))
    customer_phone = db.Column(db.String(20))
    customer_address = db.Column(db.Text)
    customer_city = db.Column(db.String(50))
    customer_state = db.Column(db.String(50))
    customer_postal_code = db.Column(db.String(20))
    customer_country = db.Column(db.String(50), default='India')

    # Sale Details
    sale_date = db.Column(db.Date, default=date.today)
    sale_time = db.Column(db.Time)
    sale_type = db.Column(db.String(30), default='retail')  # retail, wholesale, online, custom
    sales_channel = db.Column(db.String(30))  # store, online, phone, exhibition

    # Pricing and Amounts
    subtotal = db.Column(db.Float, nullable=False, default=0.0)  # Before tax and discount
    discount_percentage = db.Column(db.Float, default=0.0)  # Overall discount percentage
    discount_amount = db.Column(db.Float, default=0.0)  # Total discount amount
    tax_percentage = db.Column(db.Float, default=0.0)  # Tax percentage (GST, VAT, etc.)
    tax_amount = db.Column(db.Float, default=0.0)  # Total tax amount
    shipping_charges = db.Column(db.Float, default=0.0)  # Shipping/delivery charges
    other_charges = db.Column(db.Float, default=0.0)  # Other miscellaneous charges
    total_amount = db.Column(db.Float, nullable=False)  # Final total amount

    # Payment Information
    payment_status = db.Column(db.String(30), default='pending')  # pending, partial, paid, refunded, cancelled
    payment_method = db.Column(db.String(30))  # cash, card, upi, bank_transfer, cheque, emi
    payment_terms = db.Column(db.String(50))  # immediate, 30_days, 60_days, etc.
    amount_paid = db.Column(db.Float, default=0.0)  # Amount already paid
    balance_amount = db.Column(db.Float, default=0.0)  # Remaining balance

    # Payment Details
    transaction_id = db.Column(db.String(100))  # Payment gateway transaction ID
    payment_reference = db.Column(db.String(100))  # Bank reference or cheque number
    payment_date = db.Column(db.Date)  # Date of payment
    due_date = db.Column(db.Date)  # Payment due date

    # Delivery Information
    delivery_method = db.Column(db.String(30))  # pickup, home_delivery, courier
    delivery_address = db.Column(db.Text)  # Delivery address if different from customer address
    delivery_date = db.Column(db.Date)  # Expected delivery date
    actual_delivery_date = db.Column(db.Date)  # Actual delivery date
    delivery_status = db.Column(db.String(30), default='pending')  # pending, shipped, delivered, returned
    tracking_number = db.Column(db.String(100))  # Courier tracking number
    delivery_charges = db.Column(db.Float, default=0.0)  # Delivery charges

    # Sales Representative
    sales_person = db.Column(db.String(100))  # Sales person name
    sales_commission = db.Column(db.Float, default=0.0)  # Commission amount
    commission_percentage = db.Column(db.Float, default=0.0)  # Commission percentage

    # Status and Workflow
    status = db.Column(db.String(30), default='draft')  # draft, confirmed, processing, completed, cancelled, returned
    priority = db.Column(db.String(20), default='normal')  # urgent, high, normal, low

    # Return and Exchange
    is_returnable = db.Column(db.Boolean, default=True)  # Can be returned
    return_period = db.Column(db.Integer, default=30)  # Return period in days
    exchange_policy = db.Column(db.Text)  # Exchange policy details

    # Gift and Special Services
    is_gift = db.Column(db.Boolean, default=False)  # Is this a gift purchase
    gift_message = db.Column(db.Text)  # Gift message
    gift_wrapping = db.Column(db.Boolean, default=False)  # Gift wrapping required
    gift_wrapping_charges = db.Column(db.Float, default=0.0)  # Gift wrapping charges

    # Insurance and Warranty
    insurance_required = db.Column(db.Boolean, default=False)  # Insurance required
    insurance_amount = db.Column(db.Float, default=0.0)  # Insurance amount
    warranty_period = db.Column(db.Integer, default=12)  # Warranty period in months
    extended_warranty = db.Column(db.Boolean, default=False)  # Extended warranty

    # Additional Information
    notes = db.Column(db.Text)  # Sale notes
    internal_notes = db.Column(db.Text)  # Internal notes
    special_instructions = db.Column(db.Text)  # Special instructions

    # Marketing and Analytics
    source = db.Column(db.String(50))  # How customer found us (referral, advertisement, etc.)
    campaign = db.Column(db.String(100))  # Marketing campaign
    coupon_code = db.Column(db.String(50))  # Coupon or promo code used

    # Tracking and Audit
    created_by = db.Column(db.String(100))  # Who created the sale
    updated_by = db.Column(db.String(100))  # Who last updated the sale
    created_at = db.Column(db.DateTime, default=func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp())

    # Relationships
    jewelry_items = db.relationship('JewelryItem', secondary=sale_items, backref='sales')

    # Legacy relationship for backward compatibility
    jewelry_id = db.Column(db.Integer, db.ForeignKey('jewelry_items.id', name='fk_sales_jewelry_id'))
    jewelry_item = db.relationship('JewelryItem', foreign_keys=[jewelry_id], backref='legacy_sales')

    # Industry-specific constants
    VALID_STATUSES = [
        'draft', 'confirmed', 'processing', 'completed',
        'cancelled', 'returned', 'exchanged', 'refunded'
    ]

    PAYMENT_STATUSES = ['pending', 'partial', 'paid', 'refunded', 'cancelled', 'overdue']
    PAYMENT_METHODS = ['cash', 'card', 'upi', 'bank_transfer', 'cheque', 'emi', 'crypto']
    SALE_TYPES = ['retail', 'wholesale', 'online', 'custom', 'exhibition', 'b2b']
    SALES_CHANNELS = ['store', 'online', 'phone', 'exhibition', 'home_visit', 'referral']
    DELIVERY_METHODS = ['pickup', 'home_delivery', 'courier', 'express', 'international']
    DELIVERY_STATUSES = ['pending', 'packed', 'shipped', 'in_transit', 'delivered', 'returned']
    PRIORITIES = ['urgent', 'high', 'normal', 'low']

    def __init__(self, **kwargs):
        super(Sale, self).__init__(**kwargs)
        if not self.invoice_no:
            self.generate_invoice_number()
        if not self.order_number:
            self.generate_order_number()

    def generate_invoice_number(self):
        """Generate a unique invoice number"""
        timestamp = datetime.now().strftime('%y%m%d%H%M')
        self.invoice_no = f"INV{timestamp}"

    def generate_order_number(self):
        """Generate a unique order number"""
        timestamp = datetime.now().strftime('%y%m%d%H%M')
        self.order_number = f"ORD{timestamp}"

    def calculate_totals(self):
        """Calculate all totals based on line items"""
        # This would be implemented to calculate from sale_items
        pass

    def calculate_balance(self):
        """Calculate remaining balance"""
        self.balance_amount = self.total_amount - (self.amount_paid or 0)

    def is_overdue(self):
        """Check if payment is overdue"""
        if self.due_date and self.payment_status not in ['paid', 'refunded']:
            return date.today() > self.due_date
        return False

    def get_days_overdue(self):
        """Get number of days overdue"""
        if self.is_overdue():
            return (date.today() - self.due_date).days
        return 0

    def can_be_returned(self):
        """Check if sale can be returned"""
        if not self.is_returnable:
            return False
        if self.return_period and self.sale_date:
            return (date.today() - self.sale_date).days <= self.return_period
        return True

    @classmethod
    def validate_status(cls, status):
        """Validate sale status"""
        return status in cls.VALID_STATUSES

    @classmethod
    def validate_payment_status(cls, payment_status):
        """Validate payment status"""
        return payment_status in cls.PAYMENT_STATUSES

    def to_dict(self):
        """Convert sale to dictionary for API responses"""
        return {
            'id': self.id,
            'invoice_no': self.invoice_no,
            'order_number': self.order_number,
            'customer_name': self.customer_name,
            'customer_email': self.customer_email,
            'customer_phone': self.customer_phone,
            'customer_address': self.customer_address,
            'customer_city': self.customer_city,
            'customer_state': self.customer_state,
            'customer_postal_code': self.customer_postal_code,
            'customer_country': self.customer_country,
            'sale_date': self.sale_date.isoformat() if self.sale_date else None,
            'sale_time': self.sale_time.isoformat() if self.sale_time else None,
            'sale_type': self.sale_type,
            'sales_channel': self.sales_channel,
            'subtotal': self.subtotal,
            'discount_percentage': self.discount_percentage,
            'discount_amount': self.discount_amount,
            'tax_percentage': self.tax_percentage,
            'tax_amount': self.tax_amount,
            'shipping_charges': self.shipping_charges,
            'other_charges': self.other_charges,
            'total_amount': self.total_amount,
            'payment_status': self.payment_status,
            'payment_method': self.payment_method,
            'payment_terms': self.payment_terms,
            'amount_paid': self.amount_paid,
            'balance_amount': self.balance_amount,
            'transaction_id': self.transaction_id,
            'payment_reference': self.payment_reference,
            'payment_date': self.payment_date.isoformat() if self.payment_date else None,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'delivery_method': self.delivery_method,
            'delivery_address': self.delivery_address,
            'delivery_date': self.delivery_date.isoformat() if self.delivery_date else None,
            'actual_delivery_date': self.actual_delivery_date.isoformat() if self.actual_delivery_date else None,
            'delivery_status': self.delivery_status,
            'tracking_number': self.tracking_number,
            'delivery_charges': self.delivery_charges,
            'sales_person': self.sales_person,
            'sales_commission': self.sales_commission,
            'commission_percentage': self.commission_percentage,
            'status': self.status,
            'priority': self.priority,
            'is_returnable': self.is_returnable,
            'return_period': self.return_period,
            'exchange_policy': self.exchange_policy,
            'is_gift': self.is_gift,
            'gift_message': self.gift_message,
            'gift_wrapping': self.gift_wrapping,
            'gift_wrapping_charges': self.gift_wrapping_charges,
            'insurance_required': self.insurance_required,
            'insurance_amount': self.insurance_amount,
            'warranty_period': self.warranty_period,
            'extended_warranty': self.extended_warranty,
            'notes': self.notes,
            'internal_notes': self.internal_notes,
            'special_instructions': self.special_instructions,
            'source': self.source,
            'campaign': self.campaign,
            'coupon_code': self.coupon_code,
            'created_by': self.created_by,
            'updated_by': self.updated_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'jewelry_items': [j.to_dict() for j in self.jewelry_items] if self.jewelry_items else [],
            'is_overdue': self.is_overdue(),
            'days_overdue': self.get_days_overdue(),
            'can_be_returned': self.can_be_returned()
        }

    def __repr__(self):
        return f'<Sale {self.invoice_no} - {self.customer_name}>'
