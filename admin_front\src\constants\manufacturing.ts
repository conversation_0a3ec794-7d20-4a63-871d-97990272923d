// Manufacturing Industry Standards and Constants

export const MANUFACTURING_STATUSES = [
  { value: 'draft', label: 'Draft', color: 'gray' },
  { value: 'sent', label: 'Sent to Vendor', color: 'blue' },
  { value: 'in_progress', label: 'In Progress', color: 'yellow' },
  { value: 'quality_check', label: 'Quality Check', color: 'purple' },
  { value: 'completed', label: 'Completed', color: 'green' },
  { value: 'cancelled', label: 'Cancelled', color: 'red' },
  { value: 'returned', label: 'Returned', color: 'orange' },
  { value: 'on_hold', label: 'On Hold', color: 'gray' }
];

export const ORDER_TYPES = [
  { value: 'Diamond Cutting', label: 'Diamond Cutting' },
  { value: 'Diamond Polishing', label: 'Diamond Polishing' },
  { value: 'Jewelry Setting', label: 'Jewelry Setting' },
  { value: 'Jewelry Repair', label: 'Jewelry Repair' },
  { value: 'Custom Design', label: 'Custom Design' },
  { value: 'Resizing', label: 'Resizing' },
  { value: 'Cleaning', label: 'Cleaning' },
  { value: 'Other', label: 'Other' }
];

export const PRIORITIES = [
  { value: 'urgent', label: 'Urgent', color: 'red' },
  { value: 'high', label: 'High', color: 'orange' },
  { value: 'normal', label: 'Normal', color: 'blue' },
  { value: 'low', label: 'Low', color: 'gray' }
];

export const QUALITY_STATUSES = [
  { value: 'pending', label: 'Pending', color: 'gray' },
  { value: 'passed', label: 'Passed', color: 'green' },
  { value: 'failed', label: 'Failed', color: 'red' },
  { value: 'needs_rework', label: 'Needs Rework', color: 'orange' }
];

export const PAYMENT_STATUSES = [
  { value: 'pending', label: 'Pending', color: 'gray' },
  { value: 'partial', label: 'Partial', color: 'yellow' },
  { value: 'paid', label: 'Paid', color: 'green' },
  { value: 'overdue', label: 'Overdue', color: 'red' }
];

// Validation ranges
export const VALIDATION_RANGES = {
  progress_percentage: { min: 0, max: 100 },
  estimated_cost: { min: 0, max: 10000000 },
  actual_cost: { min: 0, max: 10000000 },
  advance_paid: { min: 0, max: 10000000 },
  original_weight: { min: 0.01, max: 1000 },
  final_weight: { min: 0.01, max: 1000 }
};

// Helper functions
export const getStatusLabel = (status: string) => {
  const statusOption = MANUFACTURING_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.label : status;
};

export const getStatusColor = (status: string) => {
  const statusOption = MANUFACTURING_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.color : 'gray';
};

export const getPriorityLabel = (priority: string) => {
  const priorityOption = PRIORITIES.find(p => p.value === priority);
  return priorityOption ? priorityOption.label : priority;
};

export const getPriorityColor = (priority: string) => {
  const priorityOption = PRIORITIES.find(p => p.value === priority);
  return priorityOption ? priorityOption.color : 'gray';
};

export const getQualityStatusLabel = (status: string) => {
  const statusOption = QUALITY_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.label : status;
};

export const getQualityStatusColor = (status: string) => {
  const statusOption = QUALITY_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.color : 'gray';
};

export const getPaymentStatusLabel = (status: string) => {
  const statusOption = PAYMENT_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.label : status;
};

export const getPaymentStatusColor = (status: string) => {
  const statusOption = PAYMENT_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.color : 'gray';
};

export const getOrderTypeLabel = (orderType: string) => {
  const typeOption = ORDER_TYPES.find(t => t.value === orderType);
  return typeOption ? typeOption.label : orderType;
};

export const calculateLossPercentage = (originalWeight: number, finalWeight: number): number => {
  if (originalWeight <= 0) return 0;
  const loss = originalWeight - finalWeight;
  return (loss / originalWeight) * 100;
};

export const calculateBalanceAmount = (actualCost: number, advancePaid: number): number => {
  return Math.max(0, actualCost - advancePaid);
};

export const formatWeight = (weight: number): string => {
  return `${weight.toFixed(3)} ct`;
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2
  }).format(amount);
};

export const formatPercentage = (percentage: number): string => {
  return `${percentage.toFixed(2)}%`;
};

export const getDaysRemaining = (expectedDate: string): number | null => {
  if (!expectedDate) return null;
  const today = new Date();
  const expected = new Date(expectedDate);
  const diffTime = expected.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

export const isOverdue = (expectedDate: string, status: string): boolean => {
  if (!expectedDate || ['completed', 'cancelled'].includes(status)) return false;
  const today = new Date();
  const expected = new Date(expectedDate);
  return today > expected;
};

export const getProgressColor = (percentage: number): string => {
  if (percentage < 25) return 'red';
  if (percentage < 50) return 'orange';
  if (percentage < 75) return 'yellow';
  if (percentage < 100) return 'blue';
  return 'green';
};

export const getUrgencyLevel = (daysRemaining: number | null): 'critical' | 'urgent' | 'normal' | 'relaxed' => {
  if (daysRemaining === null) return 'normal';
  if (daysRemaining < 0) return 'critical';
  if (daysRemaining <= 2) return 'urgent';
  if (daysRemaining <= 7) return 'normal';
  return 'relaxed';
};

export const generateOrderNumber = (): string => {
  const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 12);
  return `MFG${timestamp}`;
};

export const validateOrderData = (data: any): string[] => {
  const errors: string[] = [];
  
  // Required fields
  if (!data.vendor_id) {
    errors.push('Vendor is required');
  }
  
  if (!data.order_type) {
    errors.push('Order type is required');
  }
  
  if (!data.expected_return_date) {
    errors.push('Expected return date is required');
  }
  
  // Date validations
  if (data.expected_return_date && data.sent_date) {
    const sentDate = new Date(data.sent_date);
    const expectedDate = new Date(data.expected_return_date);
    if (expectedDate <= sentDate) {
      errors.push('Expected return date must be after sent date');
    }
  }
  
  // Cost validations
  if (data.estimated_cost && data.estimated_cost < 0) {
    errors.push('Estimated cost cannot be negative');
  }
  
  if (data.actual_cost && data.actual_cost < 0) {
    errors.push('Actual cost cannot be negative');
  }
  
  if (data.advance_paid && data.advance_paid < 0) {
    errors.push('Advance paid cannot be negative');
  }
  
  if (data.advance_paid && data.actual_cost && data.advance_paid > data.actual_cost) {
    errors.push('Advance paid cannot exceed actual cost');
  }
  
  // Progress validation
  if (data.progress_percentage !== undefined) {
    if (data.progress_percentage < 0 || data.progress_percentage > 100) {
      errors.push('Progress percentage must be between 0 and 100');
    }
  }
  
  // Weight validations
  if (data.total_original_weight && data.total_original_weight <= 0) {
    errors.push('Original weight must be positive');
  }
  
  if (data.total_final_weight && data.total_final_weight <= 0) {
    errors.push('Final weight must be positive');
  }
  
  if (data.total_original_weight && data.total_final_weight && 
      data.total_final_weight > data.total_original_weight) {
    errors.push('Final weight cannot exceed original weight');
  }
  
  return errors;
};

// Status workflow validation
export const getValidNextStatuses = (currentStatus: string): string[] => {
  const workflows: Record<string, string[]> = {
    'draft': ['sent', 'cancelled'],
    'sent': ['in_progress', 'cancelled', 'on_hold'],
    'in_progress': ['quality_check', 'on_hold', 'cancelled'],
    'quality_check': ['completed', 'needs_rework', 'returned'],
    'needs_rework': ['in_progress', 'cancelled'],
    'on_hold': ['in_progress', 'cancelled'],
    'completed': ['returned'], // Only if there are issues
    'cancelled': [], // Terminal state
    'returned': ['in_progress'] // If rework is needed
  };
  
  return workflows[currentStatus] || [];
};

export const canTransitionTo = (currentStatus: string, newStatus: string): boolean => {
  const validStatuses = getValidNextStatuses(currentStatus);
  return validStatuses.includes(newStatus);
};
