#!/usr/bin/env python3
"""
Comprehensive Test Runner for Diamond Management System
Runs both backend and frontend tests and generates a detailed report
"""

import subprocess
import sys
import os
import json
import time
from datetime import datetime
from pathlib import Path

class ComprehensiveTestRunner:
    def __init__(self):
        self.results = {
            'backend': {'success': False, 'output': '', 'duration': 0},
            'frontend': {'success': False, 'output': '', 'duration': 0},
            'overall': {'success': False, 'start_time': '', 'end_time': '', 'total_duration': 0}
        }
        
    def check_prerequisites(self):
        """Check if all prerequisites are met"""
        print("🔍 Checking prerequisites...")
        
        # Check if backend test file exists
        backend_test = Path("admin_backend/test_diamond_api.py")
        if not backend_test.exists():
            print("❌ Backend test file not found")
            return False
            
        # Check if frontend test file exists
        frontend_test = Path("admin_front/test_diamond_frontend.js")
        if not frontend_test.exists():
            print("❌ Frontend test file not found")
            return False
            
        # Check if Python is available
        try:
            subprocess.run([sys.executable, "--version"], check=True, capture_output=True)
            print("✅ Python available")
        except subprocess.CalledProcessError:
            print("❌ Python not available")
            return False
            
        # Check if Node.js is available
        try:
            subprocess.run(["node", "--version"], check=True, capture_output=True)
            print("✅ Node.js available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Node.js not available")
            return False
            
        # Check if required Python packages are available
        try:
            import requests
            print("✅ Python requests package available")
        except ImportError:
            print("❌ Python requests package not available")
            print("   Install with: pip install requests")
            return False
            
        print("✅ All prerequisites met")
        return True
    
    def run_backend_tests(self):
        """Run backend API tests"""
        print("\n🔧 Running Backend Tests...")
        print("-" * 30)
        
        start_time = time.time()
        
        try:
            # Change to backend directory
            backend_dir = Path("admin_backend")
            
            # Run the backend test script
            result = subprocess.run(
                [sys.executable, "test_diamond_api.py"],
                cwd=backend_dir,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            duration = time.time() - start_time
            
            self.results['backend']['duration'] = duration
            self.results['backend']['output'] = result.stdout + result.stderr
            self.results['backend']['success'] = result.returncode == 0
            
            if self.results['backend']['success']:
                print("✅ Backend tests completed successfully")
            else:
                print("❌ Backend tests failed")
                print("Output:", result.stdout)
                print("Errors:", result.stderr)
                
        except subprocess.TimeoutExpired:
            print("❌ Backend tests timed out")
            self.results['backend']['output'] = "Tests timed out after 5 minutes"
        except Exception as e:
            print(f"❌ Backend tests failed with exception: {e}")
            self.results['backend']['output'] = str(e)
    
    def run_frontend_tests(self):
        """Run frontend tests"""
        print("\n🌐 Running Frontend Tests...")
        print("-" * 30)
        
        start_time = time.time()
        
        try:
            # Change to frontend directory
            frontend_dir = Path("admin_front")
            
            # Check if puppeteer is installed
            try:
                subprocess.run(
                    ["npm", "list", "puppeteer"],
                    cwd=frontend_dir,
                    check=True,
                    capture_output=True
                )
            except subprocess.CalledProcessError:
                print("📦 Installing puppeteer...")
                subprocess.run(
                    ["npm", "install", "puppeteer"],
                    cwd=frontend_dir,
                    check=True
                )
            
            # Run the frontend test script
            result = subprocess.run(
                ["node", "test_diamond_frontend.js"],
                cwd=frontend_dir,
                capture_output=True,
                text=True,
                timeout=600  # 10 minute timeout
            )
            
            duration = time.time() - start_time
            
            self.results['frontend']['duration'] = duration
            self.results['frontend']['output'] = result.stdout + result.stderr
            self.results['frontend']['success'] = result.returncode == 0
            
            if self.results['frontend']['success']:
                print("✅ Frontend tests completed successfully")
            else:
                print("❌ Frontend tests failed")
                print("Output:", result.stdout)
                print("Errors:", result.stderr)
                
        except subprocess.TimeoutExpired:
            print("❌ Frontend tests timed out")
            self.results['frontend']['output'] = "Tests timed out after 10 minutes"
        except Exception as e:
            print(f"❌ Frontend tests failed with exception: {e}")
            self.results['frontend']['output'] = str(e)
    
    def generate_report(self):
        """Generate a comprehensive test report"""
        print("\n📊 Generating Test Report...")
        print("=" * 50)
        
        # Calculate overall results
        self.results['overall']['success'] = (
            self.results['backend']['success'] and 
            self.results['frontend']['success']
        )
        
        self.results['overall']['total_duration'] = (
            self.results['backend']['duration'] + 
            self.results['frontend']['duration']
        )
        
        # Print summary
        print(f"🕐 Test Start Time: {self.results['overall']['start_time']}")
        print(f"🕐 Test End Time: {self.results['overall']['end_time']}")
        print(f"⏱️  Total Duration: {self.results['overall']['total_duration']:.2f} seconds")
        print()
        
        print("📋 RESULTS SUMMARY:")
        print(f"  Backend Tests:  {'✅ PASS' if self.results['backend']['success'] else '❌ FAIL'} ({self.results['backend']['duration']:.2f}s)")
        print(f"  Frontend Tests: {'✅ PASS' if self.results['frontend']['success'] else '❌ FAIL'} ({self.results['frontend']['duration']:.2f}s)")
        print(f"  Overall:        {'✅ PASS' if self.results['overall']['success'] else '❌ FAIL'}")
        
        # Save detailed report to file
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        
        # Print recommendations
        print("\n💡 RECOMMENDATIONS:")
        if not self.results['backend']['success']:
            print("  - Check backend server is running on http://localhost:5000")
            print("  - Verify database connection and test data")
            print("  - Review backend test output for specific failures")
            
        if not self.results['frontend']['success']:
            print("  - Check frontend server is running on http://localhost:5173")
            print("  - Verify login credentials are correct")
            print("  - Review frontend test output for specific failures")
            
        if self.results['overall']['success']:
            print("  - All tests passed! The diamond management system is ready for production.")
            print("  - Consider setting up automated testing in CI/CD pipeline")
            print("  - Monitor performance metrics in production environment")
        
        return self.results['overall']['success']
    
    def run_all_tests(self):
        """Run all tests and generate report"""
        print("🚀 Diamond Management System - Comprehensive Test Suite")
        print("=" * 60)
        
        self.results['overall']['start_time'] = datetime.now().isoformat()
        
        # Check prerequisites
        if not self.check_prerequisites():
            print("❌ Prerequisites not met. Please fix the issues above.")
            return False
        
        # Run tests
        self.run_backend_tests()
        self.run_frontend_tests()
        
        self.results['overall']['end_time'] = datetime.now().isoformat()
        
        # Generate report
        success = self.generate_report()
        
        return success

def main():
    """Main function"""
    runner = ComprehensiveTestRunner()
    success = runner.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed! Diamond Management System is production-ready.")
        sys.exit(0)
    else:
        print("\n⚠️  Some tests failed. Please review the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
