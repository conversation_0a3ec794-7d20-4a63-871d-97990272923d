#!/usr/bin/env python3
"""Simple login test"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

# Test different credentials
credentials = [
    {"email": "<EMAIL>", "password": "admin123"},
    {"email": "<EMAIL>", "password": "password"},
    {"email": "<EMAIL>", "password": "admin123"},
    {"email": "<EMAIL>", "password": "password"},
]

for cred in credentials:
    print(f"Testing login with {cred['email']} / {cred['password']}")
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=cred)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        print("-" * 40)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('access_token'):
                print("✅ Login successful!")
                break
    except Exception as e:
        print(f"Error: {e}")
        print("-" * 40)
