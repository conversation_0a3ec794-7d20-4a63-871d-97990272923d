from app import db
from datetime import datetime

class DiamondImage(db.Model):
    __tablename__ = 'diamond_images'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    diamond_id = db.Column(db.Integer, db.<PERSON><PERSON>('diamonds.id'), nullable=False)
    image_url = db.Column(db.String(500), nullable=False)
    image_type = db.Column(db.String(50), nullable=False, default='main')  # main, side, certificate, 360
    is_primary = db.Column(db.<PERSON>, default=False)
    alt_text = db.Column(db.String(255))
    file_size = db.Column(db.Integer)  # in bytes
    file_format = db.Column(db.String(10))  # jpg, png, etc.
    width = db.Column(db.Integer)
    height = db.Column(db.Integer)
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship
    diamond = db.relationship('Diamond', backref=db.backref('images', lazy=True, cascade='all, delete-orphan'))
    
    def to_dict(self):
        return {
            'id': self.id,
            'diamond_id': self.diamond_id,
            'image_url': self.image_url,
            'image_type': self.image_type,
            'is_primary': self.is_primary,
            'alt_text': self.alt_text,
            'file_size': self.file_size,
            'file_format': self.file_format,
            'width': self.width,
            'height': self.height,
            'uploaded_at': self.uploaded_at.isoformat() if self.uploaded_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @staticmethod
    def get_valid_types():
        return ['main', 'side', 'certificate', '360', 'grading_report', 'laser_inscription']
    
    def __repr__(self):
        return f'<DiamondImage {self.id}: {self.image_type} for Diamond {self.diamond_id}>'
