#!/usr/bin/env python3
"""
Test jewelry directly through database
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'admin_backend'))

from app import create_app, db
from app.models.jewelry import JewelryItem

def test_jewelry_direct():
    print("🔍 Testing Jewelry Direct Database Access")
    print("=" * 40)
    
    app = create_app()
    with app.app_context():
        # Get all jewelry items
        jewelry_items = JewelryItem.query.all()
        print(f"Found {len(jewelry_items)} jewelry items in database")
        
        if jewelry_items:
            item = jewelry_items[0]
            print(f"First item ID: {item.id}")
            print(f"Name: {item.name}")
            print(f"Vendor ID: {item.vendor_id}")
            
            # Test accessing vendor relationship
            try:
                vendor = item.vendor
                print(f"Vendor: {vendor.name if vendor else 'None'}")
            except Exception as e:
                print(f"Error accessing vendor: {str(e)}")
            
            # Test to_dict method
            try:
                item_dict = item.to_dict()
                print(f"to_dict() success: {len(item_dict)} fields")
            except Exception as e:
                print(f"Error in to_dict(): {str(e)}")
            
            # Test individual field access
            print("\nTesting field access:")
            fields_to_test = ['name', 'design_code', 'category', 'metal_type', 'cost_price', 'status']
            for field in fields_to_test:
                try:
                    value = getattr(item, field, 'NOT_FOUND')
                    print(f"  {field}: {value}")
                except Exception as e:
                    print(f"  {field}: ERROR - {str(e)}")

if __name__ == "__main__":
    test_jewelry_direct()
