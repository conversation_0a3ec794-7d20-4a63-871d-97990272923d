const validateSizeInput = (size: string): boolean => {
  const singleSizeRegex = /^\d+(\.\d{1,2})?$/; // Matches single size format
  const dimensionSizeRegex = /^\d+(\.\d{1,2})?\*\d+(\.\d{1,2})?$/; // Matches dimension format with *

  return singleSizeRegex.test(size) || dimensionSizeRegex.test(size);
};

const getSizeErrorMessage = (): string => {
  return "Please enter size in correct format, e.g., 6.50 or 5.50*7.90";
};

const getStrictSizeErrorMessage = (): string => {
  return "Please enter size in the correct format, e.g., 6.50 or 5.50*7.90";
};

export function validateStrictSizeInput(size: string): boolean {
  // Strict validation logic for diamond sizes
  const singleSizePattern = /^\d+(\.\d+)?$/; // Matches single size format (e.g., 5 or 5.5)
  const dimensionPattern = /^\d+(\.\d+)?[x\*]\d+(\.\d+)?$/; // Matches dimension format with `x` or `*` (e.g., 5x5 or 5.5*5.5)

  return singleSizePattern.test(size) || dimensionPattern.test(size);
}

export { validateSizeInput, getSizeErrorMessage, getStrictSizeErrorMessage };