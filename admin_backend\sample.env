DATABASE_URL=postgresql://postgres:root@localhost:5432/disease
# prod
PROD_DATABASE=***********************************************************************************************
JWT_SECRET_KEY="new_secure_random_key"
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY="new_secure_random_key"

ACCOUNT_LOCKOUT_DURATION_MINUTES=60
MAX_LOGIN_ATTEMPTS=3

# admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=""
ADMIN_FIRST_NAME=Admin
ADMIN_LAST_NAME=User