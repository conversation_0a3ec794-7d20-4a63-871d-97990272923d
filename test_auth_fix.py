#!/usr/bin/env python3
"""
Test authentication fixes
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"
LOGIN_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

def test_auth_fix():
    print("🔐 Testing Authentication Fixes")
    print("=" * 40)
    
    # Step 1: Login
    print("1. Testing login...")
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=LOGIN_CREDENTIALS)
        print(f"   Login Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            print(f"   Token received: {len(token)} characters")
            
            # Step 2: Test protected endpoints
            print("\n2. Testing protected endpoints...")
            headers = {'Authorization': f'Bearer {token}'}
            
            endpoints = [
                '/diamonds',
                '/vendors',
                '/manufacturing',
                '/jewelry',
                '/sales',
                '/dashboard'
            ]
            
            for endpoint in endpoints:
                try:
                    response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
                    print(f"   {endpoint}: {response.status_code}")
                    
                    if response.status_code == 200:
                        result = response.json()
                        if isinstance(result, dict) and 'data' in result:
                            print(f"     ✅ Success! Found {len(result['data'])} items")
                        elif isinstance(result, list):
                            print(f"     ✅ Success! Found {len(result)} items")
                        else:
                            print(f"     ✅ Success! Response: {list(result.keys())[:3]}")
                    else:
                        print(f"     ❌ Error: {response.text[:100]}")
                        
                except Exception as e:
                    print(f"     ❌ Exception: {str(e)}")
            
            # Step 3: Test without token
            print("\n3. Testing without token (should fail)...")
            try:
                response = requests.get(f"{BASE_URL}/diamonds")
                print(f"   No token: {response.status_code}")
                if response.status_code == 401:
                    print("     ✅ Correctly rejected unauthorized request")
                else:
                    print("     ❌ Should have returned 401")
            except Exception as e:
                print(f"     ❌ Exception: {str(e)}")
                
        else:
            print(f"   ❌ Login failed: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")

if __name__ == "__main__":
    test_auth_fix()
