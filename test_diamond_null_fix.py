#!/usr/bin/env python3
"""
Test diamond null reference fix
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_diamond_null_fix():
    print("🔧 Testing Diamond Null Reference Fix...")
    
    # Login
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Create a diamond with null size_mm to test the fix
    print("\n💎 Creating diamond with null size_mm...")
    test_diamond = {
        "shape_id": 1,
        "carat": 1.0,
        "color": "G",
        "clarity": "SI1",
        "certificate_no": f"NULL_TEST_{hash('null_test') % 100000:05d}",
        "vendor_id": 1,
        "purchase_date": "2025-07-21",
        "quantity": 1,
        "minimum_stock": 1,
        "status": "in_stock"
        # Note: Not including size_mm to test null handling
    }
    
    response = session.post(f"{BASE_URL}/diamonds", json=test_diamond)
    if response.status_code in [200, 201]:
        created_diamond = response.json()
        print(f"✅ Created diamond with ID: {created_diamond['id']}")
        print(f"   size_mm value: {created_diamond.get('size_mm', 'None')}")
        
        # Verify the diamond appears in the list
        print("\n📋 Testing diamond list with null size_mm...")
        response = session.get(f"{BASE_URL}/diamonds")
        if response.status_code == 200:
            diamonds_data = response.json()
            diamonds = diamonds_data.get('data', [])
            
            # Find our test diamond
            test_diamond_found = None
            for diamond in diamonds:
                if diamond['id'] == created_diamond['id']:
                    test_diamond_found = diamond
                    break
            
            if test_diamond_found:
                print(f"✅ Test diamond found in list")
                print(f"   size_mm: {test_diamond_found.get('size_mm', 'None')}")
                print(f"   Frontend should handle null size_mm gracefully")
            else:
                print(f"❌ Test diamond not found in list")
        else:
            print(f"❌ Failed to get diamond list: {response.status_code}")
    else:
        print(f"❌ Failed to create test diamond: {response.status_code}")
    
    print("\n🎯 Null Reference Fix Test Complete!")
    print("Frontend should now handle null size_mm values without errors.")

if __name__ == "__main__":
    test_diamond_null_fix()
