#!/usr/bin/env python3
"""
Debug authentication issues
"""

import requests

BASE_URL = "http://localhost:8000/api"

def debug_auth():
    print("🔍 Debugging Authentication...")
    
    # Test jewelry endpoint specifically
    print("\nTesting jewelry endpoint without auth...")
    response = requests.get(f"{BASE_URL}/jewelry")
    print(f"Status: {response.status_code}")
    print(f"Headers: {dict(response.headers)}")
    print(f"Response: {response.text[:200]}")
    
    # Test with invalid token
    print("\nTesting jewelry endpoint with invalid token...")
    headers = {'Authorization': 'Bearer invalid_token'}
    response = requests.get(f"{BASE_URL}/jewelry", headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text[:200]}")

if __name__ == "__main__":
    debug_auth()
