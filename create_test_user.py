#!/usr/bin/env python3
"""Create a test user for testing"""

from app import create_app, db
from app.models.user import User
from app.models.role import Role

app = create_app()

with app.app_context():
    # Check if test user exists
    test_email = "<EMAIL>"
    test_user = User.query.filter_by(email=test_email).first()
    
    if test_user:
        print(f"Test user {test_email} already exists")
        # Update password
        test_user.password = "test123"
        db.session.commit()
        print("Updated password to 'test123'")
    else:
        # Get admin role
        admin_role = Role.query.filter_by(name='admin').first()
        if not admin_role:
            admin_role = Role(name='admin', description='Administrator')
            db.session.add(admin_role)
            db.session.commit()
        
        # Create test user
        test_user = User(
            email=test_email,
            password="test123",
            first_name="Test",
            last_name="User",
            role_id=admin_role.id
        )
        
        db.session.add(test_user)
        db.session.commit()
        print(f"Created test user: {test_email} / test123")
    
    # Test password verification
    if test_user.verify_password("test123"):
        print("✅ Password verification works")
    else:
        print("❌ Password verification failed")
    
    # Also update existing users' passwords
    existing_users = User.query.all()
    for user in existing_users:
        if user.email in ["<EMAIL>", "<EMAIL>"]:
            user.password = "admin123"
            print(f"Updated password for {user.email} to 'admin123'")
    
    db.session.commit()
    print("All users updated successfully")
