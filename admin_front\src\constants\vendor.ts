// Vendor Industry Standards and Constants

export const VENDOR_STATUSES = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'blacklisted', label: 'Blacklisted' },
  { value: 'pending_verification', label: 'Pending Verification' }
];

export const BUSINESS_TYPES = [
  { value: 'Manufacturer', label: 'Manufacturer' },
  { value: 'Supplier', label: 'Supplier' },
  { value: 'Trader', label: 'Trader' },
  { value: 'Wholesaler', label: 'Wholesaler' },
  { value: 'Artisan', label: 'Artisan' },
  { value: 'Importer', label: 'Importer' },
  { value: 'Exporter', label: 'Exporter' }
];

export const PAYMENT_TERMS = [
  { value: 'Cash on Delivery', label: 'Cash on Delivery' },
  { value: '7 days', label: '7 days' },
  { value: '15 days', label: '15 days' },
  { value: '30 days', label: '30 days' },
  { value: '45 days', label: '45 days' },
  { value: '60 days', label: '60 days' },
  { value: '90 days', label: '90 days' },
  { value: 'Advance Payment', label: 'Advance Payment' }
];

export const INDIAN_STATES = [
  { value: 'Andhra Pradesh', label: 'Andhra Pradesh' },
  { value: 'Arunachal Pradesh', label: 'Arunachal Pradesh' },
  { value: 'Assam', label: 'Assam' },
  { value: 'Bihar', label: 'Bihar' },
  { value: 'Chhattisgarh', label: 'Chhattisgarh' },
  { value: 'Goa', label: 'Goa' },
  { value: 'Gujarat', label: 'Gujarat' },
  { value: 'Haryana', label: 'Haryana' },
  { value: 'Himachal Pradesh', label: 'Himachal Pradesh' },
  { value: 'Jharkhand', label: 'Jharkhand' },
  { value: 'Karnataka', label: 'Karnataka' },
  { value: 'Kerala', label: 'Kerala' },
  { value: 'Madhya Pradesh', label: 'Madhya Pradesh' },
  { value: 'Maharashtra', label: 'Maharashtra' },
  { value: 'Manipur', label: 'Manipur' },
  { value: 'Meghalaya', label: 'Meghalaya' },
  { value: 'Mizoram', label: 'Mizoram' },
  { value: 'Nagaland', label: 'Nagaland' },
  { value: 'Odisha', label: 'Odisha' },
  { value: 'Punjab', label: 'Punjab' },
  { value: 'Rajasthan', label: 'Rajasthan' },
  { value: 'Sikkim', label: 'Sikkim' },
  { value: 'Tamil Nadu', label: 'Tamil Nadu' },
  { value: 'Telangana', label: 'Telangana' },
  { value: 'Tripura', label: 'Tripura' },
  { value: 'Uttar Pradesh', label: 'Uttar Pradesh' },
  { value: 'Uttarakhand', label: 'Uttarakhand' },
  { value: 'West Bengal', label: 'West Bengal' },
  { value: 'Delhi', label: 'Delhi' },
  { value: 'Jammu and Kashmir', label: 'Jammu and Kashmir' },
  { value: 'Ladakh', label: 'Ladakh' },
  { value: 'Puducherry', label: 'Puducherry' },
  { value: 'Chandigarh', label: 'Chandigarh' },
  { value: 'Dadra and Nagar Haveli and Daman and Diu', label: 'Dadra and Nagar Haveli and Daman and Diu' },
  { value: 'Lakshadweep', label: 'Lakshadweep' },
  { value: 'Andaman and Nicobar Islands', label: 'Andaman and Nicobar Islands' }
];

export const COUNTRIES = [
  { value: 'India', label: 'India' },
  { value: 'United States', label: 'United States' },
  { value: 'United Kingdom', label: 'United Kingdom' },
  { value: 'Canada', label: 'Canada' },
  { value: 'Australia', label: 'Australia' },
  { value: 'Germany', label: 'Germany' },
  { value: 'France', label: 'France' },
  { value: 'Italy', label: 'Italy' },
  { value: 'Spain', label: 'Spain' },
  { value: 'Netherlands', label: 'Netherlands' },
  { value: 'Belgium', label: 'Belgium' },
  { value: 'Switzerland', label: 'Switzerland' },
  { value: 'Austria', label: 'Austria' },
  { value: 'Sweden', label: 'Sweden' },
  { value: 'Norway', label: 'Norway' },
  { value: 'Denmark', label: 'Denmark' },
  { value: 'Finland', label: 'Finland' },
  { value: 'Japan', label: 'Japan' },
  { value: 'South Korea', label: 'South Korea' },
  { value: 'China', label: 'China' },
  { value: 'Singapore', label: 'Singapore' },
  { value: 'Hong Kong', label: 'Hong Kong' },
  { value: 'Thailand', label: 'Thailand' },
  { value: 'Malaysia', label: 'Malaysia' },
  { value: 'Indonesia', label: 'Indonesia' },
  { value: 'Philippines', label: 'Philippines' },
  { value: 'Vietnam', label: 'Vietnam' },
  { value: 'United Arab Emirates', label: 'United Arab Emirates' },
  { value: 'Saudi Arabia', label: 'Saudi Arabia' },
  { value: 'Israel', label: 'Israel' },
  { value: 'Turkey', label: 'Turkey' },
  { value: 'South Africa', label: 'South Africa' },
  { value: 'Brazil', label: 'Brazil' },
  { value: 'Mexico', label: 'Mexico' },
  { value: 'Argentina', label: 'Argentina' },
  { value: 'Chile', label: 'Chile' },
  { value: 'Colombia', label: 'Colombia' },
  { value: 'Peru', label: 'Peru' },
  { value: 'Russia', label: 'Russia' },
  { value: 'Poland', label: 'Poland' },
  { value: 'Czech Republic', label: 'Czech Republic' },
  { value: 'Hungary', label: 'Hungary' },
  { value: 'Romania', label: 'Romania' },
  { value: 'Bulgaria', label: 'Bulgaria' },
  { value: 'Croatia', label: 'Croatia' },
  { value: 'Serbia', label: 'Serbia' },
  { value: 'Greece', label: 'Greece' },
  { value: 'Portugal', label: 'Portugal' },
  { value: 'Ireland', label: 'Ireland' },
  { value: 'New Zealand', label: 'New Zealand' }
];

// Validation patterns
export const VALIDATION_PATTERNS = {
  gst: /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
  pan: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
  phone: /^[+]?[1-9]?[0-9]{7,15}$/,
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  ifsc: /^[A-Z]{4}0[A-Z0-9]{6}$/,
  postalCode: /^[1-9][0-9]{5}$/
};

// Validation ranges
export const VALIDATION_RANGES = {
  credit_limit: { min: 0, max: 10000000 },
  rating: { min: 1, max: 5 }
};

// Helper functions
export const getStatusLabel = (status: string) => {
  const statusOption = VENDOR_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.label : status;
};

export const getBusinessTypeLabel = (businessType: string) => {
  const typeOption = BUSINESS_TYPES.find(t => t.value === businessType);
  return typeOption ? typeOption.label : businessType;
};

export const validateGSTNumber = (gst: string): boolean => {
  return VALIDATION_PATTERNS.gst.test(gst);
};

export const validatePANNumber = (pan: string): boolean => {
  return VALIDATION_PATTERNS.pan.test(pan);
};

export const validatePhoneNumber = (phone: string): boolean => {
  return VALIDATION_PATTERNS.phone.test(phone);
};

export const validateEmail = (email: string): boolean => {
  return VALIDATION_PATTERNS.email.test(email);
};

export const validateIFSCCode = (ifsc: string): boolean => {
  return VALIDATION_PATTERNS.ifsc.test(ifsc);
};

export const validatePostalCode = (postalCode: string): boolean => {
  return VALIDATION_PATTERNS.postalCode.test(postalCode);
};

export const getRatingStars = (rating: number): string => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  return '★'.repeat(fullStars) + 
         (hasHalfStar ? '☆' : '') + 
         '☆'.repeat(emptyStars);
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2
  }).format(amount);
};
