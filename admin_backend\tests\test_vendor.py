import requests
from tests.conftest import get_tokens
from datetime import datetime

def test_vendor_crud(base_url):
    # Use a unique user for this test
    test_user = {
        "email": f"vendor_{datetime.now().strftime('%Y%m%d%H%M%S%f')}@example.com",
        "password": "Test1234!",
        "first_name": "Vendor",
        "last_name": "Tester"
    }
    access_token, _ = get_tokens(base_url, test_user)
    headers = {"Authorization": f"Bearer {access_token}"}
    vendor = {
        "name": "Test Vendor",
        "gst_number": f"GST{datetime.now().strftime('%Y%m%d%H%M%S%f')}",
        "contact_number": "1234567890",
        "address": "123 Test Street"
    }
    # Create
    r = requests.post(f"{base_url}/vendors/", json=vendor, headers=headers, timeout=10)
    if r.status_code != 201:
        print("Create vendor failed:", r.status_code, r.text)
    assert r.status_code == 201
    data = r.json()
    assert "id" in data, f"Response missing 'id': {data}"
    vendor_id = data["id"]
    # Get
    r = requests.get(f"{base_url}/vendors/{vendor_id}", headers=headers, timeout=10)
    if r.status_code != 200:
        print("Get vendor failed:", r.status_code, r.text)
    assert r.status_code == 200
    # Update
    r = requests.put(f"{base_url}/vendors/{vendor_id}", json={"name": "Updated Vendor"}, headers=headers, timeout=10)
    if r.status_code != 200:
        print("Update vendor failed:", r.status_code, r.text)
    assert r.status_code == 200
    # Delete
    r = requests.delete(f"{base_url}/vendors/{vendor_id}", headers=headers, timeout=10)
    if r.status_code != 200:
        print("Delete vendor failed:", r.status_code, r.text)
    assert r.status_code == 200
