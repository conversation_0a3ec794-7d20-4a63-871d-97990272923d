import requests
from tests.conftest import get_tokens
from datetime import datetime

def test_dashboard_summary(base_url):
    test_user = {
        "email": f"dashboard_{datetime.now().strftime('%Y%m%d%H%M%S%f')}@example.com",
        "password": "Test1234!",
        "first_name": "API",
        "last_name": "Tester"
    }
    access_token, _ = get_tokens(base_url, test_user)
    headers = {"Authorization": f"Bearer {access_token}"}
    r = requests.get(f"{base_url}/dashboard/summary", headers=headers, timeout=10)
    if r.status_code != 200:
        print("Dashboard summary failed:", r.status_code, r.text)
    assert r.status_code == 200
