#!/usr/bin/env python3
"""
Create test data for frontend testing
"""

import requests
import time

BASE_URL = "http://localhost:8000/api"

def create_test_data():
    print("🔧 Creating Test Data...")
    
    # Login first
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Create test jewelry items with in_stock status
    print("\n💍 Creating test jewelry items...")
    
    jewelry_items = [
        {
            "name": "Diamond Engagement Ring",
            "design_code": "DER001",
            "vendor_id": 1,
            "category": "Ring",
            "subcategory": "Engagement",
            "gross_weight": 4.5,
            "net_weight": 4.2,
            "metal_type": "Gold",
            "metal_purity": "18K",
            "cost_price": 35000,
            "retail_price": 55000,
            "making_charges": 8000,
            "status": "in_stock",
            "location": "Display Case A",
            "description": "Beautiful diamond engagement ring"
        },
        {
            "name": "Gold Wedding Band",
            "design_code": "GWB002",
            "vendor_id": 1,
            "category": "Ring",
            "subcategory": "Wedding",
            "gross_weight": 6.0,
            "net_weight": 5.8,
            "metal_type": "Gold",
            "metal_purity": "22K",
            "cost_price": 28000,
            "retail_price": 42000,
            "making_charges": 5000,
            "status": "in_stock",
            "location": "Display Case B",
            "description": "Classic gold wedding band"
        },
        {
            "name": "Diamond Necklace",
            "design_code": "DN003",
            "vendor_id": 1,
            "category": "Necklace",
            "subcategory": "Statement",
            "gross_weight": 15.2,
            "net_weight": 14.8,
            "metal_type": "Gold",
            "metal_purity": "18K",
            "cost_price": 85000,
            "retail_price": 125000,
            "making_charges": 15000,
            "status": "in_stock",
            "location": "Vault A",
            "description": "Elegant diamond necklace"
        }
    ]
    
    created_count = 0
    for item in jewelry_items:
        response = session.post(f"{BASE_URL}/jewelry", json=item)
        if response.status_code in [200, 201]:
            created_count += 1
            print(f"  ✅ Created: {item['name']}")
        else:
            print(f"  ❌ Failed to create {item['name']}: {response.status_code}")
    
    print(f"\n✅ Created {created_count} jewelry items")
    
    # Verify the data
    print("\n🔍 Verifying created data...")
    response = session.get(f"{BASE_URL}/jewelry", params={'status': 'in_stock'})
    if response.status_code == 200:
        data = response.json()
        in_stock_count = len(data.get('data', []))
        print(f"✅ In-stock jewelry items: {in_stock_count}")
    else:
        print(f"❌ Failed to verify data: {response.status_code}")

if __name__ == "__main__":
    create_test_data()
