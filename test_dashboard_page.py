#!/usr/bin/env python3
"""
Test dashboard page functionality
"""

import requests

BASE_URL = "http://localhost:8000/api"
LOGIN_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

def test_dashboard_functionality():
    print("📊 DASHBOARD PAGE FUNCTIONALITY TEST")
    print("=" * 45)
    
    # Authenticate
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json=LOGIN_CREDENTIALS)
    if response.status_code != 200:
        print("❌ Authentication failed")
        return False
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Test main dashboard endpoint
    print("\n1. Testing main dashboard...")
    response = session.get(f"{BASE_URL}/dashboard")
    if response.status_code == 200:
        dashboard = response.json()
        print(f"✅ Main dashboard: Retrieved successfully")
        print(f"   Keys: {list(dashboard.keys())[:10]}")
        
        # Check for expected metrics
        expected_metrics = [
            'diamonds_in_stock', 'jewelry_in_stock', 'total_sales',
            'open_manufacturing', 'total_inventory_value'
        ]
        found_metrics = [m for m in expected_metrics if m in dashboard]
        print(f"   Expected metrics found: {found_metrics}")
        
    else:
        print(f"❌ Main dashboard failed: {response.status_code}")
        print(f"   Error: {response.text[:200]}")
    
    # Test dashboard summary
    print("\n2. Testing dashboard summary...")
    response = session.get(f"{BASE_URL}/dashboard/summary")
    if response.status_code == 200:
        summary = response.json()
        print(f"✅ Dashboard summary: Retrieved successfully")
        print(f"   Keys: {list(summary.keys())[:10]}")
    else:
        print(f"❌ Dashboard summary failed: {response.status_code}")
    
    # Test analytics endpoints
    print("\n3. Testing analytics endpoints...")
    
    analytics_endpoints = [
        ("/dashboard/sales-analytics", "Sales Analytics"),
        ("/dashboard/inventory-analytics", "Inventory Analytics"),
        ("/dashboard/vendor-analytics", "Vendor Analytics"),
        ("/dashboard/manufacturing-analytics", "Manufacturing Analytics")
    ]
    
    for endpoint, name in analytics_endpoints:
        response = session.get(f"{BASE_URL}{endpoint}")
        if response.status_code == 200:
            analytics = response.json()
            print(f"   ✅ {name}: Retrieved successfully")
            if isinstance(analytics, dict):
                print(f"      Keys: {list(analytics.keys())[:5]}")
        else:
            print(f"   ❌ {name} failed: {response.status_code}")
    
    # Test dashboard with date filters
    print("\n4. Testing dashboard with filters...")
    filters = {
        "start_date": "2024-01-01",
        "end_date": "2024-12-31"
    }
    
    response = session.get(f"{BASE_URL}/dashboard", params=filters)
    if response.status_code == 200:
        filtered_dashboard = response.json()
        print(f"✅ Filtered dashboard: Retrieved successfully")
    else:
        print(f"❌ Filtered dashboard failed: {response.status_code}")
    
    # Test real-time metrics
    print("\n5. Testing real-time metrics...")
    
    # Get current counts from individual endpoints
    endpoints_to_check = [
        ("/diamonds", "diamonds"),
        ("/jewelry", "jewelry"),
        ("/sales", "sales"),
        ("/vendors", "vendors"),
        ("/manufacturing", "manufacturing")
    ]
    
    actual_counts = {}
    for endpoint, name in endpoints_to_check:
        response = session.get(f"{BASE_URL}{endpoint}")
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict) and 'data' in data:
                count = len(data['data'])
            elif isinstance(data, list):
                count = len(data)
            else:
                count = 0
            actual_counts[name] = count
            print(f"   {name}: {count} items")
        else:
            print(f"   ❌ {name} endpoint failed: {response.status_code}")
    
    # Compare with dashboard metrics
    if 'dashboard' in locals():
        print("\n6. Validating dashboard accuracy...")
        
        validations = [
            ('diamonds_in_stock', 'diamonds', 'Diamonds count'),
            ('jewelry_in_stock', 'jewelry', 'Jewelry count'),
            ('total_sales', 'sales', 'Sales count')
        ]
        
        for dashboard_key, endpoint_key, description in validations:
            if dashboard_key in dashboard and endpoint_key in actual_counts:
                dashboard_value = dashboard[dashboard_key]
                actual_value = actual_counts[endpoint_key]
                if dashboard_value == actual_value:
                    print(f"   ✅ {description}: {dashboard_value} (accurate)")
                else:
                    print(f"   ⚠️  {description}: Dashboard={dashboard_value}, Actual={actual_value}")
    
    # Test dashboard performance
    print("\n7. Testing dashboard performance...")
    import time
    start_time = time.time()
    response = session.get(f"{BASE_URL}/dashboard")
    end_time = time.time()
    
    if response.status_code == 200:
        response_time = (end_time - start_time) * 1000  # Convert to milliseconds
        print(f"✅ Dashboard response time: {response_time:.2f}ms")
        if response_time < 1000:  # Less than 1 second
            print("   ✅ Performance: Excellent")
        elif response_time < 3000:  # Less than 3 seconds
            print("   ⚠️  Performance: Acceptable")
        else:
            print("   ❌ Performance: Needs optimization")
    
    print("\n🎯 Dashboard functionality test complete!")
    return True

if __name__ == "__main__":
    success = test_dashboard_functionality()
    if success:
        print("✅ Dashboard functionality testing completed!")
    else:
        print("❌ Some dashboard functionality needs attention.")
