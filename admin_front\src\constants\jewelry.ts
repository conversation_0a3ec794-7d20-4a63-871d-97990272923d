// Jewelry Industry Standards and Constants

export const JEWELRY_STATUSES = [
  { value: 'in_stock', label: 'In Stock', color: 'green' },
  { value: 'sold', label: 'Sold', color: 'blue' },
  { value: 'reserved', label: 'Reserved', color: 'yellow' },
  { value: 'on_hold', label: 'On Hold', color: 'orange' },
  { value: 'damaged', label: 'Damaged', color: 'red' },
  { value: 'under_repair', label: 'Under Repair', color: 'purple' },
  { value: 'discontinued', label: 'Discontinued', color: 'gray' },
  { value: 'out_of_stock', label: 'Out of Stock', color: 'red' }
];

export const JEWELRY_CATEGORIES = [
  { value: 'Ring', label: 'Ring' },
  { value: 'Necklace', label: 'Necklace' },
  { value: 'Earrings', label: 'Earrings' },
  { value: 'Bracelet', label: 'Bracelet' },
  { value: 'Pendant', label: 'Pendant' },
  { value: 'Brooch', label: 'Brooch' },
  { value: 'Anklet', label: 'Anklet' },
  { value: 'Cufflinks', label: 'Cufflinks' },
  { value: 'Tie Pin', label: 'Tie Pin' },
  { value: 'Watch', label: 'Watch' }
];

export const RING_SUBCATEGORIES = [
  { value: 'Engagement', label: 'Engagement' },
  { value: 'Wedding', label: 'Wedding' },
  { value: 'Solitaire', label: 'Solitaire' },
  { value: 'Eternity', label: 'Eternity' },
  { value: 'Cocktail', label: 'Cocktail' },
  { value: 'Statement', label: 'Statement' },
  { value: 'Band', label: 'Band' },
  { value: 'Signet', label: 'Signet' }
];

export const NECKLACE_SUBCATEGORIES = [
  { value: 'Chain', label: 'Chain' },
  { value: 'Choker', label: 'Choker' },
  { value: 'Tennis', label: 'Tennis' },
  { value: 'Statement', label: 'Statement' },
  { value: 'Layered', label: 'Layered' },
  { value: 'Collar', label: 'Collar' },
  { value: 'Lariat', label: 'Lariat' }
];

export const EARRING_SUBCATEGORIES = [
  { value: 'Stud', label: 'Stud' },
  { value: 'Drop', label: 'Drop' },
  { value: 'Hoop', label: 'Hoop' },
  { value: 'Chandelier', label: 'Chandelier' },
  { value: 'Huggie', label: 'Huggie' },
  { value: 'Climber', label: 'Climber' },
  { value: 'Threader', label: 'Threader' }
];

export const METAL_TYPES = [
  { value: 'Gold', label: 'Gold' },
  { value: 'Silver', label: 'Silver' },
  { value: 'Platinum', label: 'Platinum' },
  { value: 'Palladium', label: 'Palladium' },
  { value: 'Titanium', label: 'Titanium' },
  { value: 'Stainless Steel', label: 'Stainless Steel' }
];

export const METAL_PURITIES = [
  { value: '14K', label: '14K Gold' },
  { value: '18K', label: '18K Gold' },
  { value: '22K', label: '22K Gold' },
  { value: '24K', label: '24K Gold' },
  { value: '925', label: '925 Sterling Silver' },
  { value: 'PT950', label: 'PT950 Platinum' },
  { value: 'PT900', label: 'PT900 Platinum' }
];

export const METAL_COLORS = [
  { value: 'Yellow', label: 'Yellow' },
  { value: 'White', label: 'White' },
  { value: 'Rose', label: 'Rose' },
  { value: 'Two-Tone', label: 'Two-Tone' },
  { value: 'Tri-Color', label: 'Tri-Color' }
];

export const CONDITIONS = [
  { value: 'new', label: 'New', color: 'green' },
  { value: 'used', label: 'Used', color: 'yellow' },
  { value: 'refurbished', label: 'Refurbished', color: 'blue' },
  { value: 'vintage', label: 'Vintage', color: 'purple' },
  { value: 'antique', label: 'Antique', color: 'brown' }
];

export const AVAILABILITIES = [
  { value: 'available', label: 'Available', color: 'green' },
  { value: 'reserved', label: 'Reserved', color: 'yellow' },
  { value: 'sold', label: 'Sold', color: 'blue' },
  { value: 'on_hold', label: 'On Hold', color: 'orange' }
];

export const GENDERS = [
  { value: 'men', label: 'Men' },
  { value: 'women', label: 'Women' },
  { value: 'unisex', label: 'Unisex' }
];

export const STYLES = [
  { value: 'modern', label: 'Modern' },
  { value: 'vintage', label: 'Vintage' },
  { value: 'classic', label: 'Classic' },
  { value: 'contemporary', label: 'Contemporary' },
  { value: 'art_deco', label: 'Art Deco' },
  { value: 'bohemian', label: 'Bohemian' },
  { value: 'minimalist', label: 'Minimalist' },
  { value: 'ornate', label: 'Ornate' }
];

export const FINISHES = [
  { value: 'polished', label: 'Polished' },
  { value: 'matte', label: 'Matte' },
  { value: 'brushed', label: 'Brushed' },
  { value: 'hammered', label: 'Hammered' },
  { value: 'textured', label: 'Textured' },
  { value: 'sandblasted', label: 'Sandblasted' }
];

export const SETTING_STYLES = [
  { value: 'prong', label: 'Prong' },
  { value: 'bezel', label: 'Bezel' },
  { value: 'pave', label: 'Pave' },
  { value: 'channel', label: 'Channel' },
  { value: 'tension', label: 'Tension' },
  { value: 'flush', label: 'Flush' },
  { value: 'cluster', label: 'Cluster' },
  { value: 'halo', label: 'Halo' }
];

export const CLASP_TYPES = [
  { value: 'lobster', label: 'Lobster Clasp' },
  { value: 'spring_ring', label: 'Spring Ring' },
  { value: 'toggle', label: 'Toggle' },
  { value: 'magnetic', label: 'Magnetic' },
  { value: 'box', label: 'Box Clasp' },
  { value: 'hook', label: 'Hook & Eye' },
  { value: 'barrel', label: 'Barrel Clasp' }
];

// Validation ranges
export const VALIDATION_RANGES = {
  gross_weight: { min: 0.1, max: 1000 },
  net_weight: { min: 0.1, max: 1000 },
  cost_price: { min: 0, max: 10000000 },
  retail_price: { min: 0, max: 10000000 },
  making_charges: { min: 0, max: 1000000 },
  quantity: { min: 0, max: 10000 },
  minimum_stock: { min: 0, max: 1000 },
  warranty_period: { min: 0, max: 120 },
  rating: { min: 1, max: 5 }
};

// Helper functions
export const getStatusLabel = (status: string) => {
  const statusOption = JEWELRY_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.label : status;
};

export const getStatusColor = (status: string) => {
  const statusOption = JEWELRY_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.color : 'gray';
};

export const getCategoryLabel = (category: string) => {
  const categoryOption = JEWELRY_CATEGORIES.find(c => c.value === category);
  return categoryOption ? categoryOption.label : category;
};

export const getConditionLabel = (condition: string) => {
  const conditionOption = CONDITIONS.find(c => c.value === condition);
  return conditionOption ? conditionOption.label : condition;
};

export const getConditionColor = (condition: string) => {
  const conditionOption = CONDITIONS.find(c => c.value === condition);
  return conditionOption ? conditionOption.color : 'gray';
};

export const getAvailabilityLabel = (availability: string) => {
  const availabilityOption = AVAILABILITIES.find(a => a.value === availability);
  return availabilityOption ? availabilityOption.label : availability;
};

export const getAvailabilityColor = (availability: string) => {
  const availabilityOption = AVAILABILITIES.find(a => a.value === availability);
  return availabilityOption ? availabilityOption.color : 'gray';
};

export const getSubcategoriesByCategory = (category: string) => {
  switch (category) {
    case 'Ring':
      return RING_SUBCATEGORIES;
    case 'Necklace':
      return NECKLACE_SUBCATEGORIES;
    case 'Earrings':
      return EARRING_SUBCATEGORIES;
    default:
      return [];
  }
};

export const calculateProfitMargin = (costPrice: number, retailPrice: number): number => {
  if (!costPrice || !retailPrice) return 0;
  const profit = retailPrice - costPrice;
  return (profit / retailPrice) * 100;
};

export const calculateTotalCost = (costPrice: number, makingCharges: number): number => {
  return (costPrice || 0) + (makingCharges || 0);
};

export const getEffectivePrice = (retailPrice: number, discountPrice?: number): number => {
  return discountPrice || retailPrice;
};

export const formatWeight = (weight: number): string => {
  return `${weight.toFixed(2)} g`;
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2
  }).format(amount);
};

export const formatPercentage = (percentage: number): string => {
  return `${percentage.toFixed(2)}%`;
};

export const generateSKU = (category: string): string => {
  const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 12);
  const categoryCode = category ? category.slice(0, 3).toUpperCase() : 'JWL';
  return `${categoryCode}${timestamp}`;
};

export const isLowStock = (quantity: number, minimumStock: number): boolean => {
  return quantity <= minimumStock;
};

export const getRatingStars = (rating: number): string => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  return '★'.repeat(fullStars) + 
         (hasHalfStar ? '☆' : '') + 
         '☆'.repeat(emptyStars);
};

export const validateJewelryData = (data: any): string[] => {
  const errors: string[] = [];
  
  // Required fields
  if (!data.name) {
    errors.push('Name is required');
  }
  
  if (!data.design_code) {
    errors.push('Design code is required');
  }
  
  if (!data.vendor_id) {
    errors.push('Vendor is required');
  }
  
  if (!data.gross_weight || data.gross_weight <= 0) {
    errors.push('Gross weight must be positive');
  }
  
  if (!data.metal_type) {
    errors.push('Metal type is required');
  }
  
  // Price validations
  if (data.cost_price && data.cost_price < 0) {
    errors.push('Cost price cannot be negative');
  }
  
  if (data.retail_price && data.retail_price < 0) {
    errors.push('Retail price cannot be negative');
  }
  
  if (data.making_charges && data.making_charges < 0) {
    errors.push('Making charges cannot be negative');
  }
  
  if (data.discount_price && data.retail_price && data.discount_price > data.retail_price) {
    errors.push('Discount price cannot exceed retail price');
  }
  
  // Weight validations
  if (data.net_weight && data.gross_weight && data.net_weight > data.gross_weight) {
    errors.push('Net weight cannot exceed gross weight');
  }
  
  // Quantity validations
  if (data.quantity !== undefined && data.quantity < 0) {
    errors.push('Quantity cannot be negative');
  }
  
  if (data.minimum_stock !== undefined && data.minimum_stock < 0) {
    errors.push('Minimum stock cannot be negative');
  }
  
  // Rating validation
  if (data.rating !== undefined && (data.rating < 1 || data.rating > 5)) {
    errors.push('Rating must be between 1 and 5');
  }
  
  return errors;
};
