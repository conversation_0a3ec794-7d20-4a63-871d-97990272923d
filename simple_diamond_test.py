#!/usr/bin/env python3
"""
Simple diamond test
"""

import requests

BASE_URL = "http://localhost:8000/api"

def test():
    print("Testing diamond functionality...")
    
    # Login
    response = requests.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if response.status_code != 200:
        print("Login failed")
        return
    
    token = response.json().get('access_token')
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test GET diamonds
    response = requests.get(f"{BASE_URL}/diamonds", headers=headers)
    print(f"GET diamonds: {response.status_code}")
    
    # Test GET shapes
    response = requests.get(f"{BASE_URL}/shapes", headers=headers)
    print(f"GET shapes: {response.status_code}")
    if response.status_code == 200:
        shapes = response.json()
        print(f"Found {len(shapes)} shapes")
    
    # Test GET vendors
    response = requests.get(f"{BASE_URL}/vendors", headers=headers)
    print(f"GET vendors: {response.status_code}")
    if response.status_code == 200:
        vendors = response.json()
        print(f"Found {len(vendors)} vendors")

if __name__ == "__main__":
    test()
