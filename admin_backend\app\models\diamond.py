from app import db
from datetime import date, datetime
from sqlalchemy.orm import relationship

class Shape(db.Model):
    __tablename__ = 'shapes'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    
    @classmethod
    def create_default_shapes(cls):
        """Create default shapes if they don't exist."""
        predefined_shapes = [
            'Round', 'Princess', 'Cushion', 'Emerald',
            'Oval', 'Pear', '<PERSON><PERSON>', '<PERSON><PERSON>',
            'Radian<PERSON>', 'Heart', 'Trillion', 'Baguette',
            'Portugal', 'Old Mine', 'Old European', 'Rose',
            'Hexagon', 'Octagon', 'Briolette', 'Tapered Baguette',
            'Bullet', 'Kite', 'Shield', 'Half Moon',
            'Lozenge', 'Triangle', 'Fan', 'Trapezoid',
            'French Cut', 'Cabochon', 'Star', 'Horse Head',
            'Navette', 'Antique Cushion', 'Bead'
        ]

        for shape_name in predefined_shapes:
            if not cls.query.filter_by(name=shape_name).first():
                shape = cls(name=shape_name)
                db.session.add(shape)

        db.session.commit()

class Manufacturing(db.Model):
    __tablename__ = 'manufacturing'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    diamonds = relationship('Diamond', backref='manufacturing')

class Jewelry(db.Model):
    __tablename__ = 'jewelry'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    diamonds = relationship('Diamond', backref='jewelry')

class Diamond(db.Model):
    __tablename__ = 'diamonds'
    id = db.Column(db.Integer, primary_key=True)

    # Basic Diamond Properties
    shape_id = db.Column(db.Integer, db.ForeignKey('shapes.id'), nullable=False)
    shape = relationship('Shape', backref='diamonds')
    carat = db.Column(db.Float, nullable=False)

    # 4Cs - Industry Standard Grading
    color = db.Column(db.String(5), nullable=False)  # D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z
    clarity = db.Column(db.String(10), nullable=False)  # FL, IF, VVS1, VVS2, VS1, VS2, SI1, SI2, SI3, I1, I2, I3
    cut_grade = db.Column(db.String(20))  # Excellent, Very Good, Good, Fair, Poor

    # Additional Grading Properties
    polish = db.Column(db.String(20))  # Excellent, Very Good, Good, Fair, Poor
    symmetry = db.Column(db.String(20))  # Excellent, Very Good, Good, Fair, Poor
    fluorescence = db.Column(db.String(30))  # None, Faint, Medium, Strong, Very Strong
    fluorescence_color = db.Column(db.String(20))  # Blue, Yellow, White, etc.

    # Measurements
    length_mm = db.Column(db.Float)
    width_mm = db.Column(db.Float)
    depth_mm = db.Column(db.Float)
    depth_percent = db.Column(db.Float)  # Depth percentage
    table_percent = db.Column(db.Float)  # Table percentage
    girdle = db.Column(db.String(50))  # Thin to Thick, etc.
    culet = db.Column(db.String(20))  # None, Very Small, Small, Medium, Large

    # Certification
    certificate_no = db.Column(db.String(100), unique=True, nullable=False)
    certification_lab = db.Column(db.String(50))  # GIA, AGS, EGL, GCAL, etc.
    certificate_date = db.Column(db.Date)
    certificate_url = db.Column(db.String(500))  # Link to certificate

    # Pricing
    cost_price = db.Column(db.Float)  # Purchase cost
    retail_price = db.Column(db.Float)  # Retail selling price
    market_value = db.Column(db.Float)  # Current market value
    last_valuation_date = db.Column(db.Date)

    # Inventory Management
    quantity = db.Column(db.Integer, nullable=False, default=1)
    reserved_quantity = db.Column(db.Integer, default=0)
    available_quantity = db.Column(db.Integer, default=1)  # quantity - reserved_quantity
    minimum_stock = db.Column(db.Integer, default=1)

    # Status and Tracking
    status = db.Column(db.String(20), default='in_stock')  # in_stock, reserved, sold, manufacturing, damaged
    location = db.Column(db.String(100))  # Storage location
    notes = db.Column(db.Text)  # Additional notes

    # Relationships
    manufacturing_id = db.Column(db.Integer, db.ForeignKey('manufacturing.id'))
    jewelry_id = db.Column(db.Integer, db.ForeignKey('jewelry.id'))
    vendor_id = db.Column(db.Integer, db.ForeignKey('vendors.id'), nullable=True)
    vendor = db.relationship('Vendor', backref='diamonds')

    # Dates
    purchase_date = db.Column(db.Date, default=date.today)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    # Legacy field for backward compatibility
    size_mm = db.Column(db.String(50))  # Deprecated, use length_mm, width_mm, depth_mm instead

    # Industry Standard Constants
    VALID_COLORS = ['D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    VALID_CLARITIES = ['FL', 'IF', 'VVS1', 'VVS2', 'VS1', 'VS2', 'SI1', 'SI2', 'SI3', 'I1', 'I2', 'I3']
    VALID_CUT_GRADES = ['Excellent', 'Very Good', 'Good', 'Fair', 'Poor']
    VALID_POLISH_SYMMETRY = ['Excellent', 'Very Good', 'Good', 'Fair', 'Poor']
    VALID_FLUORESCENCE = ['None', 'Faint', 'Medium', 'Strong', 'Very Strong']
    VALID_FLUORESCENCE_COLORS = ['Blue', 'Yellow', 'White', 'Green', 'Orange', 'Red']
    VALID_STATUSES = ['in_stock', 'reserved', 'sold', 'manufacturing', 'damaged', 'lost']
    VALID_CERTIFICATION_LABS = ['GIA', 'AGS', 'EGL', 'GCAL', 'SSEF', 'Gübelin', 'AIGS', 'GRS', 'Lotus']

    def __init__(self, **kwargs):
        super(Diamond, self).__init__(**kwargs)
        # Auto-calculate available quantity
        self.update_available_quantity()

    def update_available_quantity(self):
        """Update available quantity based on total and reserved quantities."""
        if self.quantity is not None and self.reserved_quantity is not None:
            self.available_quantity = max(0, self.quantity - self.reserved_quantity)

    def reserve_quantity(self, quantity):
        """Reserve a specific quantity of diamonds."""
        if quantity <= 0:
            raise ValueError("Quantity to reserve must be positive")
        if quantity > self.available_quantity:
            raise ValueError(f"Cannot reserve {quantity} diamonds. Only {self.available_quantity} available.")

        self.reserved_quantity = (self.reserved_quantity or 0) + quantity
        self.update_available_quantity()

        if self.available_quantity == 0:
            self.status = 'reserved'

    def release_reservation(self, quantity):
        """Release a reserved quantity back to available stock."""
        if quantity <= 0:
            raise ValueError("Quantity to release must be positive")
        if quantity > (self.reserved_quantity or 0):
            raise ValueError(f"Cannot release {quantity} diamonds. Only {self.reserved_quantity} reserved.")

        self.reserved_quantity = max(0, (self.reserved_quantity or 0) - quantity)
        self.update_available_quantity()

        if self.reserved_quantity == 0 and self.status == 'reserved':
            self.status = 'in_stock'

    def is_low_stock(self):
        """Check if diamond is below minimum stock level."""
        return self.available_quantity <= self.minimum_stock

    def get_profit_margin(self):
        """Calculate profit margin percentage."""
        if self.cost_price and self.retail_price and self.cost_price > 0:
            return ((self.retail_price - self.cost_price) / self.cost_price) * 100
        return None

    def get_profit_amount(self):
        """Calculate profit amount."""
        if self.cost_price and self.retail_price:
            return self.retail_price - self.cost_price
        return None

    @classmethod
    def validate_color(cls, color):
        """Validate diamond color grade."""
        return color in cls.VALID_COLORS

    @classmethod
    def validate_clarity(cls, clarity):
        """Validate diamond clarity grade."""
        return clarity in cls.VALID_CLARITIES

    @classmethod
    def validate_cut_grade(cls, cut_grade):
        """Validate diamond cut grade."""
        return cut_grade in cls.VALID_CUT_GRADES

    @classmethod
    def validate_fluorescence(cls, fluorescence):
        """Validate diamond fluorescence."""
        return fluorescence in cls.VALID_FLUORESCENCE

    @classmethod
    def validate_certification_lab(cls, lab):
        """Validate certification lab."""
        return lab in cls.VALID_CERTIFICATION_LABS

    def to_dict(self):
        """Convert diamond to dictionary for API responses."""
        return {
            'id': self.id,
            'shape': self.shape.name if self.shape else None,
            'shape_id': self.shape_id,
            'carat': self.carat,
            'color': self.color,
            'clarity': self.clarity,
            'cut_grade': self.cut_grade,
            'polish': self.polish,
            'symmetry': self.symmetry,
            'fluorescence': self.fluorescence,
            'fluorescence_color': self.fluorescence_color,
            'length_mm': self.length_mm,
            'width_mm': self.width_mm,
            'depth_mm': self.depth_mm,
            'depth_percent': self.depth_percent,
            'table_percent': self.table_percent,
            'girdle': self.girdle,
            'culet': self.culet,
            'certificate_no': self.certificate_no,
            'certification_lab': self.certification_lab,
            'certificate_date': self.certificate_date.isoformat() if self.certificate_date else None,
            'certificate_url': self.certificate_url,
            'cost_price': self.cost_price,
            'retail_price': self.retail_price,
            'market_value': self.market_value,
            'last_valuation_date': self.last_valuation_date.isoformat() if self.last_valuation_date else None,
            'quantity': self.quantity,
            'reserved_quantity': self.reserved_quantity,
            'available_quantity': self.available_quantity,
            'minimum_stock': self.minimum_stock,
            'status': self.status,
            'location': self.location,
            'notes': self.notes,
            'vendor_id': self.vendor_id,
            'vendorName': self.vendor.name if self.vendor else None,
            'purchase_date': self.purchase_date.isoformat() if self.purchase_date else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'size_mm': self.size_mm,  # Legacy field
            'profit_margin': self.get_profit_margin(),
            'profit_amount': self.get_profit_amount(),
            'is_low_stock': self.is_low_stock()
        }

# Ensure `db` is correctly initialized in the app context.
