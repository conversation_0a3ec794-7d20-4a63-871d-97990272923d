import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Download, CheckCircle, Eye } from 'lucide-react';
import { api } from '../../lib/api';
import { Sale } from '../../types';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Select from '../../components/ui/Select';
import Modal from '../../components/ui/Modal';
import { LoadingState, ErrorState, EmptyState } from '../../components/DataStates';
import SalesForm from './SalesForm';
import SalesDetails from './SalesDetails';
import toast from 'react-hot-toast';

const SalesList: React.FC = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [paymentStatusFilter, setPaymentStatusFilter] = useState('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');

  const queryClient = useQueryClient();

  const { data: sales, isLoading, error, refetch } = useQuery({
    queryKey: ['sales', { 
      search: searchTerm, 
      payment_status: paymentStatusFilter,
      date_from: dateFrom,
      date_to: dateTo
    }],
    queryFn: async (): Promise<Sale[]> => {
      const params: any = {};
      if (searchTerm) params.customer_name = searchTerm;
      if (paymentStatusFilter) params.payment_status = paymentStatusFilter;
      if (dateFrom) params.date_from = dateFrom;
      if (dateTo) params.date_to = dateTo;
      
      const response = await api.sales.list(params);
      return response?.data || [];
    },
    retry: 1
  });

  // Enhanced error handling for API calls
  const markPaidMutation = useMutation({
    mutationFn: async (id: number) => {
      await api.sales.updatePaymentStatus(id, 'paid');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sales'] });
      toast.success('Sale marked as paid');
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to mark sale as paid';
      toast.error(`Error: ${errorMessage}`);
    }
  });

  const downloadInvoiceMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await api.get(`/sales/invoice/${id}/`, {
        responseType: 'blob',
      });
      return response;
    },
    onSuccess: (response, saleId) => {
      if (response?.data) {
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        const contentDisposition = response.headers?.['content-disposition'];
        let filename = `invoice_${saleId}.pdf`;
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename="(.+)"/);
          if (filenameMatch) {
            filename = filenameMatch[1];
          }
        }
        link.setAttribute('download', filename);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
        toast.success('Invoice downloaded successfully');
      }
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to download invoice';
      toast.error(`Error: ${errorMessage}`);
    }
  });

  const handleMarkPaid = async (sale: Sale) => {
    if (window.confirm('Are you sure you want to mark this sale as paid?')) {
      markPaidMutation.mutate(sale.id);
    }
  };

  const handleDownloadInvoice = (sale: Sale) => {
    downloadInvoiceMutation.mutate(sale.id);
  };

  const handleViewDetails = (sale: Sale) => {
    setSelectedSale(sale);
    setIsDetailsModalOpen(true);
  };

  const paymentStatusOptions = [
    { value: 'paid', label: 'Paid' },
    { value: 'unpaid', label: 'Unpaid' }
  ];

  if (isLoading) {
    return <LoadingState message="Loading sales data..." />;
  }

  if (error) {
    return (
      <ErrorState 
        title="Failed to load sales data"
        onRetry={refetch}
      />
    );
  }

  if (!sales || sales.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Sales</h1>
            <p className="text-gray-600">Manage jewelry sales and invoices</p>
          </div>
          <Button onClick={() => setIsAddModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Sale
          </Button>
        </div>
        
        <EmptyState
          title="No sales found"
          description="Start by recording your first jewelry sale"
          action={{
            label: "New Sale",
            onClick: () => setIsAddModalOpen(true)
          }}
        />
        
        {/* Modals */}
        <Modal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          title="New Sale"
        >
          <SalesForm onSuccess={() => setIsAddModalOpen(false)} />
        </Modal>
      </div>
    );
  }

  // Refactored table row rendering logic
  const renderTableRows = (sales: Sale[]) => (
    sales.map((sale) => (
      <tr key={sale.id} className="hover:bg-gray-50">
        <td className="px-6 py-4 whitespace-nowrap">
          <div>
            <div className="text-sm font-medium text-gray-900">{sale.invoice_no}</div>
            <div className="text-sm text-gray-500">
              {new Date(sale.sale_date).toLocaleDateString()}
            </div>
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className="text-sm text-gray-900">{sale.customer_name}</span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div>
            <div className="text-sm font-medium text-gray-900">
              {sale.jewelry?.name}
            </div>
            <div className="text-sm text-gray-500">
              {sale.jewelry?.design_code}
            </div>
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className="text-sm font-medium text-gray-900">
            ₹{sale.total_amount.toLocaleString()}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            sale.payment_status === 'paid' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-red-100 text-red-800'
          }`}>
            {sale.payment_status}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleViewDetails(sale)}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDownloadInvoice(sale)}
              isLoading={downloadInvoiceMutation.isPending}
            >
              <Download className="h-4 w-4" />
            </Button>
            {sale.payment_status === 'unpaid' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleMarkPaid(sale)}
                isLoading={markPaidMutation.isPending}
              >
                <CheckCircle className="h-4 w-4 text-green-600" />
              </Button>
            )}
          </div>
        </td>
      </tr>
    ))
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Sales</h1>
          <p className="text-gray-600">Manage jewelry sales and invoices</p>
        </div>
        <Button onClick={() => setIsAddModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Record Sale
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Input
            placeholder="Search by customer name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full"
          />
          <Select
            options={paymentStatusOptions}
            value={paymentStatusFilter}
            onChange={(e) => setPaymentStatusFilter(e.target.value)}
            placeholder="Payment status"
          />
          <Input
            type="date"
            placeholder="From date"
            value={dateFrom}
            onChange={(e) => setDateFrom(e.target.value)}
          />
          <Input
            type="date"
            placeholder="To date"
            value={dateTo}
            onChange={(e) => setDateTo(e.target.value)}
          />
          <Button
            variant="ghost"
            onClick={() => {
              setSearchTerm('');
              setPaymentStatusFilter('');
              setDateFrom('');
              setDateTo('');
            }}
          >
            Clear Filters
          </Button>
        </div>
      </Card>

      {/* Sales List */}
      <Card padding={false}>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Invoice Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Jewelry Item
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {renderTableRows(Array.isArray(sales) ? sales : [])}
            </tbody>
          </table>
          {sales?.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No sales found matching your criteria.</p>
            </div>
          )}
        </div>
      </Card>

      {/* Modals */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Record New Sale"
        size="lg"
      >
        <SalesForm
          onSuccess={() => {
            setIsAddModalOpen(false);
            queryClient.invalidateQueries({ queryKey: ['sales'] });
            queryClient.invalidateQueries({ queryKey: ['jewelry'] });
          }}
        />
      </Modal>

      <Modal
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedSale(null);
        }}
        title="Sale Details"
        size="lg"
      >
        {selectedSale && (
          <SalesDetails sale={selectedSale} />
        )}
      </Modal>
    </div>
  );
};

export default SalesList;