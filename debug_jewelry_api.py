#!/usr/bin/env python3
"""
Debug jewelry API responses
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def debug_jewelry():
    print("🔍 Debugging Jewelry API...")
    
    # Login first
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Test jewelry endpoint
    print(f"\n📊 Testing /jewelry...")
    response = session.get(f"{BASE_URL}/jewelry")
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"Response structure: {type(data)}")
            if isinstance(data, dict):
                print(f"Keys: {list(data.keys())}")
            elif isinstance(data, list):
                print(f"List length: {len(data)}")
                if len(data) > 0:
                    print(f"First item keys: {list(data[0].keys()) if isinstance(data[0], dict) else 'Not a dict'}")
            print(f"Sample data: {json.dumps(data, indent=2)[:500]}...")
        except Exception as e:
            print(f"JSON parse error: {e}")
            print(f"Raw response: {response.text[:200]}")
    else:
        print(f"Error: {response.text[:200]}")
    
    # Test with status filter
    print(f"\n📊 Testing /jewelry?status=in_stock...")
    response = session.get(f"{BASE_URL}/jewelry", params={'status': 'in_stock'})
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"Filtered response structure: {type(data)}")
            print(f"Sample data: {json.dumps(data, indent=2)[:300]}...")
        except Exception as e:
            print(f"JSON parse error: {e}")

if __name__ == "__main__":
    debug_jewelry()
