import requests
from tests.conftest import get_tokens

def test_upload_image(base_url, test_user):
    access_token, _ = get_tokens(base_url, test_user)
    headers = {"Authorization": f"Bearer {access_token}"}
    files = {"image": ("test.png", b"fake image data", "image/png")}
    r = requests.post(f"{base_url}/upload/image", files=files, headers=headers, timeout=10)
    assert r.status_code == 200
    assert "image_path" in r.json()
