import requests
import json

try:
    # Login
    login_response = requests.post('http://localhost:8000/api/auth/login', 
        json={'email': '<EMAIL>', 'password': '<PERSON><PERSON>@109'})
    
    if login_response.status_code == 200:
        token = login_response.json()['access_token']
        auth_header = {'Authorization': f'Bearer {token}'}
        
        # Test 1: Basic diamond listing
        print("Test 1: Basic diamond listing")
        diamonds_response = requests.get('http://localhost:8000/api/diamonds', 
            headers=auth_header)
        
        if diamonds_response.status_code == 200:
            diamonds_data = diamonds_response.json()
            print(f'Total diamonds: {diamonds_data.get("total", 0)}')
            
        # Test 2: Filter by shape
        print("\nTest 2: Filter by shape (Round)")
        shape_filter_response = requests.get('http://localhost:8000/api/diamonds?shape=Round', 
            headers=auth_header)
        
        if shape_filter_response.status_code == 200:
            shape_data = shape_filter_response.json()
            print(f'Round diamonds: {shape_data.get("total", 0)}')
            
        # Test 3: Filter by carat range
        print("\nTest 3: Filter by carat range (1.0-2.0)")
        carat_filter_response = requests.get('http://localhost:8000/api/diamonds?min_carat=1.0&max_carat=2.0', 
            headers=auth_header)
        
        if carat_filter_response.status_code == 200:
            carat_data = carat_filter_response.json()
            print(f'Diamonds 1.0-2.0 carats: {carat_data.get("total", 0)}')
            
        # Test 4: Filter by color
        print("\nTest 4: Filter by color (H)")
        color_filter_response = requests.get('http://localhost:8000/api/diamonds?color=H', 
            headers=auth_header)
        
        if color_filter_response.status_code == 200:
            color_data = color_filter_response.json()
            print(f'H color diamonds: {color_data.get("total", 0)}')
            
        # Test 5: Filter by clarity
        print("\nTest 5: Filter by clarity (VS2)")
        clarity_filter_response = requests.get('http://localhost:8000/api/diamonds?clarity=VS2', 
            headers=auth_header)
        
        if clarity_filter_response.status_code == 200:
            clarity_data = clarity_filter_response.json()
            print(f'VS2 clarity diamonds: {clarity_data.get("total", 0)}')
            
        # Test 6: Combined filters
        print("\nTest 6: Combined filters (Round, 1.0-2.0 carats)")
        combined_filter_response = requests.get('http://localhost:8000/api/diamonds?shape=Round&min_carat=1.0&max_carat=2.0', 
            headers=auth_header)
        
        if combined_filter_response.status_code == 200:
            combined_data = combined_filter_response.json()
            print(f'Round diamonds 1.0-2.0 carats: {combined_data.get("total", 0)}')
            
        # Test 7: Search by certificate number
        print("\nTest 7: Search by certificate number")
        # Get a certificate number from the first diamond
        if diamonds_data.get('data') and len(diamonds_data['data']) > 0:
            cert_no = diamonds_data['data'][0].get('certificate_no', '')
            if cert_no:
                cert_search_response = requests.get(f'http://localhost:8000/api/diamonds?certificate_no={cert_no}', 
                    headers=auth_header)
                
                if cert_search_response.status_code == 200:
                    cert_data = cert_search_response.json()
                    print(f'Diamonds with certificate {cert_no}: {cert_data.get("total", 0)}')
            else:
                print("No certificate number found in sample data")
                
        # Test 8: Export CSV functionality
        print("\nTest 8: Export CSV functionality")
        csv_response = requests.get('http://localhost:8000/api/diamonds/export', 
            headers=auth_header)
        
        if csv_response.status_code == 200:
            print(f'CSV export successful, content type: {csv_response.headers.get("Content-Type")}')
            print(f'First 100 characters of CSV: {csv_response.text[:100]}...')
        else:
            print(f'CSV export failed: {csv_response.status_code}')
            print(csv_response.text)
            
    else:
        print('Login failed:', login_response.status_code)
        print(login_response.text)
        
except Exception as e:
    print('Error:', e)
