# API Endpoints for Inventory & Sales System

All endpoints require authentication (JWT/session). All responses are JSON unless otherwise noted.

---

## 1. Diamond Inventory

| Method | Route                        | Description                |
|--------|------------------------------|----------------------------|
| GET    | /api/v1/diamonds             | List all diamonds (with filters) |
| POST   | /api/v1/diamonds             | Create a new diamond       |
| GET    | /api/v1/diamonds/:id         | Get diamond details        |
| PUT    | /api/v1/diamonds/:id         | Update diamond             |
| DELETE | /api/v1/diamonds/:id         | Delete diamond             |
| PATCH  | /api/v1/diamonds/:id/deduct  | Deduct quantity (used in manufacturing) |

**Sample POST/PUT JSON:**
```json
{
  "shape": "Round",
  "size_mm": 5.0,
  "carat": 1.2,
  "clarity": "VS1",
  "color": "D",
  "certificate_no": "GIA123456",
  "quantity": 10,
  "purchase_date": "2025-06-21",
  "status": "in_stock"
}
```

---

## 2. Vendor Management

| Method | Route                | Description         |
|--------|----------------------|---------------------|
| GET    | /api/v1/vendors      | List all vendors    |
| POST   | /api/v1/vendors      | Create vendor       |
| GET    | /api/v1/vendors/:id  | Get vendor details  |
| PUT    | /api/v1/vendors/:id  | Update vendor       |
| DELETE | /api/v1/vendors/:id  | Delete vendor       |

**Sample POST/PUT JSON:**
```json
{
  "name": "ABC Jewels",
  "gst_number": "24ABCDE1234F1Z5",
  "contact_number": "9876543210",
  "address": "123, Main Road, Surat"
}
```

---

## 3. Manufacturing Requests

| Method | Route                                 | Description                        |
|--------|---------------------------------------|------------------------------------|
| GET    | /api/v1/manufacturing                 | List all manufacturing requests    |
| POST   | /api/v1/manufacturing                 | Create new manufacturing request   |
| GET    | /api/v1/manufacturing/:id             | Get request details                |
| PUT    | /api/v1/manufacturing/:id/complete    | Mark request as completed (deduct diamonds) |
| GET    | /api/v1/manufacturing/history         | View request history               |

**Sample POST JSON:**
```json
{
  "vendor_id": 1,
  "sent_date": "2025-06-21",
  "expected_return_date": "2025-07-01",
  "diamonds": [
    {"diamond_id": 2, "quantity": 3},
    {"diamond_id": 5, "quantity": 1}
  ]
}
```

---

## 4. Jewelry Items

| Method | Route                        | Description                |
|--------|------------------------------|----------------------------|
| GET    | /api/v1/jewelry              | List all jewelry items (with filters) |
| POST   | /api/v1/jewelry              | Create new jewelry item    |
| GET    | /api/v1/jewelry/:id          | Get jewelry item details   |
| PUT    | /api/v1/jewelry/:id          | Update jewelry item        |
| DELETE | /api/v1/jewelry/:id          | Delete jewelry item        |
| POST   | /api/v1/jewelry/:id/image    | Upload jewelry image       |
| PATCH  | /api/v1/jewelry/:id/mark-sold| Mark jewelry as sold       |
| PATCH  | /api/v1/jewelry/:id/deduct-diamonds | Deduct diamonds used in a jewelry item |

**Sample POST/PUT JSON:**
```json
{
  "name": "Diamond Ring",
  "design_code": "RNG2025",
  "vendor_id": 1,
  "gross_weight": 5.5,
  "metal_type": "Gold",
  "received_date": "2025-06-21",
  "status": "in_stock",
  "diamonds": [
    {"diamond_id": 2, "quantity": 1}
  ]
}
```
**Image Upload:**
- `POST /api/v1/jewelry/:id/image`
- Content-Type: `multipart/form-data`
- Field: `image`

---

## 5. Sales & Invoicing

| Method | Route                        | Description                |
|--------|------------------------------|----------------------------|
| GET    | /api/v1/sales                | List all sales             |
| POST   | /api/v1/sales                | Create new sale            |
| GET    | /api/v1/sales/:id            | Get sale details           |
| GET    | /api/v1/sales/invoice/:id    | Download PDF invoice       |
| PATCH  | /api/v1/sales/:id/mark-paid  | Mark sale as paid          |

**Sample POST JSON:**
```json
{
  "invoice_no": "INV-2025-001",
  "customer_name": "John Doe",
  "sale_date": "2025-06-21",
  "total_amount": 150000,
  "payment_status": "unpaid",
  "jewelry_id": 3
}
```

---

## 6. Inventory Summary & Dashboard

| Method | Route                        | Description                |
|--------|------------------------------|----------------------------|
| GET    | /api/v1/dashboard/summary    | Get inventory and sales summary |
| GET    | /api/v1/dashboard/sales      | Get sales statistics       |
| GET    | /api/v1/dashboard/stock      | Get current stock summary  |

---

## 7. Image/File Uploads

| Method | Route                        | Description                |
|--------|------------------------------|----------------------------|
| POST   | /api/v1/upload/image         | Upload image (generic)     |

- All endpoints require authentication.
- All responses are JSON unless otherwise noted (e.g., PDF invoice download).
- For file/image upload, use `multipart/form-data`.
