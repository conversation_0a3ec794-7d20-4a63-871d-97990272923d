#!/usr/bin/env python3
"""
Debug JWT authentication issues in detail
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"
LOGIN_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

def debug_jwt():
    print("🔍 Debugging JWT Authentication Issues")
    print("=" * 60)
    
    # Step 1: Login and get token
    print("1. Getting authentication token...")
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=LOGIN_CREDENTIALS)
        print(f"   Login Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            print(f"   Token received: {len(token)} characters")
            print(f"   Token starts with: {token[:50]}...")
            
            if token:
                # Step 2: Test different header formats
                print("\n2. Testing different Authorization header formats...")
                
                test_cases = [
                    ("Bearer {token}", f"Bearer {token}"),
                    ("bearer {token}", f"bearer {token}"),
                    ("JWT {token}", f"JWT {token}"),
                    ("{token}", token),
                ]
                
                for case_name, header_value in test_cases:
                    print(f"\n   Testing: {case_name}")
                    headers = {'Authorization': header_value}
                    
                    # Test with jewelry endpoint (known to work without auth)
                    try:
                        response = requests.get(f"{BASE_URL}/jewelry", headers=headers)
                        print(f"     Jewelry Status: {response.status_code}")
                        if response.status_code != 200:
                            print(f"     Response: {response.text[:100]}...")
                    except Exception as e:
                        print(f"     Error: {str(e)}")
                    
                    # Test with diamonds endpoint (requires auth)
                    try:
                        response = requests.get(f"{BASE_URL}/diamonds", headers=headers)
                        print(f"     Diamonds Status: {response.status_code}")
                        if response.status_code == 200:
                            result = response.json()
                            print(f"     Success! Found {len(result.get('data', []))} diamonds")
                        else:
                            print(f"     Response: {response.text[:100]}...")
                    except Exception as e:
                        print(f"     Error: {str(e)}")
                
                # Step 3: Test with correct Bearer format
                print(f"\n3. Testing with standard Bearer format...")
                headers = {'Authorization': f'Bearer {token}'}
                
                endpoints = [
                    '/diamonds',
                    '/vendors', 
                    '/manufacturing',
                    '/sales',
                    '/dashboard'
                ]
                
                for endpoint in endpoints:
                    try:
                        response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
                        print(f"   {endpoint}: {response.status_code}")
                        
                        if response.status_code == 200:
                            result = response.json()
                            if isinstance(result, list):
                                print(f"     Found {len(result)} items")
                            elif isinstance(result, dict):
                                if 'data' in result:
                                    print(f"     Found {len(result['data'])} items")
                                else:
                                    print(f"     Keys: {list(result.keys())[:5]}")
                        else:
                            error_text = response.text[:150]
                            print(f"     Error: {error_text}...")
                            
                    except Exception as e:
                        print(f"     Exception: {str(e)}")
                
        else:
            print("   ❌ Login failed")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")

if __name__ == "__main__":
    debug_jwt()
