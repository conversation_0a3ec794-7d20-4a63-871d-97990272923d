import React, { useState } from 'react';
import { useQuery, useMutation, QueryClient } from '@tanstack/react-query';
import { api } from '../../lib/api';
import Button from '../../components/ui/Button';
import Table from '../../components/ui/Table';
import Modal from '../../components/ui/Modal';
import toast from 'react-hot-toast';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

const queryClient = new QueryClient();

const UsersList: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const { data: users, isLoading, error } = useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      const response = await api.users.list();
      if (!response) throw new Error('Failed to fetch users');
      return response.data;
    },
  });

  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      await api.users.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast.success('User deleted successfully');
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to delete user';
      toast.error(`Error: ${errorMessage}`);
    },
  });

  const saveMutation = useMutation({
    mutationFn: async (user: User) => {
      await api.users.update(user.id, user);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast.success('User updated successfully');
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.message || 'Failed to update user';
      toast.error(`Error: ${errorMessage}`);
    },
  });

  const handleDelete = (user: User) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      deleteMutation.mutate(user.id);
    }
  };

  const handleEdit = (user: User) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  const handleSave = () => {
    if (selectedUser) {
      saveMutation.mutate(selectedUser);
      setIsModalOpen(false);
    }
  };

  if (isLoading) {
    return <div className="spinner">Loading users...</div>;
  }

  if (error) {
    return <div className="error">Error loading users</div>;
  }

  return (
    <div>
      <h1>Users</h1>
      <Table
        data={users}
        columns={[
          { key: 'name', label: 'Name' },
          { key: 'email', label: 'Email' },
          { key: 'role', label: 'Role' },
        ]}
        renderRow={(user: User) => (
          <>
            <td>{user.name}</td>
            <td>{user.email}</td>
            <td>{user.role}</td>
            <td>
              <Button onClick={() => handleEdit(user)}>Edit</Button>
              <Button onClick={() => handleDelete(user)}>Delete</Button>
            </td>
          </>
        )}
      />
      <Modal isOpen={isModalOpen} title="Edit User" onClose={() => setIsModalOpen(false)}>
        <div>
          <h2>Edit User</h2>
          <form onSubmit={(e) => { e.preventDefault(); handleSave(); }}>
            <label>
              Name:
              <input
                type="text"
                value={selectedUser?.name || ''}
                onChange={(e) => setSelectedUser({ ...selectedUser!, name: e.target.value })}
              />
            </label>
            <label>
              Email:
              <input
                type="email"
                value={selectedUser?.email || ''}
                onChange={(e) => setSelectedUser({ ...selectedUser!, email: e.target.value })}
              />
            </label>
            <label>
              Role:
              <input
                type="text"
                value={selectedUser?.role || ''}
                onChange={(e) => setSelectedUser({ ...selectedUser!, role: e.target.value })}
              />
            </label>
            <Button type="submit">Save</Button>
          </form>
        </div>
      </Modal>
    </div>
  );
};

export default UsersList;
