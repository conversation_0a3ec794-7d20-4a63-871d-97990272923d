#!/usr/bin/env python3
"""
Create test jewelry item in database
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'admin_backend'))

from app import create_app, db
from app.models.jewelry import JewelryItem
from app.models.vendor import Vendor
from datetime import date

def create_test_jewelry():
    print("🔧 Creating Test Jewelry Item")
    print("=" * 30)
    
    app = create_app()
    with app.app_context():
        # Get or create a vendor
        vendor = Vendor.query.first()
        if not vendor:
            vendor = Vendor(
                name="Test Jewelry Vendor",
                gst_number="29AAAAA12345A1Z5",
                contact_number="+91-9876543210",
                address="Test Address"
            )
            db.session.add(vendor)
            db.session.commit()
            print(f"Created vendor: {vendor.name}")
        else:
            print(f"Using existing vendor: {vendor.name}")
        
        # Create jewelry item with minimal required fields
        jewelry = JewelryItem(
            name="Test Ring",
            vendor_id=vendor.id,
            gross_weight=5.0,
            metal_type="Gold",
            received_date=date.today(),
            status="in_stock"
        )
        
        # Add optional fields that exist in the model
        try:
            jewelry.design_code = "TR001"
            jewelry.category = "Ring"
            jewelry.cost_price = 25000
            jewelry.retail_price = 40000
        except Exception as e:
            print(f"Warning: Could not set optional fields: {str(e)}")
        
        db.session.add(jewelry)
        db.session.commit()
        
        print(f"Created jewelry item: ID {jewelry.id}, Name: {jewelry.name}")
        
        # Test the to_dict method
        try:
            jewelry_dict = jewelry.to_dict()
            print(f"to_dict() success: {len(jewelry_dict)} fields")
            print(f"Sample fields: {list(jewelry_dict.keys())[:10]}")
        except Exception as e:
            print(f"Error in to_dict(): {str(e)}")
        
        return jewelry.id

if __name__ == "__main__":
    jewelry_id = create_test_jewelry()
    print(f"Test jewelry created with ID: {jewelry_id}")
