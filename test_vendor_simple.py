#!/usr/bin/env python3
"""
Simple vendor test
"""

import requests
import time
import random

BASE_URL = "http://localhost:8000/api"

def test():
    print("Testing vendor functionality...")
    
    # Login
    response = requests.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if response.status_code != 200:
        print("Login failed")
        return
    
    token = response.json().get('access_token')
    headers = {'Authorization': f'Bearer {token}'}
    
    # Test GET vendors
    response = requests.get(f"{BASE_URL}/vendors", headers=headers)
    print(f"GET vendors: {response.status_code}")
    if response.status_code == 200:
        vendors = response.json()
        print(f"Found {len(vendors)} vendors")
        if vendors:
            print(f"First vendor: {vendors[0].get('name')} (GST: {vendors[0].get('gst_number')})")
    
    # Test CREATE vendor with unique GST
    unique_id = int(time.time()) + random.randint(1000, 9999)
    vendor_data = {
        "name": f"Test Vendor {unique_id}",
        "gst_number": f"27AAAAA{unique_id % 100000:05d}A1Z5",
        "contact_number": "+91-9876543210",
        "address": "123 Test Street, Test City"
    }
    
    response = requests.post(f"{BASE_URL}/vendors", json=vendor_data, headers=headers)
    print(f"CREATE vendor: {response.status_code}")
    
    if response.status_code in [200, 201]:
        vendor = response.json()
        vendor_id = vendor.get('id')
        print(f"Created vendor ID: {vendor_id}")
        
        # Test READ
        response = requests.get(f"{BASE_URL}/vendors/{vendor_id}", headers=headers)
        print(f"READ vendor: {response.status_code}")
        
        # Test UPDATE
        update_data = {"name": f"Updated Vendor {unique_id}"}
        response = requests.put(f"{BASE_URL}/vendors/{vendor_id}", json=update_data, headers=headers)
        print(f"UPDATE vendor: {response.status_code}")
        
        # Test DELETE
        response = requests.delete(f"{BASE_URL}/vendors/{vendor_id}", headers=headers)
        print(f"DELETE vendor: {response.status_code}")
        
    else:
        print(f"Error: {response.text[:200]}")

if __name__ == "__main__":
    test()
