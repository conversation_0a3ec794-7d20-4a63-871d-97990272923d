import requests
import json

try:
    # Login
    login_response = requests.post('http://localhost:8000/api/auth/login',
        json={'email': '<EMAIL>', 'password': '<PERSON><PERSON>@109'})
    
    if login_response.status_code == 200:
        token = login_response.json()['access_token']
        
        # Get diamonds with pagination
        diamonds_response = requests.get('http://localhost:8000/api/diamonds?page=1&limit=5', 
            headers={'Authorization': f'Bearer {token}'})
        
        if diamonds_response.status_code == 200:
            diamonds_data = diamonds_response.json()
            print('Total diamonds:', diamonds_data.get('total', 0))
            print('Current page:', diamonds_data.get('page', 1))
            print('Per page:', diamonds_data.get('per_page', 20))
            print('Total pages:', diamonds_data.get('pages', 1))
            print()
            
            if diamonds_data.get('data'):
                print('Sample diamonds:')
                for i, diamond in enumerate(diamonds_data['data'][:3]):
                    print(f"{i+1}. ID: {diamond.get('id')}, Shape: {diamond.get('shape')}, Carat: {diamond.get('carat')}, Color: {diamond.get('color')}, Clarity: {diamond.get('clarity')}, Status: {diamond.get('status')}, Price: ${diamond.get('retail_price', 'N/A')}")
            else:
                print('No diamonds found')
        else:
            print('Failed to get diamonds:', diamonds_response.status_code)
            print(diamonds_response.text)
    else:
        print('Login failed:', login_response.status_code)
        print(login_response.text)
        
except Exception as e:
    print('Error:', e)
