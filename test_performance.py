#!/usr/bin/env python3
"""
Test API performance
"""

import requests
import time

BASE_URL = "http://localhost:8000/api"

def test_performance():
    # Login
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Test API performance multiple times
    times = []
    for i in range(5):
        start_time = time.time()
        response = session.get(f"{BASE_URL}/diamonds")
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # ms
        times.append(response_time)
        print(f"Test {i+1}: {response_time:.2f}ms")
    
    avg_time = sum(times) / len(times)
    print(f"\nAverage response time: {avg_time:.2f}ms")
    
    if avg_time < 500:
        print("🏆 Performance: Excellent")
    elif avg_time < 1000:
        print("✅ Performance: Good")
    elif avg_time < 2000:
        print("⚠️  Performance: Acceptable")
    else:
        print("❌ Performance: Poor")

if __name__ == "__main__":
    test_performance()
