{"version": "2.0.0", "tasks": [{"label": "Run Backend Server", "type": "shell", "command": "cd admin_backend && python run.py", "group": "build", "isBackground": true, "problemMatcher": []}, {"label": "Run Frontend Dev Server", "type": "shell", "command": "cd admin_front && npm run dev", "group": "build", "isBackground": true, "problemMatcher": []}, {"label": "Run Both Servers", "dependsOn": ["Run Backend Server", "Run Frontend Dev Server"], "group": {"kind": "build", "isDefault": true}, "problemMatcher": []}]}