import React from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQuery } from '@tanstack/react-query';
import { api } from '../../lib/api';
import Input from '../../components/ui/Input';
import Select from '../../components/ui/Select';
import Button from '../../components/ui/Button';
import toast from 'react-hot-toast';

interface SalesFormProps {
  onSuccess: () => void;
}

interface SalesFormData {
  invoice_no: string;
  customer_name: string;
  sale_date: string;
  total_amount: number;
  payment_status: 'paid' | 'unpaid';
  jewelry_id: string;
}

const SalesForm: React.FC<SalesFormProps> = ({ onSuccess }) => {
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<SalesFormData>({
    defaultValues: {
      sale_date: new Date().toISOString().split('T')[0],
      payment_status: 'unpaid'
    }
  });

  const { data: jewelry } = useQuery({
    queryKey: ['jewelry', { status: 'in_stock' }],
    queryFn: async () => {
      const response = await api.jewelry.list({ status: 'in_stock' });
      return response?.data || [];
    }
  });

  const mutation = useMutation({
    mutationFn: async (data: SalesFormData) => {
      await api.sales.create(data);
    },
    onSuccess: () => {
      toast.success('Sale recorded successfully');
      onSuccess();
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to record sale';
      toast.error(message);
    }
  });

  const onSubmit = (data: SalesFormData) => {
    mutation.mutate(data);
  };

  const jewelryOptions = Array.isArray(jewelry) ? jewelry.map((item: any) => ({
    value: item.id,
    label: `${item.name} - ${item.design_code}`
  })) : [];

  const paymentStatusOptions = [
    { value: 'paid', label: 'Paid' },
    { value: 'unpaid', label: 'Unpaid' }
  ];

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label="Invoice Number"
          type="text"
          required
          {...register('invoice_no', { 
            required: 'Invoice number is required',
            pattern: {
              value: /^[A-Z0-9-]+$/,
              message: 'Invoice number should contain only uppercase letters, numbers, and hyphens'
            }
          })}
          error={errors.invoice_no?.message}
          placeholder="INV-2025-001"
        />

        <Input
          label="Customer Name"
          type="text"
          required
          {...register('customer_name', { 
            required: 'Customer name is required',
            minLength: { value: 2, message: 'Customer name must be at least 2 characters' }
          })}
          error={errors.customer_name?.message}
        />

        <Input
          label="Sale Date"
          type="date"
          required
          {...register('sale_date', { required: 'Sale date is required' })}
          error={errors.sale_date?.message}
        />

        <Input
          label="Total Amount (₹)"
          type="number"
          step="0.01"
          required
          {...register('total_amount', { 
            required: 'Total amount is required',
            min: { value: 0.01, message: 'Amount must be greater than 0' }
          })}
          error={errors.total_amount?.message}
        />

        <Select
          label="Payment Status"
          options={paymentStatusOptions}
          required
          {...register('payment_status', { required: 'Payment status is required' })}
          error={errors.payment_status?.message}
        />

        <Select
          label="Jewelry Item"
          options={jewelryOptions}
          required
          {...register('jewelry_id', { required: 'Jewelry item is required' })}
          error={errors.jewelry_id?.message}
        />
      </div>

      <div className="bg-blue-50 p-4 rounded-lg">
        <p className="text-sm text-blue-800">
          <strong>Note:</strong> Recording this sale will automatically mark the selected jewelry item as sold.
        </p>
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          type="submit"
          isLoading={mutation.isPending}
        >
          Record Sale
        </Button>
      </div>
    </form>
  );
};

export default SalesForm;