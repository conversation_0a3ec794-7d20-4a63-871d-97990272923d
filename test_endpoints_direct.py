#!/usr/bin/env python3
"""
Test endpoints directly to identify issues
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_endpoints():
    print("🔍 Testing Endpoints Directly")
    print("=" * 50)
    
    # Test endpoints without authentication first
    endpoints = [
        ("/auth/login", "POST", {"email": "<EMAIL>", "password": "<PERSON><PERSON>@109"}),
        ("/diamonds", "GET", None),
        ("/vendors", "GET", None),
        ("/manufacturing", "GET", None),
        ("/jewelry", "GET", None),
        ("/sales", "GET", None),
        ("/dashboard", "GET", None),
    ]
    
    token = None
    
    for endpoint, method, data in endpoints:
        print(f"\n{method} {endpoint}")
        try:
            if method == "POST":
                if endpoint == "/auth/login":
                    response = requests.post(f"{BASE_URL}{endpoint}", json=data)
                    print(f"   Status: {response.status_code}")
                    if response.status_code == 200:
                        result = response.json()
                        token = result.get('access_token')
                        print(f"   Token: {token[:50]}..." if token else "   No token")
                    else:
                        print(f"   Response: {response.text}")
                else:
                    headers = {'Authorization': f'Bearer {token}'} if token else {}
                    response = requests.post(f"{BASE_URL}{endpoint}", json=data, headers=headers)
                    print(f"   Status: {response.status_code}")
                    print(f"   Response: {response.text[:200]}...")
            else:  # GET
                headers = {'Authorization': f'Bearer {token}'} if token else {}
                response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if isinstance(result, list):
                            print(f"   Found {len(result)} items")
                        elif isinstance(result, dict):
                            if 'data' in result:
                                print(f"   Found {len(result['data'])} items in data")
                            else:
                                print(f"   Response keys: {list(result.keys())}")
                    except:
                        print(f"   Response: {response.text[:200]}...")
                else:
                    print(f"   Response: {response.text[:200]}...")
                    
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")

if __name__ == "__main__":
    test_endpoints()
