import React from 'react';
import { Sale } from '../../types';
import Card from '../../components/ui/Card';

interface SalesDetailsProps {
  sale: Sale;
}

const SalesDetails: React.FC<SalesDetailsProps> = ({ sale }) => {
  return (
    <div className="space-y-6">
      {/* Sale Information */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Sale Information</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <span className="text-sm text-gray-500">Invoice Number:</span>
            <p className="font-medium">{sale.invoice_no}</p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Customer Name:</span>
            <p className="font-medium">{sale.customer_name}</p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Sale Date:</span>
            <p className="font-medium">{sale.sale_date ? new Date(sale.sale_date).toLocaleDateString() : 'N/A'}</p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Total Amount:</span>
            <p className="font-medium text-lg">
              ₹{sale.total_amount.toLocaleString()}
            </p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Payment Status:</span>
            <p className={`font-medium ${
              sale.payment_status === 'paid' ? 'text-green-600' : 'text-red-600'
            }`}>
              {sale.payment_status.charAt(0).toUpperCase() + sale.payment_status.slice(1)}
            </p>
          </div>
          <div>
            <span className="text-sm text-gray-500">Created:</span>
            <p className="font-medium">{sale.created_at ? new Date(sale.created_at).toLocaleDateString() : 'N/A'}</p>
          </div>
        </div>
      </Card>

      {/* Jewelry Information */}
      {sale.jewelry && (
        <Card>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Jewelry Item Sold</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-sm text-gray-500">Name:</span>
              <p className="font-medium">{sale.jewelry.name}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Design Code:</span>
              <p className="font-medium">{sale.jewelry.design_code}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Vendor:</span>
              <p className="font-medium">{sale.jewelry.vendor?.name || 'N/A'}</p>
            </div>
            <div>
              <span className="text-sm text-gray-500">Gross Weight:</span>
              <p className="font-medium">{sale.jewelry.gross_weight}g</p>
            </div>
          </div>

          {/* Diamonds Used */}
          <div className="space-y-2">
            {sale.jewelry.diamonds?.map((item, idx) => (
              <div key={idx} className="text-sm text-gray-700">
                <strong>{item.diamond?.shape || 'Unknown Shape'}:</strong> {item.diamond?.certificate_no || 'No Certificate'} — {item.quantity} pcs
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default SalesDetails;