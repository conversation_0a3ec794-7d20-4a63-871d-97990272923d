#!/usr/bin/env python3
"""
Test the shapes API to verify frontend compatibility after fixes
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_shapes_frontend_compatibility():
    print("🔧 Testing Shapes API Frontend Compatibility")
    print("=" * 55)
    
    # Step 1: Login
    print("\n1. Authenticating...")
    login_response = requests.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        return
    
    token = login_response.json().get('access_token')
    headers = {'Authorization': f'Bearer {token}'}
    print("✅ Authentication successful")
    
    # Step 2: Test shapes endpoint
    print("\n2. Testing shapes endpoint...")
    shapes_response = requests.get(f"{BASE_URL}/diamonds/shapes", headers=headers)
    print(f"   Status: {shapes_response.status_code}")
    
    if shapes_response.status_code == 200:
        shapes_data = shapes_response.json()
        print(f"   ✅ Shapes loaded successfully!")
        print(f"   📊 Response type: {type(shapes_data)}")
        print(f"   📊 Number of shapes: {len(shapes_data)}")
        
        # Verify the structure
        if isinstance(shapes_data, list) and len(shapes_data) > 0:
            first_shape = shapes_data[0]
            print(f"   📋 First shape structure: {first_shape}")
            
            # Check if it has the expected fields
            if 'id' in first_shape and 'name' in first_shape:
                print("   ✅ Shape structure is correct (has id and name)")
                
                # Show sample shapes for frontend
                print(f"   📋 Sample shapes for frontend:")
                for i, shape in enumerate(shapes_data[:5]):
                    frontend_format = {
                        "value": str(shape['id']),
                        "label": shape['name']
                    }
                    print(f"      {i+1}. {frontend_format}")
                
                print(f"\n   🎯 Frontend should receive this data structure:")
                print(f"      - Direct array (not wrapped in 'data' property)")
                print(f"      - Each shape: {{'id': number, 'name': string}}")
                print(f"      - Frontend maps to: {{'value': string, 'label': string}}")
                
            else:
                print("   ❌ Shape structure is incorrect")
        else:
            print("   ❌ Shapes data is not a valid array")
    else:
        print(f"   ❌ Failed to load shapes: {shapes_response.text}")
        return
    
    # Step 3: Test shape creation
    print("\n3. Testing shape creation...")
    test_shape = {"name": f"TestShape_{int(requests.get('http://worldtimeapi.org/api/timezone/UTC').json()['unixtime']) % 10000}"}
    create_response = requests.post(f"{BASE_URL}/diamonds/shapes", 
                                  json=test_shape, 
                                  headers=headers)
    print(f"   Status: {create_response.status_code}")
    
    if create_response.status_code in [200, 201]:
        created_shape = create_response.json()
        print(f"   ✅ Shape creation successful!")
        print(f"   📋 Created shape: {created_shape}")
        
        # Clean up - delete the test shape
        if 'id' in created_shape:
            delete_response = requests.delete(f"{BASE_URL}/diamonds/shapes/{created_shape['id']}", headers=headers)
            if delete_response.status_code in [200, 204]:
                print("   🧹 Test shape cleaned up successfully")
    else:
        print(f"   ❌ Shape creation failed: {create_response.text}")
    
    print("\n🎯 Summary:")
    print("   ✅ Shapes API endpoint: /api/diamonds/shapes")
    print("   ✅ Returns direct array (not wrapped in data property)")
    print("   ✅ Each shape has id and name fields")
    print("   ✅ Frontend should now load shapes correctly")
    print("\n   📝 Frontend fixes applied:")
    print("   - Updated api.shapes.list() to return response directly")
    print("   - Removed unnecessary local state in DiamondForm")
    print("   - Fixed infinite re-render issue")
    print("   - Updated DiamondList to handle direct array response")

if __name__ == "__main__":
    test_shapes_frontend_compatibility()
