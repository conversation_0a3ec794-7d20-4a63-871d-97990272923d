# Inventory & Sales Admin Panel

A production-ready React admin panel for managing diamond inventory, vendors, manufacturing, jewelry, and sales operations.

## 🚀 Features

- **Dashboard**: Real-time business metrics and analytics
- **Diamond Inventory**: Complete CRUD operations with advanced filtering
- **Vendor Management**: Supplier information and contact management
- **Manufacturing**: Track production orders and timelines
- **Jewelry Catalog**: Product management with image support
- **Sales Tracking**: Order management and payment status
- **Authentication**: JWT-based secure authentication
- **Responsive Design**: Modern UI built with Tailwind CSS

## 🛠️ Technology Stack

- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: React Query (TanStack Query)
- **Forms**: React Hook Form
- **HTTP Client**: Axios
- **Icons**: Lucide React
- **Notifications**: React Hot Toast
- **Routing**: React Router DOM

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd project
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your configuration:
   ```bash
   VITE_API_BASE_URL=http://127.0.0.1:8000/api
   VITE_APP_ENV=production
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

5. **Build for production**
   ```bash
   npm run build
   ```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `VITE_API_BASE_URL` | Backend API base URL | `http://127.0.0.1:8000/api` |
| `VITE_APP_ENV` | Application environment | `development` |
| `VITE_DEBUG_API` | Enable API debugging | `false` |

### Backend Requirements

The application expects a REST API backend with the following endpoints:

#### Authentication
- `POST /auth/login` - User login
- `GET /auth/me` - Get current user
- `POST /auth/logout` - User logout
- `POST /auth/refresh` - Refresh JWT token

#### Dashboard
- `GET /dashboard/summary` - Business metrics summary
- `GET /dashboard/sales` - Sales statistics
- `GET /dashboard/stock` - Stock levels

#### Resources
- `GET|POST /diamonds/` - List/Create diamonds
- `GET|PUT|DELETE /diamonds/{id}/` - Get/Update/Delete diamond
- `GET|POST /vendors/` - List/Create vendors
- `GET|PUT|DELETE /vendors/{id}/` - Get/Update/Delete vendor
- `GET|POST /manufacturing/` - List/Create manufacturing orders
- `GET|POST /jewelry/` - List/Create jewelry items
- `GET|POST /sales/` - List/Create sales

## 🏗️ Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI components (Button, Input, etc.)
│   ├── layout/         # Layout components (Sidebar, TopNav)
│   └── DataStates.tsx  # Loading, Error, Empty states
├── contexts/           # React contexts
│   └── AuthContext.tsx # Authentication context
├── lib/               # Utilities and services
│   └── api.ts         # Production API service
├── pages/             # Page components
│   ├── auth/          # Authentication pages
│   ├── diamonds/      # Diamond management
│   ├── vendors/       # Vendor management
│   ├── manufacturing/ # Manufacturing management
│   ├── jewelry/       # Jewelry management
│   └── sales/         # Sales management
├── types/             # TypeScript type definitions
│   └── index.ts       # Application types
├── App.tsx            # Main application component
└── main.tsx           # Application entry point
```

## 🔐 Authentication

The application uses JWT-based authentication with automatic token refresh. Only users with admin privileges can access the system.

### Login Process
1. User submits credentials
2. Backend validates and returns JWT tokens
3. Tokens are stored in localStorage
4. Access token is attached to all API requests
5. Refresh token automatically renews expired access tokens

## 📊 API Service

The production API service (`src/lib/api.ts`) provides:

- **Automatic Authentication**: JWT tokens are attached to all requests
- **Token Refresh**: Automatic renewal of expired tokens
- **Error Handling**: Centralized error handling with user notifications
- **Request Logging**: Development-time request logging
- **Type Safety**: Full TypeScript support
- **Resource Methods**: Dedicated methods for each API resource

### Usage Example

```typescript
import { api } from '../lib/api';

// Using resource-specific methods
const diamonds = await api.diamonds.list({ search: 'round', status: 'in_stock' });
const diamond = await api.diamonds.create(diamondData);
const updatedDiamond = await api.diamonds.update(id, updateData);

// Using generic HTTP methods
const response = await api.get('/custom-endpoint');
const result = await api.post('/custom-endpoint', data);
```

## 🎨 UI Components

### Core Components
- **Button**: Primary, secondary, and danger variants
- **Input**: Text, email, number, and date inputs
- **Select**: Dropdown selection with options
- **Card**: Content container with consistent styling
- **Modal**: Overlay dialog for forms and confirmations

### Data States
- **LoadingState**: Spinner with customizable message
- **ErrorState**: Error display with retry functionality
- **EmptyState**: Empty data placeholder with actions

## 🚦 Error Handling

The application implements comprehensive error handling:

1. **API Errors**: Automatic error toasts for failed requests
2. **Authentication Errors**: Automatic logout on 401 responses
3. **Form Validation**: Real-time form validation with error messages
4. **Network Errors**: Graceful handling of network failures
5. **Component Errors**: Error boundaries for component failures

## 🔍 Development

### Code Quality
- TypeScript for type safety
- ESLint for code linting
- Consistent code formatting
- Component-based architecture

### Best Practices
- Separation of concerns
- Reusable components
- Centralized state management
- Error boundary implementation
- Responsive design principles

## 📋 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🔧 Deployment

### Production Build
```bash
npm run build
```

The build outputs to the `dist/` directory and is ready for deployment to any static hosting service.

### Environment Setup
Ensure the following for production:
1. Set `VITE_API_BASE_URL` to your production API URL
2. Configure CORS on your backend for the frontend domain
3. Set up HTTPS for both frontend and backend
4. Configure proper error monitoring

## 📚 API Documentation

For detailed API documentation including request/response schemas, see `DEVELOPMENT_GUIDE.md`.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
