from flask_restx import Api
from flask import Blueprint

# Create blueprint
api_bp = Blueprint('api', __name__)

# Initialize API
api = Api(
    api_bp,
    version='1.0',
    title='Flask Auth API',
    description='A secure authentication API with role-based access control',
    doc='/docs',
    security='Bearer Auth',
    authorizations={
        'Bearer Auth': {
            'type': 'apiKey',
            'in': 'header',
            'name': 'Authorization',
            'description': 'Type in the *Value* input box: Bearer {your JWT token}'
        }
    }
)

# Import and register namespaces
from app.api.auth import auth_ns
from app.api.users import users_ns
from .diamond import diamond_ns
from .diamond_images import diamond_images_ns
from .vendor import vendor_ns
from .manufacturing import manufacturing_ns
from .jewelry import jewelry_ns
from .sale import sale_ns
from .dashboard import dashboard_ns
from .upload import upload_ns

api.add_namespace(auth_ns)
api.add_namespace(users_ns)
api.add_namespace(diamond_ns)
api.add_namespace(diamond_images_ns)
api.add_namespace(vendor_ns)
api.add_namespace(manufacturing_ns)
api.add_namespace(jewelry_ns)
api.add_namespace(sale_ns)
api.add_namespace(dashboard_ns)
api.add_namespace(upload_ns)