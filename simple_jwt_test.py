#!/usr/bin/env python3
"""
Simple JWT test
"""

import requests

BASE_URL = "http://localhost:8000/api"

def test_jwt():
    print("Testing JWT...")
    
    # Login
    login_data = {"email": "<EMAIL>", "password": "<PERSON><PERSON>@109"}
    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    print(f"Login: {response.status_code}")
    
    if response.status_code == 200:
        token = response.json().get('access_token')
        print(f"Token length: {len(token)}")
        
        # Test protected endpoint
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f"{BASE_URL}/diamonds", headers=headers)
        print(f"Diamonds: {response.status_code}")
        print(f"Response: {response.text[:100]}")
        
        # Test dashboard
        response = requests.get(f"{BASE_URL}/dashboard", headers=headers)
        print(f"Dashboard: {response.status_code}")
        print(f"Response: {response.text[:100]}")

if __name__ == "__main__":
    test_jwt()
