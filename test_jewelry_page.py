#!/usr/bin/env python3
"""
Test jewelry page functionality
"""

import requests
import time
from datetime import date

BASE_URL = "http://localhost:8000/api"
LOGIN_CREDENTIALS = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

def test_jewelry_functionality():
    print("💍 JEWELRY PAGE FUNCTIONALITY TEST")
    print("=" * 40)
    
    # Authenticate
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json=LOGIN_CREDENTIALS)
    if response.status_code != 200:
        print("❌ Authentication failed")
        return False
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Test GET jewelry
    print("\n1. Testing GET jewelry...")
    response = session.get(f"{BASE_URL}/jewelry")
    if response.status_code == 200:
        jewelry_items = response.json()
        print(f"✅ GET jewelry: Found {len(jewelry_items)} items")
    else:
        print(f"❌ GET jewelry failed: {response.status_code}")
        return False
    
    # Get or create a vendor for testing
    print("\n2. Setting up test vendor...")
    response = session.get(f"{BASE_URL}/vendors")
    if response.status_code == 200:
        vendors = response.json()
        if vendors:
            vendor_id = vendors[0]['id']
            print(f"✅ Using existing vendor: ID {vendor_id}")
        else:
            # Create a vendor
            unique_id = int(time.time())
            vendor_data = {
                "name": f"Jewelry Test Vendor {unique_id}",
                "gst_number": f"29AAAAA{unique_id % 100000:05d}A1Z5",
                "contact_number": "+91-9876543210",
                "address": "Test Jewelry Address"
            }
            response = session.post(f"{BASE_URL}/vendors", json=vendor_data)
            if response.status_code in [200, 201]:
                vendor = response.json()
                vendor_id = vendor.get('id')
                print(f"✅ Created test vendor: ID {vendor_id}")
            else:
                print(f"❌ Failed to create vendor: {response.status_code}")
                return False
    else:
        print(f"❌ Failed to get vendors: {response.status_code}")
        return False
    
    # Test CREATE jewelry
    print("\n3. Testing CREATE jewelry...")
    unique_id = int(time.time())
    jewelry_data = {
        "name": f"Test Ring {unique_id}",
        "design_code": f"TR{unique_id}",
        "vendor_id": vendor_id,
        "category": "Ring",
        "subcategory": "Engagement",
        "collection": "Classic",
        "style": "Solitaire",
        "gross_weight": 5.5,
        "net_weight": 4.8,
        "metal_type": "Gold",
        "metal_purity": "18K",
        "metal_color": "Yellow",
        "cost_price": 25000,
        "retail_price": 40000,
        "making_charges": 5000,
        "size": "7",
        "received_date": date.today().isoformat(),
        "status": "in_stock",
        "location": "Display Case A",
        "description": "Beautiful test engagement ring"
    }
    
    response = session.post(f"{BASE_URL}/jewelry", json=jewelry_data)
    if response.status_code in [200, 201]:
        jewelry = response.json()
        jewelry_id = jewelry.get('id')
        print(f"✅ CREATE jewelry: ID {jewelry_id}")
        
        # Check enhanced fields
        enhanced_fields = ['category', 'subcategory', 'metal_purity', 'making_charges', 'design_code']
        found_fields = [f for f in enhanced_fields if jewelry.get(f) is not None]
        print(f"✅ Enhanced fields: {found_fields}")
        
        # Check profit margin calculation
        profit_margin = jewelry.get('profit_margin')
        if profit_margin is not None:
            total_cost = 25000 + 5000  # cost_price + making_charges
            expected_margin = ((40000 - total_cost) / 40000) * 100
            print(f"✅ Profit margin: {profit_margin:.2f}% (expected: {expected_margin:.2f}%)")
        else:
            print("⚠️  Profit margin not calculated")
        
    else:
        print(f"❌ CREATE jewelry failed: {response.status_code}")
        print(f"Error: {response.text[:200]}")
        return False
    
    # Test READ jewelry
    print("\n4. Testing READ jewelry...")
    response = session.get(f"{BASE_URL}/jewelry/{jewelry_id}")
    if response.status_code == 200:
        print(f"✅ READ jewelry: Retrieved successfully")
    else:
        print(f"❌ READ jewelry failed: {response.status_code}")
    
    # Test UPDATE jewelry
    print("\n5. Testing UPDATE jewelry...")
    update_data = {
        "name": f"Updated Ring {unique_id}",
        "cost_price": 28000,
        "retail_price": 45000,
        "status": "sold",
        "notes": "Updated test jewelry"
    }
    
    response = session.put(f"{BASE_URL}/jewelry/{jewelry_id}", json=update_data)
    if response.status_code == 200:
        updated_jewelry = response.json()
        print(f"✅ UPDATE jewelry: Updated successfully")
        print(f"   Status: {updated_jewelry.get('status')}")
        
        # Check updated profit margin
        new_margin = updated_jewelry.get('profit_margin')
        if new_margin is not None:
            total_cost = 28000 + 5000  # updated cost_price + making_charges
            expected_margin = ((45000 - total_cost) / 45000) * 100
            print(f"✅ Updated profit margin: {new_margin:.2f}% (expected: {expected_margin:.2f}%)")
    else:
        print(f"❌ UPDATE jewelry failed: {response.status_code}")
    
    # Test jewelry filtering
    print("\n6. Testing jewelry filtering...")
    filters = [
        {"category": "Ring", "name": "Category filter"},
        {"status": "sold", "name": "Status filter"},
        {"search": "test", "name": "Search filter"}
    ]
    
    for filter_data in filters:
        filter_name = filter_data.pop('name')
        response = session.get(f"{BASE_URL}/jewelry", params=filter_data)
        if response.status_code == 200:
            filtered_items = response.json()
            print(f"   ✅ {filter_name}: Found {len(filtered_items)} items")
        else:
            print(f"   ❌ {filter_name} failed: {response.status_code}")
    
    # Test jewelry analytics
    print("\n7. Testing jewelry analytics...")
    response = session.get(f"{BASE_URL}/jewelry/analytics")
    if response.status_code == 200:
        analytics = response.json()
        print(f"✅ Jewelry analytics: Retrieved successfully")
        print(f"   Keys: {list(analytics.keys())[:5]}")
    else:
        print(f"❌ Jewelry analytics failed: {response.status_code}")
    
    # Test DELETE jewelry
    print("\n8. Testing DELETE jewelry...")
    response = session.delete(f"{BASE_URL}/jewelry/{jewelry_id}")
    if response.status_code in [200, 204]:
        print(f"✅ DELETE jewelry: Deleted successfully")
    else:
        print(f"❌ DELETE jewelry failed: {response.status_code}")
    
    print("\n🎯 Jewelry functionality test complete!")
    return True

if __name__ == "__main__":
    success = test_jewelry_functionality()
    if success:
        print("✅ All jewelry functionality is working correctly!")
    else:
        print("❌ Some jewelry functionality needs attention.")
