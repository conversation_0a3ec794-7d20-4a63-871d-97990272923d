#!/usr/bin/env python3
"""
Debug script to test diamond creation with detailed error reporting
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://127.0.0.1:8000/api"
TEST_USER = {
    "email": "<EMAIL>",
    "password": "<PERSON><PERSON>@109"
}

def debug_diamond_creation():
    """Debug diamond creation with detailed logging"""
    session = requests.Session()
    
    # Login first
    print("🔐 Logging in...")
    try:
        login_response = session.post(f"{BASE_URL}/auth/login", json=TEST_USER)
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code}")
            return False
        
        token_data = login_response.json()
        token = token_data.get('access_token')
        session.headers.update({'Authorization': f'Bearer {token}'})
        print("✅ Login successful")
        
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Check available shapes first
    print("\n🔍 Checking available shapes...")
    try:
        shapes_response = session.get(f"{BASE_URL}/shapes")
        if shapes_response.status_code == 200:
            shapes = shapes_response.json()
            print(f"✅ Found {len(shapes)} shapes:")
            for shape in shapes[:5]:  # Show first 5
                print(f"   - ID: {shape['id']}, Name: {shape['name']}")
        else:
            print(f"❌ Failed to get shapes: {shapes_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error getting shapes: {e}")
        return False
    
    # Test diamond creation with minimal required fields
    print("\n🧪 Testing diamond creation...")
    
    diamond_data = {
        "shape_id": 1,  # Round (should be first shape)
        "carat": 1.25,
        "color": "D",
        "clarity": "VVS1",
        "certificate_no": f"DEBUG{int(time.time())}",  # Unique certificate
        "certification_lab": "GIA",
        "quantity": 1,
        "status": "in_stock"
    }
    
    print(f"📋 Diamond data: {json.dumps(diamond_data, indent=2)}")
    
    try:
        response = session.post(f"{BASE_URL}/diamonds", json=diamond_data)
        
        print(f"\n📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"📊 Response Data: {json.dumps(response_data, indent=2)}")
        except:
            print(f"📊 Response Text: {response.text}")
        
        if response.status_code == 201:
            print("✅ Diamond creation successful!")
            return True
        else:
            print(f"❌ Diamond creation failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Diamond creation error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Debugging Diamond Creation...")
    print("=" * 50)
    
    success = debug_diamond_creation()
    
    if success:
        print("\n🎉 Diamond creation is working!")
    else:
        print("\n⚠️ Diamond creation failed. Check the backend logs for more details.")
