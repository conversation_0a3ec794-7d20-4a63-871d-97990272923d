#!/usr/bin/env python3
"""
Test manufacturing and vendor fixes
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_fixes():
    print("🔧 Testing Manufacturing and Vendor Fixes...")
    
    # Login first
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "Shivam@109"
    })
    
    if response.status_code != 200:
        print("❌ Login failed")
        return
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    print("✅ Authenticated")
    
    # Test manufacturing endpoint
    print("\n🏭 Testing Manufacturing API...")
    response = session.get(f"{BASE_URL}/manufacturing")
    if response.status_code == 200:
        data = response.json()
        print(f"  ✅ Manufacturing API: {type(data)}")
        if isinstance(data, list):
            print(f"    - {len(data)} manufacturing requests")
        elif isinstance(data, dict) and 'data' in data:
            print(f"    - {len(data['data'])} manufacturing requests (paginated)")
    else:
        print(f"  ❌ Manufacturing API: {response.status_code}")
    
    # Test vendor creation
    print("\n👥 Testing Vendor Creation...")
    test_vendor = {
        "name": "Test Vendor Company",
        "company_name": "Test Vendor Pvt Ltd",
        "contact_number": "+91-9876543210",
        "email": "<EMAIL>",
        "address": "123 Test Street, Test City",
        "city": "Mumbai",
        "state": "Maharashtra",
        "country": "India",
        "postal_code": "400001",
        "gst_number": f"27ABCDE1234F{hash('test') % 1000:03d}Z5",  # Generate unique GST
        "business_type": "Manufacturer",
        "status": "active",
        "is_verified": True,
        "credit_limit": 100000.0
    }
    
    response = session.post(f"{BASE_URL}/vendors", json=test_vendor)
    if response.status_code in [200, 201]:
        vendor_data = response.json()
        print(f"  ✅ Vendor created successfully: {vendor_data.get('name', 'Unknown')}")
        
        # Test vendor update
        vendor_id = vendor_data.get('id')
        if vendor_id:
            update_data = {"name": "Updated Test Vendor"}
            response = session.put(f"{BASE_URL}/vendors/{vendor_id}", json=update_data)
            if response.status_code == 200:
                print(f"  ✅ Vendor updated successfully")
            else:
                print(f"  ❌ Vendor update failed: {response.status_code}")
    else:
        print(f"  ❌ Vendor creation failed: {response.status_code}")
        try:
            error_data = response.json()
            print(f"    Error: {error_data}")
        except:
            print(f"    Raw error: {response.text[:200]}")
    
    # Test vendors list
    print("\n📋 Testing Vendors List...")
    response = session.get(f"{BASE_URL}/vendors")
    if response.status_code == 200:
        vendors = response.json()
        print(f"  ✅ Vendors list: {len(vendors)} vendors")
    else:
        print(f"  ❌ Vendors list failed: {response.status_code}")
    
    print("\n🎯 Fix Verification Complete!")

if __name__ == "__main__":
    test_fixes()
