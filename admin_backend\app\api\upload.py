from flask_restx import Namespace, Resource, fields, reqparse
from flask import request, current_app
from app.utils.decorators import token_required
from werkzeug.utils import secure_filename
import os

upload_ns = Namespace('upload', description='File upload operations', path='/upload')

upload_response_model = upload_ns.model('UploadResponse', {
    'image_path': fields.String
})

error_model = upload_ns.model('UploadError', {
    'status': fields.String(description='Error status'),
    'message': fields.String(description='Error message'),
    'status_code': fields.Integer(description='HTTP status code')
})

upload_parser = reqparse.RequestParser()
upload_parser.add_argument('image', location='files', type='FileStorage', required=True)

@upload_ns.route('/image')
class UploadImage(Resource):
    @upload_ns.doc('upload_image')
    @upload_ns.expect(upload_parser)
    @upload_ns.marshal_with(upload_response_model)
    @upload_ns.response(200, 'Image uploaded', upload_response_model)
    @upload_ns.response(400, 'No image uploaded', error_model)
    @upload_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def post(self):
        """Upload an image file"""
        try:
            if 'image' not in request.files:
                upload_ns.abort(400, 'No image uploaded')
            image = request.files['image']
            filename = secure_filename(image.filename)
            upload_folder = os.path.join(current_app.root_path, 'static', 'uploads')
            os.makedirs(upload_folder, exist_ok=True)
            path = os.path.join(upload_folder, filename)
            image.save(path)
            image_url = f'/static/uploads/{filename}'
            return {'image_path': image_url}, 200
        except Exception as e:
            upload_ns.abort(400, f'Failed to upload image: {str(e)}')
