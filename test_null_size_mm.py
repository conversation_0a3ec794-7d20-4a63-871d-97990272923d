#!/usr/bin/env python3
"""
Test null size_mm handling
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_null_size_mm():
    print("🔧 Testing Null size_mm Handling...")
    
    # Login
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    
    # Create diamond with proper certificate number
    test_diamond = {
        "shape_id": 1,
        "carat": 1.0,
        "color": "G",
        "clarity": "SI1",
        "certificate_no": "NULLTEST123",  # Proper format
        "vendor_id": 1,
        "purchase_date": "2025-07-21",
        "quantity": 1,
        "minimum_stock": 1,
        "status": "in_stock"
    }
    
    response = session.post(f"{BASE_URL}/diamonds", json=test_diamond)
    if response.status_code in [200, 201]:
        created_diamond = response.json()
        print(f"✅ Created diamond: {created_diamond['id']}")
        print(f"   size_mm: {created_diamond.get('size_mm', 'None')}")
        
        # Test the list endpoint
        response = session.get(f"{BASE_URL}/diamonds")
        if response.status_code == 200:
            print(f"✅ Diamond list API working")
        else:
            print(f"❌ Diamond list failed: {response.status_code}")
    else:
        try:
            error = response.json()
            print(f"❌ Creation failed: {error}")
        except:
            print(f"❌ Creation failed: {response.text}")

if __name__ == "__main__":
    test_null_size_mm()
