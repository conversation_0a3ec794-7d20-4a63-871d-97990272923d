import requests
from datetime import datetime

def test_user_registration(base_url):
    test_user = {
        "email": f"test_{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
        "password": "Test@123",
        "first_name": "Test",
        "last_name": "User"
    }
    # Successful registration
    r = requests.post(f"{base_url}/auth/register", json=test_user, timeout=10)
    assert r.status_code == 201 or r.status_code == 409
    # Duplicate registration
    r2 = requests.post(f"{base_url}/auth/register", json=test_user, timeout=10)
    assert r2.status_code == 409

def test_user_login(base_url):
    test_user = {
        "email": f"login_{datetime.now().strftime('%Y%m%d%H%M%S%f')}@example.com",
        "password": "Test1234!",
        "first_name": "API",
        "last_name": "Tester"
    }
    requests.post(f"{base_url}/auth/register", json=test_user, timeout=10)
    r = requests.post(f"{base_url}/auth/login", json={
        "email": test_user["email"],
        "password": test_user["password"]
    }, timeout=10)
    if r.status_code != 200:
        print("Login failed:", r.status_code, r.text)
    assert r.status_code == 200

def test_profile_retrieval(base_url):
    test_user = {
        "email": f"profile_{datetime.now().strftime('%Y%m%d%H%M%S%f')}@example.com",
        "password": "Test1234!",
        "first_name": "API",
        "last_name": "Tester"
    }
    requests.post(f"{base_url}/auth/register", json=test_user, timeout=10)
    r = requests.post(f"{base_url}/auth/login", json={
        "email": test_user["email"],
        "password": test_user["password"]
    }, timeout=10)
    access_token = r.json()["access_token"]
    headers = {"Authorization": f"Bearer {access_token}"}
    r2 = requests.get(f"{base_url}/auth/me", headers=headers, timeout=10)
    if r2.status_code != 200:
        print("Profile retrieval failed:", r2.status_code, r2.text)
    assert r2.status_code == 200

def test_token_refresh(base_url):
    test_user = {
        "email": f"refresh_{datetime.now().strftime('%Y%m%d%H%M%S%f')}@example.com",
        "password": "Test1234!",
        "first_name": "API",
        "last_name": "Tester"
    }
    requests.post(f"{base_url}/auth/register", json=test_user, timeout=10)
    r = requests.post(f"{base_url}/auth/login", json={
        "email": test_user["email"],
        "password": test_user["password"]
    }, timeout=10)
    refresh_token = r.json()["refresh_token"]
    headers = {"Authorization": f"Bearer {refresh_token}"}
    r2 = requests.post(f"{base_url}/auth/refresh", headers=headers, timeout=10)
    if r2.status_code != 200:
        print("Token refresh failed:", r2.status_code, r2.text)
    assert r2.status_code == 200

def test_user_logout(base_url):
    test_user = {
        "email": f"logout_{datetime.now().strftime('%Y%m%d%H%M%S%f')}@example.com",
        "password": "Test1234!",
        "first_name": "API",
        "last_name": "Tester"
    }
    requests.post(f"{base_url}/auth/register", json=test_user, timeout=10)
    r = requests.post(f"{base_url}/auth/login", json={
        "email": test_user["email"],
        "password": test_user["password"]
    }, timeout=10)
    access_token = r.json()["access_token"]
    headers = {"Authorization": f"Bearer {access_token}"}
    r2 = requests.post(f"{base_url}/auth/logout", headers=headers, timeout=10)
    if r2.status_code != 200:
        print("Logout failed:", r2.status_code, r2.text)
    assert r2.status_code == 200

def test_register_and_login(base_url):
    test_user = {
        "email": f"reglogin_{datetime.now().strftime('%Y%m%d%H%M%S%f')}@example.com",
        "password": "Test1234!",
        "first_name": "API",
        "last_name": "Tester"
    }
    r = requests.post(f"{base_url}/auth/register", json=test_user, timeout=10)
    if r.status_code not in (201, 409):
        print("Register failed:", r.status_code, r.text)
    assert r.status_code in (201, 409)
    r = requests.post(f"{base_url}/auth/login", json={
        "email": test_user["email"],
        "password": test_user["password"]
    }, timeout=10)
    if r.status_code != 200:
        print("Login after register failed:", r.status_code, r.text)
    assert r.status_code == 200