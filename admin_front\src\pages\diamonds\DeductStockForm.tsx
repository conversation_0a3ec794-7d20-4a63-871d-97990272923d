import React from 'react';
import { useForm } from 'react-hook-form';
import { useMutation } from '@tanstack/react-query';
import { api } from '../../lib/api';
import { Diamond } from '../../types';
import Input from '../../components/ui/Input';
import Button from '../../components/ui/Button';
import toast from 'react-hot-toast';

interface DeductStockFormProps {
  diamond: Diamond;
  onSuccess: () => void;
}

interface DeductStockFormData {
  quantity: number;
}

const DeductStockForm: React.FC<DeductStockFormProps> = ({ diamond, onSuccess }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<DeductStockFormData>();

  const quantityValue = watch('quantity');

  const mutation = useMutation({
    mutationFn: async (data: DeductStockFormData) => {
      await api.diamonds.deductStock(diamond.id, data.quantity);
    },
    onSuccess: () => {
      toast.success('Stock deducted successfully');
      onSuccess();
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || 'Failed to deduct stock';
      toast.error(message);
    }
  });

  const onSubmit = (data: DeductStockFormData) => {
    mutation.mutate(data);
  };

  return (
    <div className="space-y-6">
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-medium text-gray-900 mb-2">Diamond Details</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Shape:</span>
            <span className="ml-2 font-medium">{diamond.shape}</span>
          </div>
          <div>
            <span className="text-gray-500">Carat:</span>
            <span className="ml-2 font-medium">{diamond.carat} ct</span>
          </div>
          <div>
            <span className="text-gray-500">Current Stock:</span>
            <span className="ml-2 font-medium">{diamond.quantity}</span>
          </div>
          <div>
            <span className="text-gray-500">Certificate:</span>
            <span className="ml-2 font-medium">{diamond.certificate_no}</span>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Input
          label="Quantity to Deduct"
          type="number"
          min="1"
          max={diamond.quantity}
          required
          {...register('quantity', { 
            valueAsNumber: true,
            required: 'Quantity is required',
            min: { value: 1, message: 'Quantity must be at least 1' },
            max: { value: diamond.quantity, message: `Cannot exceed available stock (${diamond.quantity})` }
          })}
          error={errors.quantity?.message}
          helperText={`Available stock: ${diamond.quantity}`}
        />

        {quantityValue && (
          <div className="bg-blue-50 p-3 rounded-lg">
            <p className="text-sm text-blue-800">
              After deduction: <strong>{diamond.quantity - quantityValue}</strong> units remaining
            </p>
          </div>
        )}

        <div className="flex justify-end space-x-4">
          <Button
            type="submit"
            isLoading={mutation.isPending}
            variant="danger"
          >
            Deduct Stock
          </Button>
        </div>
      </form>
    </div>
  );
};

export default DeductStockForm;