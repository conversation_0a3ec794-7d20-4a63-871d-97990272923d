import os
from datetime import timedelta
from dotenv import load_dotenv

load_dotenv()

class Config:
    """Base configuration."""
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'dev-jwt-secret')
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=1)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    JWT_BLACKLIST_ENABLED = True
    JWT_BLACKLIST_TOKEN_CHECKS = ['access', 'refresh']
    ACCOUNT_LOCKOUT_DURATION = timedelta(minutes=int(os.getenv('ACCOUNT_LOCKOUT_DURATION_MINUTES', '30')))
    MAX_LOGIN_ATTEMPTS = int(os.getenv('MAX_LOGIN_ATTEMPTS', '5'))

class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True
    # Use SQLite if SQLITE_DATABASE_URL is set, otherwise fallback to DATABASE_URL
    SQLALCHEMY_DATABASE_URI = os.getenv('SQLITE_DATABASE_URL', os.getenv('DATABASE_URL', 'postgresql://localhost/flask_auth_dev'))

class TestingConfig(Config):
    """Testing configuration."""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'  # Use in-memory SQLite for testing
    PRESERVE_CONTEXT_ON_EXCEPTION = False
    JWT_SECRET_KEY = 'test-jwt-secret'  # Use a fixed secret key for testing

class ProductionConfig(Config):
    """Production configuration."""
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = os.getenv('PROD_DATABASE')
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(minutes=15)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=7)

config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}